FROM registry.kuainiujinke.com/cd_biz/biz-admin.base:7.4.33_1

COPY . /opt

RUN mkdir -p /data/logs/php7/php/ \
 && mv /opt/docker/nginx/* /data/nginx/conf \
 && mv /opt/docker/php/php.ini /etc/php.ini \
 && mv /opt/docker/php/php-fpm.conf /etc/php-fpm.conf \
 && mv /opt/docker/supervisor/supervisord.conf /etc/supervisord.conf \
 && mkdir -p /data/www/wwwroot/biz-admin \
 && mv /opt/* /data/www/wwwroot/biz-admin \
 && chown -R nginx.nginx /data/www/wwwroot/biz-admin /data/logs

EXPOSE 80
