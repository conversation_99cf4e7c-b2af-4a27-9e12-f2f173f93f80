<?php

use common\db\Connection;
use Predis\Client;
use Predis\Connection\Parameters;
use yii\caching\FileCache;
use yii\symfonymailer\Mailer;

return [
    'components' => [
        'db'         => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=************;dbname=qsq_erp',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbLog'      => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=************;dbname=qsq_log',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbHistory'  => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=*********;dbname=qsq_erp_history',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbCapital'  => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=************;dbname=capital',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbGBiz'     => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=*********;dbname=gbiz_dev',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbRBiz'     => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=*********;dbname=rbiz_dev',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbPaysvr'   => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=*********;dbname=payment_staging',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbAccount'  => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=*********;dbname=account',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbContract' => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=*********;dbname=contract',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbCmdb'     => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=*********;dbname=cmdb_v1',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'dbRbizHis'  => [
            'class'    => Connection::class,
            'dsn'      => 'mysql:host=*********;dbname=rbiz_history',
            'username' => 'root',
            'password' => '123456',
            'charset'  => 'utf8',
        ],
        'cache'      => [
            'class' => FileCache::class,
            //            'class' => PredisCache::class,
            // 'redis' => 'redis',
        ],
        'redis'      => function () {
            return new Client(new Parameters([
                'scheme'   => 'tcp',
                'host'     => '127.0.0.1',
                'port'     => 6379,
                'database' => 1,
            ]));
        },
        'log'        => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets'    => [],
        ],
        'mailer'     => [
            'class'            => Mailer::class,
            'viewPath'         => '@common/mail',
            'useFileTransport' => true,//这句一定有，false发送邮件，true只是生成邮件在runtime文件夹下，不发邮件
            'transport'        => [
                'dsn' => 'smtps://<EMAIL>:<EMAIL>:465',
            ],
        ],
    ],
];
