<?php

namespace console\controllers;

use kvmanager\models\KeyValue;
use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;
use yii\console\Controller;
use yii\console\ExitCode;

class TestController extends Controller
{
    public function actionIndex(): int
    {
        $expressionLanguage = new ExpressionLanguage();
        $expressionLanguage->addFunction(ExpressionFunction::fromPhp('date'));
        $expressionLanguage->addFunction(ExpressionFunction::fromPhp('strtotime'));
        // 分割字符串，判断第一部分是否大于0，第二部分参与计算
        $expression = "date('Ym', strtotime(self ~ '/01 +' ~ mob ~ ' month'))";

// 使用示例
        $result = $expressionLanguage->evaluate($expression, [
            'self' => '2025/05',
            'mob' => '1'
        ]);
        echo $result;
// 输出: <span class="text-success">1.8929 ✓</span><span class="text-muted"> - </span><span class="calculated">10</span>
        return ExitCode::OK;
    }

    /**
     * @param string $key
     * @param string $namespace
     * @param string $group
     *
     * @return int
     */
    public function actionCleanCache(string $key, string $namespace = 'biz', string $group = 'KV'): int
    {
        $model = KeyValue::findOne([
            'namespace' => $namespace,
            'group' => $group,
            'key' => $key,
        ]);

        if ($model) {
            $model->cleanCache();
        }

        return ExitCode::OK;
    }
}
