<?php

use capital\console\CapitalController;
use Codingheping\StatementComponent\console\MigrateController;
use common\components\CapitalComponent;
use datasource\console\GenerateController;
use dcs\console\CapitalAssetController;
use dcs\console\CapitalSettlementDetailController;
use dcs\console\DebtTransferServiceFeeController;
use dcs\console\DepositController;
use dcs\console\RefreshCapitalSettlementController;
use dcs\console\SftpController;
use dcs\console\WithholdResultController;
use Xlerr\SettlementFlow\Console\OrderSyncController;
use xlerr\task\console\TaskController;
use yii\console\ErrorHandler;

$params = array_merge(
    require(__DIR__ . '/../../common/config/params.php'),
    require(__DIR__ . '/../../common/config/params-local.php'),
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php')
);

return [
    'id' => 'app-console',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log', 'import'],
    'controllerNamespace' => 'console\controllers',
    'controllerMap' => [
        'task' => TaskController::class,
        'dashboard-data-generate' => GenerateController::class,
        'debt-transfer-service-fee' => DebtTransferServiceFeeController::class,
        'withhold-result' => WithholdResultController::class,
        'deposit' => DepositController::class,
        'capital-asset' => CapitalAssetController::class,
        'sftp' => SftpController::class,
        'capital-settlement-detail' => CapitalSettlementDetailController::class,
        'statement-migrate' => MigrateController::class,
        'refresh-capital-settlement' => RefreshCapitalSettlementController::class,
        'capital-deposit' => \capital\console\DepositController::class,
        'capital' => CapitalController::class,
        'order-sync' => [
            'class' => OrderSyncController::class,
            'capitalClient' => CapitalComponent::class,
        ],
    ],
    'components' => [
        'errorHandler' => [
            'class' => ErrorHandler::class,
        ],
    ],
    'params' => $params,
];
