<?php

namespace payment\controllers;

use Exception;
use kartik\depdrop\DepDropAction;
use payment\models\Bank;
use payment\models\ProductBankSupport;
use payment\models\ProductBankSupportSearch;
use payment\models\ProviderSignCompany;
use Yii;
use yii\base\UserException;
use yii\db\ActiveQuery;
use yii\helpers\ArrayHelper;
use yii\web\Controller;

/**
 * ProductBankSupportController implements the CRUD actions for ProductBankSupport model.
 */
class ProductBankSupportController extends Controller
{
    public function actions()
    {
        return [
            'provider-list' => [
                'class' => DepDropAction::class,
                'outputCallback' => function ($selectedId, $params) {
                    return ProviderSignCompany::find()
                        ->innerJoinWith(['provider'])
                        ->where([
                            'provider_sign_company_sign_company_code' => $selectedId,
                        ])
                        ->select([
                            'id' => 'provider.provider_code',
                            'name' => 'provider.provider_name',
                        ])
                        ->asArray()
                        ->all();
                },
            ],
        ];
    }

    /**
     * Lists all ProductBankSupport models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ProductBankSupportSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new ProductBankSupport model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return mixed
     */
    public function actionCreate()
    {
        if ($this->request->isPost) {
            $conditionAttr = [
                'product_bank_support_sign_company_code',
                'product_bank_support_provider_code',
                'product_bank_support_product_type',
            ];
            $condition = array_combine(
                $conditionAttr,
                array_map(fn($attr) => $this->request->post($attr), $conditionAttr)
            );
            $data = $this->request->post('table');
            $transaction = ProductBankSupport::getDb()->beginTransaction();
            try {
                foreach ($data as $bankCode => $info) {
                    $model = ProductBankSupport::find()->where($condition)->andWhere([
                        'product_bank_support_bank_code' => $bankCode,
                    ])->one() ?? new ProductBankSupport($condition);
                    // 判断值是否为0, 0表示默认填充的不需要入库的逻辑
                    if ($info['product_bank_support_status'] !== 'on' && $model->isNewRecord) {
                        $sumVal = array_sum(
                            ArrayHelper::filter($info, [
                                'product_bank_support_pay_once',
                                'product_bank_support_pay_daily',
                                'product_bank_support_pay_monthly',
                                'product_bank_support_fee',
                                'product_bank_support_fee_rate',
                                'product_bank_support_fee_min',
                                'product_bank_support_fee_max',
                                'product_bank_support_score',
                            ])
                        );
                        if ($sumVal === 0) {
                            continue;
                        }
                    }

                    if (!($model->load($info, '') && $model->save())) {
                        if ($model->hasErrors()) {
                            $error = implode('<br/>', array_merge(...array_values($model->errors)));
                        } else {
                            $error = '保存失败';
                        }
                        throw new UserException($error);
                    }
                }
                $transaction->commit();
                Yii::$app->session->addFlash('success', '保存成功');
            } catch (Exception $e) {
                $transaction->rollBack();
                Yii::$app->session->addFlash('error', $e->getMessage());
            }

            array_unshift($condition, 'create');

            return $this->redirect($condition);
        }

        $signCompanyList = ProviderSignCompany::find()
            ->joinWith('signCompany', false)
            ->select('sign_company_name')
            ->indexBy('provider_sign_company_sign_company_code')
            ->column();

        $config = ProductBankSupport::find()
            ->alias('pbs')
            ->joinWith([
                'bank' => function (ActiveQuery $query) {
                    $query->alias('b');
                },
            ], false)
            ->select([
                'product_bank_support_sign_company_code',
                'product_bank_support_provider_code',
                'product_bank_support_product_type',
                'product_bank_support_bank_code',
                'product_bank_support_pay_once',
                'product_bank_support_pay_daily',
                'product_bank_support_pay_monthly',
                'product_bank_support_fee',
                'product_bank_support_fee_rate',
                'product_bank_support_fee_min',
                'product_bank_support_fee_max',
                'product_bank_support_score',
                'product_bank_support_status',
                'bank_name',
            ])
            ->asArray()
            ->all();
        $config = ArrayHelper::index($config, 'product_bank_support_bank_code', [
            static function ($row) {
                return implode(':', [
                    $row['product_bank_support_sign_company_code'],
                    $row['product_bank_support_provider_code'],
                    $row['product_bank_support_product_type'],
                ]);
            },
        ]);

        $bankList = Bank::find()
            ->select([
                'id' => 'bank_code',
                'text' => 'bank_name',
            ])
            ->indexBy('id')
            ->asArray()
            ->all();

        $defaultParams = explode(':', array_key_first($config));

        $model = new ProductBankSupportSearch();
        [
            $model->product_bank_support_sign_company_code,
            $model->product_bank_support_provider_code,
            $model->product_bank_support_product_type,
        ] = [
            $this->request->get('product_bank_support_sign_company_code', $defaultParams[0]),
            $this->request->get('product_bank_support_provider_code', $defaultParams[1]),
            $this->request->get('product_bank_support_product_type', $defaultParams[2]),
        ];

        return $this->render('create', [
            'model' => $model,
            'signCompanyList' => $signCompanyList,
            'config' => $config,
            'bankList' => $bankList,
        ]);
    }
}
