<?php

namespace payment\controllers;

use payment\models\WithholdSearch;
use yii\web\Controller;

/**
 * WithholdController implements the CRUD actions for Withhold model.
 */
class WithholdController extends Controller
{
    /**
     * Lists all Withhold models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new WithholdSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
}
