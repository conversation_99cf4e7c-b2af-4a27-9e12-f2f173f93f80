<?php

namespace payment\controllers;

use dcs\components\PaymentComponent;
use payment\models\WithdrawReceiptSearch;
use Yii;
use yii\base\InvalidConfigException;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\Controller;

/**
 * WithdrawReceiptController implements the CRUD actions for WithdrawReceipt model.
 */
class WithdrawReceiptController extends Controller
{
    /**
     * Lists all WithdrawReceipt models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new WithdrawReceiptSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single WithdrawReceipt model.
     *
     * @return string
     * @throws InvalidConfigException
     */
    public function actionOrderQuery(): string
    {
        $client = PaymentComponent::instance();
        $client->transactionWithdrawQuery(
            ArrayHelper::filter($this->request->get(), [
                'merchant_name',
                'merchant_key',
            ])
        );

        return Html::tag(
            'pre',
            Html::tag(
                'code',
                Json::encode(
                    $client->getResponse(),
                    JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
                )
            )
        );
    }
}
