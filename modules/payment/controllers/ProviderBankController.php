<?php

namespace payment\controllers;

use Throwable;
use Yii;
use payment\models\ProviderBank;
use payment\models\ProviderBankSearch;
use yii\db\StaleObjectException;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

/**
 * ProviderBankController implements the CRUD actions for ProviderBank model.
 */
class ProviderBankController extends Controller
{
    /**
     * Lists all ProviderBank models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new ProviderBankSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new ProviderBank model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return string
     */
    public function actionCreate(): string
    {
        $model = new ProviderBank();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ProviderBank model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id Provider Bank ID
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionUpdate(int $id): string
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Finds the ProviderBank model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id Provider Bank ID
     *
     * @return ProviderBank the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): ProviderBank
    {
        if (($model = ProviderBank::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
