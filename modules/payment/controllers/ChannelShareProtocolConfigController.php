<?php

namespace payment\controllers;

use kartik\depdrop\DepDropAction;
use payment\models\Channel;
use payment\models\ChannelShareProtocolConfig;
use payment\models\ChannelShareProtocolConfigSearch;
use Throwable;
use yii\base\UserException;
use yii\db\StaleObjectException;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Request;
use yii\web\Response;

/**
 * ChannelShareProtocolConfigController implements the CRUD actions for ChannelShareProtocolConfig model.
 *
 * @property Request $request
 * @property Response $response
 */
class ChannelShareProtocolConfigController extends Controller
{
    /**
     * @inheritdoc
     */
    public function actions(): array
    {
        return [
            'channel-list-by-provider' => [
                'class' => DepDropAction::class,
                'outputCallback' => function ($providerCode) {
                    return Channel::find()->where([
                        'channel_provider_code' => $providerCode,
                        'channel_status' => 1,
                        'channel_provider_product_type' => 'protocol',
                    ])->select([
                        'id' => 'channel_name',
                        'name' => 'channel_name',
                    ])->asArray()->all();
                },
            ],
        ];
    }

    /**
     * Lists all ChannelShareProtocolConfig models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new ChannelShareProtocolConfigSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new ChannelShareProtocolConfig model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return string
     * @throws Throwable
     */
    public function actionCreate(): string
    {
        $model = new ChannelShareProtocolConfig();
        $model->mutual = true;

        if ($model->load($this->request->post()) && $model->validate()) {
            $result = ChannelShareProtocolConfig::getDb()->transaction(function () use ($model) {
                if ($model->mutual && !$model->hasMutual()) {
                    $mutualModel = clone $model;
                    [
                        $mutualModel->channel_share_protocol_config_channel_name,
                        $mutualModel->channel_share_protocol_config_share_channel_name,
                    ] = [
                        $mutualModel->channel_share_protocol_config_share_channel_name,
                        $mutualModel->channel_share_protocol_config_channel_name,
                    ];
                    if (!$mutualModel->insert()) {
                        throw new UserException('保存失败1');
                    }
                }

                if (!$model->insert()) {
                    throw new UserException('保存失败2');
                }

                return true;
            });

            if ($result) {
                return Html::script('window.top.reloadCurrentTab()');
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * @param int $id Channel Share Protocol Config ID
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function actionEnable(int $id): Response
    {
        $model = $this->findModel($id);
        $model->channel_share_protocol_config_status = ChannelShareProtocolConfig::STATUS_ENABLE;
        $model->update();

        return $this->redirect($this->request->referrer);
    }

    /**
     * @param int $id Channel Share Protocol Config ID
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function actionDisable(int $id): Response
    {
        $model = $this->findModel($id);
        $model->channel_share_protocol_config_status = ChannelShareProtocolConfig::STATUS_DISABLE;
        $model->update();

        return $this->redirect($this->request->referrer);
    }

    /**
     * Finds the ChannelShareProtocolConfig model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id Channel Share Protocol Config ID
     *
     * @return ChannelShareProtocolConfig the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): ChannelShareProtocolConfig
    {
        if (($model = ChannelShareProtocolConfig::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
