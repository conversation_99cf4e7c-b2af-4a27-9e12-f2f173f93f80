<?php

namespace payment\controllers;

use dcs\components\PaymentComponent;
use payment\models\WithholdReceiptSearch;
use Yii;
use yii\base\InvalidConfigException;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\Controller;

/**
 * WithholdReceiptController implements the CRUD actions for WithholdReceipt model.
 */
class WithholdReceiptController extends Controller
{
    /**
     * Lists all WithholdReceipt models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new WithholdReceiptSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single WithholdReceipt model.
     *
     * @return string
     * @throws InvalidConfigException
     */
    public function actionOrderQuery(): string
    {
        $client = PaymentComponent::instance();
        $client->transactionWithholdQuery(
            ArrayHelper::filter($this->request->get(), [
                'merchant_name',
                'merchant_key',
            ])
        );

        return Html::tag(
            'pre',
            Html::tag(
                'code',
                Json::encode(
                    $client->getResponse(),
                    JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
                )
            )
        );
    }
}
