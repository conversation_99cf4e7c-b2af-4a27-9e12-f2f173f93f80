<?php

namespace payment\controllers;

use payment\models\ChannelMaintainPlan;
use payment\models\ChannelMaintainPlanSearch;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\Exception;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * ChannelMaintainPlanController implements the CRUD actions for ChannelMaintainPlan model.
 */
class ChannelMaintainPlanController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all ChannelMaintainPlan models.
     *
     * @return string
     * @throws InvalidConfigException
     * @throws UserException
     * @throws Exception
     */
    public function actionIndex(): string
    {
        $model = new ChannelMaintainPlanSearch();
        if ($this->request->isPost) {
            $model->setScenario('create');
            $model->load($this->request->post());
            $session = Yii::$app->getSession();
            if (!$model->savePlan()) {
                $errors = array_merge(...array_values($model->getErrors()));
                $session->addFlash('error', implode(' ', $errors));
            } else {
                $session->addFlash('success', '操作成功');
            }
        }

        $dataProvider = (new ChannelMaintainPlanSearch())->search([]);

        return $this->render('index', [
            'model' => $model,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Deletes an existing ChannelMaintainPlan model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id Channel Maintain Plan ID
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function actionDelete(int $id): Response
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ChannelMaintainPlan model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id Channel Maintain Plan ID
     *
     * @return ChannelMaintainPlan the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): ChannelMaintainPlan
    {
        if (($model = ChannelMaintainPlan::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
