<?php

use payment\models\Channel;
use payment\models\ChannelSearch;
use payment\models\ProviderProduct;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ChannelSearch */

$this->title = '通道列表';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{update} {balance}',
            'buttons' => [
                'update' => static fn($url) => ActionColumn::newButton('编辑', $url, [
                    'class' => 'btn-primary layer-dialog',
                ]),
                'balance' => static function ($url, Channel $model) {
                    return ActionColumn::newButton('余额查询', [
                        'balance',
                        'account' => $model->channel_name,
                    ], [
                        'class' => 'btn-info layer-dialog',
                    ]);
                },
            ],
            'visibleButtons' => [
                'balance' => static fn(Channel $model) => $model->channel_provider_product_type === 'withdraw',
            ],
        ],

        'channel_name',
        'channel_alias',
        [
            'attribute' => 'channel_sign_company_code',
            'value' => 'signCompany.sign_company_name',
        ],
        [
            'attribute' => 'channel_provider_code',
            'value' => 'provider.provider_name',
        ],
        [
            'attribute' => 'channel_provider_product_type',
            'format' => ['in', ProviderProduct::typeList()],
        ],
        [
            'attribute' => 'channel_status',
            'format' => ['in', Channel::STATUS_LIST],
        ],
        'channel_merchant_no',
        'channel_created_at',
        'channel_updated_at',
    ],
]);
