<?php

use kartik\widgets\DepDrop;
use payment\models\Channel;
use payment\models\Provider;
use payment\models\ProviderProduct;
use payment\models\SignCompany;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model Channel */
?>

<?php $form = ActiveForm::begin([
    'type' => ActiveForm::TYPE_HORIZONTAL,
    'fieldConfig' => [
        'labelSpan' => 4,
    ],
]); ?>

<div class="box-body">

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'channel_name')->textInput(['maxlength' => true]) ?>

            <?= $form->field($model, 'channel_sign_company_code')->widget(Select2::class, [
                'data' => SignCompany::find()->select('sign_company_name')->indexBy('sign_company_code')->column(),
                'options' => [
                    'disabled' => !$model->isNewRecord,
                    'prompt' => $model->getAttributeLabel('channel_sign_company_code'),
                ],
            ]) ?>

            <?= $form->field($model, 'channel_provider_product_type')->widget(Select2::class, [
                'data' => ProviderProduct::typeList(),
                'options' => [
                    'disabled' => !$model->isNewRecord,
                ]
            ]) ?>

            <?= $form->field($model, 'channel_merchant_no')->textInput(['maxlength' => true]) ?>

            <?= $form->field($model, 'channel_need_charge_sms')->widget(Select2::class, [
                'data' => Channel::NEED_LIST,
            ]) ?>

            <?= $form->field($model, 'channel_need_binding_sms')->widget(Select2::class, [
                'data' => Channel::NEED_LIST,
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'channel_alias')->textInput(['maxlength' => true]) ?>

            <?= $form->field($model, 'channel_provider_code')->widget(DepDrop::class, [
                'type' => DepDrop::TYPE_SELECT2,
                'select2Options' => [
                    'theme' => Select2::THEME_DEFAULT,
                ],
                'options' => [
                    'disabled' => !$model->isNewRecord,
                    'placeholder' => $model->getAttributeLabel('channel_provider_code'),
                ],
                'pluginOptions' => [
                    'depends' => [Html::getInputId($model, 'channel_sign_company_code')],
                    'initDepends' => [Html::getInputId($model, 'channel_sign_company_code')],
                    'initialize' => true,
                    'params' => [],
                    'url' => Url::to(['product-bank-support/provider-list']),
                ],
            ]) ?>

            <?= $form->field($model, 'channel_type')->widget(Select2::class, [
                'data' => Channel::TYPE_LIST,
            ]) ?>

            <?= $form->field($model, 'channel_status')->widget(Select2::class, [
                'data' => Channel::STATUS_LIST,
            ]) ?>

            <?= $form->field($model, 'channel_support_operator')->widget(Select2::class, [
                'data' => Channel::OPERATE_LIST,
            ]) ?>

            <?= $form->field($model, 'channel_need_binding')->widget(Select2::class, [
                'data' => Channel::NEED_LIST,
            ]) ?>

            <?= $form->field($model, 'channel_verify_priority')->widget(Select2::class, [
                'data' => Channel::PRIORITY_LIST,
            ]) ?>
        </div>
    </div>
</div>

<div class="box-footer">
    <div class="col-md-offset-2">
        <?= Html::submitButton('保存', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>
</div>

<?php ActiveForm::end(); ?>
