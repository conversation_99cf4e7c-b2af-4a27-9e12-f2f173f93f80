<?php

use payment\models\Channel;
use payment\models\manual\BindCard;
use xlerr\common\assets\HighlightJsAsset;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View     $this
 * @var BindCard $model
 */

$this->title = '绑卡';

HighlightJsAsset::register($this);

$js = <<<JS
hljs.highlightAll();
JS;

$this->registerJs($js);

?>

<div class="row">
    <div class="col-md-6">
        <div class="box box-primary">
            <div class="box-header with-border">
                <div class="box-title"><?= $this->title ?></div>
            </div>
            <?php $form = ActiveForm::begin([
                'type' => ActiveForm::TYPE_HORIZONTAL,
            ]) ?>
            <div class="box-body">
                <?= $form->field($model, 'merchant_name') ?>

                <?= $form->field($model, 'channel_name')->widget(Select2::class, [
                    'data' => Channel::dropdownListWithType(Channel::TYPE_WITHHOLD),
                    'options' => [
                        'prompt' => $model->getAttributeLabel('channel_name'),
                    ],
                ]) ?>

                <?= $form->field($model, 'card_num') ?>

                <?= $form->field($model, 'id_num') ?>

                <?= $form->field($model, 'username') ?>

                <?= $form->field($model, 'mobile') ?>

                <?= $form->field($model, 'verify_code') ?>

                <?= $form->field($model, 'verify_seq') ?>

                <?= $form->field($model, 'need_redundant')->widget(Select2::class, [
                    'data' => [
                        'true' => '需要',
                        'false' => '不需要',
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => '请选择',
                    ],
                ]) ?>

            </div>
            <div class="box-footer">
                <div class="col-md-offset-2">
                    <?= Html::submitButton('提交', [
                        'class' => 'btn btn-primary',
                    ]) ?>
                </div>
            </div>
            <?php ActiveForm::end() ?>
        </div>
    </div>
    <div class="col-md-6">
        <div class="box box-default">
            <div class="box-header with-border">
                <div class="box-title">
                    Response Detail
                </div>
            </div>
            <div class="box-body" style="min-height: 100px">
                <pre style="padding: 0; margin: 0; border: 0;"><code style="padding: 0"
                                                                     class="language-json"><?= $model->response ?></code></pre>
            </div>
        </div>
    </div>
</div>
