<?php

use payment\models\Bank;
use payment\models\ProductBankSupport;
use payment\models\ProductBankSupportSearch;
use payment\models\Provider;
use payment\models\ProviderProduct;
use payment\models\SignCompany;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ProductBankSupportSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'product_bank_support_sign_company_code', [
            'options' => [
                'style' => 'min-width: 150px',
            ],
        ])->widget(Select2::class, [
            'data' => SignCompany::find()->select('sign_company_name')->indexBy('sign_company_code')->column(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('product_bank_support_sign_company_code'),
            ],
        ]) ?>

        <?= $form->field($model, 'product_bank_support_provider_code', [
            'options' => [
                'style' => 'min-width: 150px',
            ],
        ])->widget(Select2::class, [
            'data' => Provider::find()->select('provider_name')->indexBy('provider_code')->column(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('product_bank_support_provider_code'),
            ],
        ]) ?>

        <?= $form->field($model, 'product_bank_support_bank_code', [
            'options' => [
                'style' => 'min-width: 150px',
            ],
        ])->widget(Select2::class, [
            'data' => Bank::find()->select('bank_name')->indexBy('bank_code')->column(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('product_bank_support_bank_code'),
            ],
        ]) ?>

        <?= $form->field($model, 'product_bank_support_product_type', [
            'options' => [
                'style' => 'min-width: 100px',
            ],
        ])->widget(Select2::class, [
            'data' => ProviderProduct::typeList(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('product_bank_support_product_type'),
            ],
        ]) ?>

        <?= $form->field($model, 'product_bank_support_status', [
            'options' => [
                'style' => 'min-width: 100px',
            ],
        ])->widget(Select2::class, [
            'data' => ProductBankSupport::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('product_bank_support_status'),
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>
        <?= Html::a('银行配置', ['create'], [
            'class' => 'btn btn-success layer-dialog',
        ]) ?>
        <?= Html::a('批量维护计划', ['channel-maintain-plan/index'], [
            'class' => 'btn btn-twitter layer-dialog',
        ]) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>