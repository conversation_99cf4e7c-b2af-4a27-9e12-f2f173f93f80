<?php

use payment\models\Channel;
use payment\models\Withhold;
use payment\models\WithholdSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model WithholdSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
            'fieldConfig' => [
                'autoPlaceholder' => false,
            ],
        ]); ?>

        <?= $form->field($model, 'withhold_merchant_key') ?>

        <?= $form->field($model, 'channelKey')->textInput([
            'placeholder' => '支付通道订单号',
        ]) ?>

        <?= $form->field($model, 'withhold_card_num') ?>

        <?= $form->field($model, 'idNum')->textInput([
            'placeholder' => '身份证号码',
        ]) ?>

        <?= $form->field($model, 'mobile')->textInput([
            'placeholder' => '电话号码',
        ]) ?>

        <?= $form->field($model, 'username')->textInput([
            'placeholder' => '用户名称',
        ]) ?>

        <?= $form->field($model, 'withhold_status')->widget(Select2::class, [
            'data' => Withhold::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('withhold_status'),
                'strict' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'channelName', [
            'options' => [
                'style' => 'min-width: 180px',
            ],
        ])->widget(Select2::class, [
            'data' => Channel::find()->select('channel_alias')->indexBy('channel_name')->column(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '请选择通道',
            ],
        ]) ?>

        <?= $form->field($model, 'createStartDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'createEndDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建结束时间',
            ],
        ]) ?>

        <?= $form->field($model, 'finishStartDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '完成开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'finishEndDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '完成结束时间',
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>