<?php

use payment\models\Withhold;
use payment\models\WithholdSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel WithholdSearch */

$this->title = '代扣列表';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{detail}',
            'buttons' => [
                'detail' => static fn($url, Withhold $model) => ActionColumn::newButton('收据详情', [
                    'withhold-receipt/index',
                    'withhold_receipt_merchant_key' => $model->withhold_merchant_key,
                ], [
                    'class' => 'btn-primary layer-dialog',
                ]),
            ],
        ],

        'withhold_id',
        'withhold_merchant_key',
        'withhold_merchant_name',
        [
            'attribute' => 'withhold_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'withhold_status',
            'format' => ['in', Withhold::STATUS_LIST],
        ],
        [
            'attribute' => 'withhold_card_num',
            'format' => ['DDecrypt', false],
        ],
        [
            'label' => '身份证号码',
            'attribute' => 'card.card_id_num',
            'format' => ['DDecrypt', false],
        ],
        [
            'label' => '电话号码',
            'attribute' => 'card.card_mobile',
            'format' => ['DDecrypt', false],
        ],
        [
            'label' => '用户名称',
            'attribute' => 'card.card_username',
            'format' => ['DDecrypt', false],
        ],
        'withhold_capital',
        'withhold_operator',
        [
            'label' => '规则集名称',
            'attribute' => 'ruleset.ruleset_name',
            'format' => static fn($val) => (string)$val,
        ],
        'withhold_started_at',
        'withhold_finished_at',
        'withhold_created_at',
    ],
]);
