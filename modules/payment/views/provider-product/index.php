<?php

use payment\models\ProviderProduct;
use payment\models\ProviderProductSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ProviderProductSearch */

$this->title = '渠道产品配置';
$this->params['breadcrumbs'][] = $this->title;

echo Html::tag(
    'p',
    Html::a('创建', [
        'create',
        'provider_product_sign_company_code' => $searchModel->provider_product_sign_company_code,
        'provider_product_provider_code' => $searchModel->provider_product_provider_code,
    ], ['class' => 'btn btn-success'])
);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{update}',
        ],

        [
            'attribute' => 'provider_product_sign_company_code',
            'value' => 'signCompany.sign_company_name',
        ],
        [
            'attribute' => 'provider_product_provider_code',
            'value' => 'provider.provider_name',
        ],
        [
            'attribute' => 'provider_product_type',
            'format' => ['in', ProviderProduct::typeList()],
        ],
        [
            'attribute' => 'provider_product_status',
            'format' => ['in', ProviderProduct::STATUS_LIST],
        ],
        [
            'attribute' => 'provider_product_use_holiday_plan',
            'format' => ['in', ProviderProduct::HOLIDAY_PLAN_LIST],
        ],
        [
            'attribute' => 'provider_product_fee_type',
            'format' => ['in', ProviderProduct::FEE_TYPE_LIST],
        ],
        [
            'attribute' => 'provider_product_fee',
            'class' => MoneyDataColumn::class,
        ],
        'provider_product_fee_rate',
        [
            'attribute' => 'provider_product_fee_min',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'provider_product_fee_max',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'provider_product_min_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'provider_product_max_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'provider_product_card_type',
            'format' => ['in', ProviderProduct::CARD_TYPE_LIST],
        ],
        [
            'attribute' => 'provider_product_fee_status',
            'format' => ['in', ProviderProduct::FEE_STATUS_LIST],
        ],
        [
            'attribute' => 'provider_product_verify_fee',
            'class' => MoneyDataColumn::class,
        ],
        'provider_product_start_at',
        'provider_product_end_at',
        // 'provider_product_settle_type',
        // 'provider_product_loaning_rate',
        // 'provider_product_account_type',
        // 'provider_product_create_at',
        // 'provider_product_update_at',
        // 'provider_product_loaning_formula',
        // 'provider_product_round_type',

    ],
]);
