<?php

use kartik\widgets\DateTimePicker;
use kartik\widgets\DepDrop;
use payment\models\Provider;
use payment\models\ProviderProduct;
use payment\models\SignCompany;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model ProviderProduct */

$signCompanyName = SignCompany::find()->where([
    'sign_company_code' => $model->provider_product_sign_company_code,
])->select('sign_company_name')->scalar();

$providerName = Provider::find()->where([
    'provider_code' => $model->provider_product_provider_code,
])->select('provider_name')->scalar();

?>

<?php $form = ActiveForm::begin([
    'type' => ActiveForm::TYPE_HORIZONTAL,
    'formConfig' => [
        'labelSpan' => 3,
    ],
]); ?>

<div class="box-body">
    <div class="row">
        <div class="col-md-6">
            <?= Html::activeHiddenInput($model, 'provider_product_sign_company_code') ?>
            <div class="form-group required">
                <label class="control-label has-star col-md-3"><?= $model->getAttributeLabel('provider_product_sign_company_code')?></label>
                <div class="col-md-9">
                    <input type="text" class="form-control" value="<?= $signCompanyName ?>" disabled>
                    <div class="help-block help-block-error"></div>
                </div>
            </div>

            <?= $form->field($model, 'provider_product_type')->widget(Select2::class, [
                'data' => ProviderProduct::typeList(),
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_type'),
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_account_type')->widget(Select2::class, [
                'data' => ProviderProduct::ACCOUNT_TYPE_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_account_type'),
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_fee')->widget(MoneyInput::class) ?>

            <?= $form->field($model, 'provider_product_fee_min')->widget(MoneyInput::class) ?>

            <?= $form->field($model, 'provider_product_min_amount')->widget(MoneyInput::class) ?>

            <?= $form->field($model, 'provider_product_start_at')->widget(DateTimePicker::class, [
                'type' => DateTimePicker::TYPE_INPUT,
                'options' => [
                    'autocomplete' => 'off',
                ],
                'pluginOptions' => [
                    'todayBtn' => 'linked',
                    'format' => 'yyyy-mm-dd hh:ii:ss',
                    'todayHighlight' => true,
                    'autoclose' => true,
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_card_type')->widget(Select2::class, [
                'data' => ProviderProduct::CARD_TYPE_LIST,
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_card_type'),
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_settle_type')->widget(Select2::class, [
                'data' => ProviderProduct::SETTLE_TYPE_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_settle_type'),
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_loaning_rate')->textInput() ?>

            <?= $form->field($model, 'provider_product_use_holiday_plan')->widget(Select2::class, [
                'data' => ProviderProduct::HOLIDAY_PLAN_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_use_holiday_plan'),
                ],
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= Html::activeHiddenInput($model, 'provider_product_provider_code') ?>
            <div class="form-group required">
                <label class="control-label has-star col-md-3"><?= $model->getAttributeLabel('provider_product_provider_code') ?></label>
                <div class="col-md-9">
                    <input type="text" class="form-control" value="<?= $providerName ?>" disabled>
                    <div class="help-block help-block-error"></div>
                </div>
            </div>

            <?= $form->field($model, 'provider_product_status')->widget(Select2::class, [
                'data' => ProviderProduct::STATUS_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_status'),
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_fee_status')->widget(Select2::class, [
                'data' => ProviderProduct::FEE_STATUS_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_fee_status'),
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_fee_rate')->textInput() ?>

            <?= $form->field($model, 'provider_product_fee_max')->widget(MoneyInput::class) ?>

            <?= $form->field($model, 'provider_product_max_amount')->widget(MoneyInput::class) ?>

            <?= $form->field($model, 'provider_product_end_at')->widget(DateTimePicker::class, [
                'type' => DateTimePicker::TYPE_INPUT,
                'options' => [
                    'autocomplete' => 'off',
                ],
                'pluginOptions' => [
                    'todayBtn' => 'linked',
                    'format' => 'yyyy-mm-dd hh:ii:ss',
                    'todayHighlight' => true,
                    'autoclose' => true,
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_fee_type')->widget(Select2::class, [
                'data' => ProviderProduct::FEE_TYPE_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_fee_type'),
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_loaning_formula')->widget(DepDrop::class, [
                'type'           => DepDrop::TYPE_SELECT2,
                'select2Options' => [
                    'theme'      => Select2::THEME_DEFAULT,
                    'hideSearch' => true,
                ],
                'options'        => [
                    'placeholder' => '请选择...',
                ],
                'pluginOptions'  => [
                    'depends'     => [Html::getInputId($model, 'provider_product_settle_type')],
                    'initDepends' => [Html::getInputId($model, 'provider_product_settle_type')],
                    'initialize'  => true,
                    'params'      => [],
                    'placeholder' => '请选择...',
                    'url'         => Url::to(['loaning-formula-list']),
                ],
            ]) ?>

            <?= $form->field($model, 'provider_product_verify_fee')->widget(MoneyInput::class) ?>

            <?= $form->field($model, 'provider_product_round_type')->widget(Select2::class, [
                'data' => ProviderProduct::ROUTE_TYPE_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('provider_product_round_type'),
                ],
            ]) ?>
        </div>
    </div>


</div>

<div class="box-footer text-center">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>

    <?= Html::a('取消', [
        'index',
        'provider_product_sign_company_code' => $model->provider_product_sign_company_code,
        'provider_product_provider_code' => $model->provider_product_provider_code,
    ], [
        'class' => 'btn btn-default',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
