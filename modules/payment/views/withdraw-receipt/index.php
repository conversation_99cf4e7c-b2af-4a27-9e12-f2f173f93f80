<?php

use payment\models\WithdrawReceipt;
use payment\models\WithdrawReceiptSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel WithdrawReceiptSearch */

echo GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{order-query}',
            'buttons' => [
                'order-query' => static fn($_, WithdrawReceipt $model) => ActionColumn::newButton('通道订单查询', [
                    'order-query',
                    'merchant_name' => $model->withdraw_receipt_merchant_name,
                    'merchant_key' => $model->withdraw_receipt_merchant_key,
                ], [
                    'class' => 'btn-primary layer-dialog',
                ]),
            ],
        ],

        'withdraw_receipt_id',
        'withdraw_receipt_merchant_key',
        'withdraw_receipt_merchant_name',
        [
            'attribute' => 'withdraw_receipt_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'withdraw_receipt_status',
            'format' => ['in', WithdrawReceipt::STATUS_LIST],
        ],
        [
            'attribute' => 'withdraw_receipt_channel_name',
            'value' => 'channel.channel_alias',
        ],
        'withdraw_receipt_channel_key',
        'withdraw_receipt_channel_resp_code',
        'withdraw_receipt_channel_resp_message',
        'withdraw_receipt_started_at',
        'withdraw_receipt_finished_at',
    ],
]);
