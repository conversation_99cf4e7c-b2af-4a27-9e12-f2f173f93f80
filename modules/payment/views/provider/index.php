<?php

use payment\models\Provider;
use payment\models\ProviderSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ProviderSearch */

$this->title = '渠道信息';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{update}',
        ],

        'provider_id',
        'provider_code',
        'provider_name',
        [
            'attribute' => 'provider_status',
            'format' => ['in', Provider::STATUS_LIST],
        ],
        'provider_remark',
        'provider_create_at',
        'provider_update_at',
    ],
]);
