<?php

use payment\models\Channel;
use payment\models\ChannelShareProtocolConfig;
use payment\models\ChannelShareProtocolConfigSearch;
use payment\models\Provider;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ChannelShareProtocolConfigSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'channel_share_protocol_config_provider_code', [
            'options' => [
                'style' => 'min-width: 160px',
            ],
        ])->widget(Select2::class, [
            'data' => Provider::find()
                ->select('provider_name')
                ->indexBy('provider_code')
                ->column(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('channel_share_protocol_config_provider_code'),
            ],
        ]) ?>

        <?= $form->field($model, 'channel_share_protocol_config_channel_name', [
            'options' => [
                'style' => 'min-width: 260px',
            ],
        ])->widget(Select2::class, [
            'data' => Channel::find()
                ->where([
                    'channel_status' => 1,
                    'channel_provider_product_type' => 'protocol',
                ])
                ->select('channel_name')
                ->indexBy('channel_name')
                ->column(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('channel_share_protocol_config_channel_name'),
            ],
        ]) ?>

        <?= $form->field($model, 'channel_share_protocol_config_status', [
            'options' => [
                'style' => 'min-width: 120px',
            ],
        ])->widget(Select2::class, [
            'data' => ChannelShareProtocolConfig::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('channel_share_protocol_config_status'),
            ],
        ]) ?>


        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>
        <?= Html::a('创建', ['create'], ['class' => 'btn btn-success layer-dialog']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>