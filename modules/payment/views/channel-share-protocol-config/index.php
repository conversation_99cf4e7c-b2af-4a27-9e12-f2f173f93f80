<?php

use payment\models\ChannelShareProtocolConfig;
use payment\models\ChannelShareProtocolConfigSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ChannelShareProtocolConfigSearch */

$this->title = '协议共享配置管理';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{enable} {disable}',
            'buttons' => [
                'enable' => static function ($url) {
                    return DialogActionColumn::newButton('启用', $url, [
                        'data' => [
                            'method' => 'post',
                            'confirm' => '确定启用吗?',
                        ],
                        'class' => 'btn btn-success',
                    ]);
                },
                'disable' => static function ($url) {
                    return DialogActionColumn::newButton('禁用', $url, [
                        'data' => [
                            'method' => 'post',
                            'confirm' => '确定禁用吗?',
                        ],
                        'class' => 'btn btn-danger',
                    ]);
                },
            ],
            'visibleButtons' => [
                'enable' => static fn(ChannelShareProtocolConfig $model
                ) => $model->channel_share_protocol_config_status === ChannelShareProtocolConfig::STATUS_DISABLE,
                'disable' => static fn(ChannelShareProtocolConfig $model
                ) => $model->channel_share_protocol_config_status === ChannelShareProtocolConfig::STATUS_ENABLE,
            ],
        ],
        [
            'attribute' => 'channel_share_protocol_config_provider_code',
            'value' => 'provider.provider_name',
        ],
        'channel_share_protocol_config_channel_name',
        'channel_share_protocol_config_share_channel_name',
//        [
//            'attribute' => 'channel_share_protocol_config_status',
//            'format' => static function ($status) {
//                return Html::tag('span', ChannelShareProtocolConfig::STATUS_LIST[$status], [
//                    'class' => $status === ChannelShareProtocolConfig::STATUS_ENABLE ? 'text-success' : 'text-danger',
//                ]);
//            },
//        ],
    ],
]);
