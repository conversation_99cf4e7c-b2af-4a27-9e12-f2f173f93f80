<?php

namespace payment\models;

use yii\db\ActiveQuery;

/**
 * This is the model class for table "withhold_receipt".
 *
 * @property int          $withhold_receipt_id
 * @property string       $withhold_receipt_merchant_name        商户名称
 * @property string       $withhold_receipt_merchant_key         商户订单号
 * @property string       $withhold_receipt_channel_name         支付通道
 * @property string|null  $withhold_receipt_channel_key          支付通道订单号
 * @property string|null  $withhold_receipt_channel_inner_key    支付通道内部订单号
 * @property string       $withhold_receipt_card_num             卡号
 * @property int          $withhold_receipt_amount               交易金额（分）
 * @property int          $withhold_receipt_status               交易状态： 0=新建，1=处理中，2=成功，3=失败
 * @property int          $withhold_receipt_status_stage         交易细分子状态：0=默认，1=缺少信息，2=余额不足
 * @property string|null  $withhold_receipt_channel_resp_code    支付通道返回码
 * @property string|null  $withhold_receipt_channel_resp_message 支付通道返回信息
 * @property string       $withhold_receipt_started_at           交易发起时间
 * @property string       $withhold_receipt_finished_at          交易完成时间
 * @property string       $withhold_receipt_created_at
 * @property string       $withhold_receipt_updated_at
 * @property string|null  $withhold_receipt_redirect             通道方跳转URL
 * @property string|null  $withhold_receipt_ruleset_code         支付路由规则集代码
 * @property-read Channel $channel
 */
class WithholdReceipt extends \yii\db\ActiveRecord
{
    use GetDbTrait;

    public const STATUS_LIST = [
        '新建',
        '处理中',
        '成功',
        '失败',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'withhold_receipt';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['withhold_receipt_amount', 'withhold_receipt_status', 'withhold_receipt_status_stage'], 'integer'],
            [
                [
                    'withhold_receipt_started_at',
                    'withhold_receipt_finished_at',
                    'withhold_receipt_created_at',
                    'withhold_receipt_updated_at',
                ],
                'safe',
            ],
            [
                [
                    'withhold_receipt_merchant_name',
                    'withhold_receipt_merchant_key',
                    'withhold_receipt_channel_name',
                    'withhold_receipt_channel_key',
                    'withhold_receipt_channel_inner_key',
                ],
                'string',
                'max' => 64,
            ],
            [
                ['withhold_receipt_card_num', 'withhold_receipt_channel_resp_code', 'withhold_receipt_ruleset_code'],
                'string',
                'max' => 32,
            ],
            [['withhold_receipt_channel_resp_message'], 'string', 'max' => 255],
            [['withhold_receipt_redirect'], 'string', 'max' => 512],
            [
                ['withhold_receipt_channel_name', 'withhold_receipt_channel_key'],
                'unique',
                'targetAttribute' => ['withhold_receipt_channel_name', 'withhold_receipt_channel_key'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withhold_receipt_id' => 'ID',
            'withhold_receipt_merchant_name' => '商户名称',
            'withhold_receipt_merchant_key' => '商户订单号',
            'withhold_receipt_channel_name' => '支付通道',
            'withhold_receipt_channel_key' => '支付通道订单号',
            'withhold_receipt_channel_inner_key' => '支付通道内部订单号',
            'withhold_receipt_card_num' => '卡号',
            'withhold_receipt_amount' => '交易金额(元)',
            'withhold_receipt_status' => '交易状态',
            'withhold_receipt_status_stage' => '交易细分子状态：0=默认，1=缺少信息，2=余额不足',
            'withhold_receipt_channel_resp_code' => '支付通道返回码',
            'withhold_receipt_channel_resp_message' => '支付通道返回信息',
            'withhold_receipt_started_at' => '交易发起时间',
            'withhold_receipt_finished_at' => '交易完成时间',
            'withhold_receipt_created_at' => 'Withhold Receipt Created At',
            'withhold_receipt_updated_at' => 'Withhold Receipt Updated At',
            'withhold_receipt_redirect' => '通道方跳转URL',
            'withhold_receipt_ruleset_code' => '支付路由规则集代码',
        ];
    }

    public function getChannel(): ActiveQuery
    {
        return $this->hasOne(Channel::class, ['channel_name' => 'withhold_receipt_channel_name']);
    }
}
