<?php

namespace payment\models\manual;

use payment\components\GrayPaymentComponent;
use xlerr\desensitise\Desensitise;
use yii\base\Model;

use function xlerr\desensitise\encrypt;

class Recharge extends Model
{
    public $merchant_name;
    public $merchant_key;
    public $channel_name;
    public $amount;
    public $card_num;
    public $id_num;
    public $username;
    public $mobile;

    public string $response = '';

    public function rules()
    {
        return [
            [
                [
                    'merchant_name',
                    'merchant_key',
                    'channel_name',
                    'amount',
                    'card_num',
                    'id_num',
                    'username',
                    'mobile',
                ],
                'required',
            ],
            [
                ['amount'],
                'integer',
                'min' => 1,
                'tooSmall' => '最小不能低于0.01元',
                'max' => 10000,
                'tooBig' => '最大不超过10.00元',
            ],
        ];
    }

    public function attributeLabels()
    {
        return [
            'merchant_key' => '商户订单号',
            'merchant_name' => '商户名称',
            'channel_name' => '充值通道',
            'amount' => '金额(元)',
            'card_num' => '银行卡号',
            'id_num' => '证件号码',
            'username' => '户名',
            'mobile' => '手机号',
        ];
    }

    public function submit(): void
    {
        if ($this->validate()) {
            $client = GrayPaymentComponent::instance();
            $client->withholdRecharge($this);

            $this->response = json_encode(
                $client->getResponse(),
                JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
            );
        }
    }

    public function fields(): array
    {
        return [
            'merchant_key',
            'merchant_name',
            'channel_name',
            'amount',
            'card_num_encrypt' => fn(self $model) => encrypt(
                $model->card_num,
                Desensitise::TYPE_BANK_CARD_NUMBER
            )->hash,
            'id_num_encrypt' => fn(self $model) => encrypt($model->id_num, Desensitise::TYPE_IDENTITY_NUMBER)->hash,
            'username_encrypt' => fn(self $model) => encrypt($model->username, Desensitise::TYPE_NAME)->hash,
            'mobile_encrypt' => fn(self $model) => encrypt($model->mobile, Desensitise::TYPE_PHONE_NUMBER)->hash,
        ];
    }
}
