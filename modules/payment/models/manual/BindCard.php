<?php

namespace payment\models\manual;

use payment\components\GrayPaymentComponent;
use yii\base\Model;

class BindCard extends Model
{
    public $merchant_name;
    public $channel_name;
    public $card_num;
    public $id_num;
    public $username;
    public $mobile;
    public $verify_code;
    public $verify_seq;
    public $need_redundant;

    public string $response = '';

    public function rules()
    {
        return [
            [
                [
                    'merchant_name',
                    'channel_name',
                    'card_num',
                    'id_num',
                    'username',
                    'mobile',
                    'verify_code',
                    'verify_seq',
                ],
                'required',
            ],
            [['need_redundant'], 'string'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'merchant_name' => '商户名称',
            'channel_name' => '通道',
            'card_num' => '银行卡号',
            'id_num' => '身份证号',
            'username' => '用户姓名',
            'mobile' => '手机号',
            'verify_code' => '验证码',
            'verify_seq' => '验证序列',
            'need_redundant' => '其他通道认证',
        ];
    }

    public function submit(): void
    {
        if ($this->validate()) {
            $client = GrayPaymentComponent::instance();
            $client->withholdBindCard($this);

            $this->response = json_encode(
                $client->getResponse(),
                JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
            );
        }
    }
}
