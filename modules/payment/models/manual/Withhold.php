<?php

namespace payment\models\manual;

use payment\components\GrayPaymentComponent;
use yii\base\Model;

class Withhold extends Model
{
    public $merchant_key;
    public $merchant_name;
    public $channel_name;
    public $amount;
    public $card_num;
    public $id_num;
    public $username;
    public $mobile;
    public $remark;

    public string $response = '';

    public function rules()
    {
        return [
            [
                [
                    'merchant_key',
                    'merchant_name',
                    'channel_name',
                    'amount',
                    'card_num',
                    'id_num',
                    'username',
                    'mobile',
                    'remark',
                ],
                'required',
            ],
            [
                ['amount'],
                'integer',
                'min' => 1,
                'tooSmall' => '最小不能低于0.01元',
                'max' => 10000,
                'tooBig' => '最大不超过10.00元',
            ],
        ];
    }

    public function attributeLabels()
    {
        return [
            'merchant_key' => '代扣订单号',
            'merchant_name' => '商户名称',
            'channel_name' => '通道',
            'amount' => '金额(元)',
            'card_num' => '银行卡号',
            'id_num' => '身份证号',
            'username' => '用户姓名',
            'mobile' => '手机号',
            'remark' => '代扣说明',
        ];
    }

    public function submit(): void
    {
        if ($this->validate()) {
            $client = GrayPaymentComponent::instance();
            $client->withhold($this);

            $this->response = json_encode(
                $client->getResponse(),
                JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
            );
        }
    }
}
