<?php

namespace payment\models\manual;

use payment\components\GrayPaymentComponent;
use yii\base\Model;

class Callback extends Model
{
    public $merchant_key;
    public $merchant_name;
    public $reason;

    public string $response = '';

    public function rules()
    {
        return [
            [['merchant_name', 'merchant_key', 'reason'], 'required'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'merchant_name' => '商户名称',
            'merchant_key' => '商户订单号',
            'reason' => '回调说明',
        ];
    }

    public function submit(): void
    {
        if ($this->validate()) {
            $client = GrayPaymentComponent::instance();
            $client->manualCallback($this);

            $this->response = json_encode(
                $client->getResponse(),
                JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
            );
        }
    }
}
