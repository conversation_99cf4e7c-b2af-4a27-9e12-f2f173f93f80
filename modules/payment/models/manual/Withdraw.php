<?php

namespace payment\models\manual;

use payment\components\GrayPaymentComponent;
use yii\base\Model;

class Withdraw extends Model
{
    public $merchant_name;
    public $merchant_key;
    public $trade_no;
    public $account;
    public $amount;

    public $receiver_type;
    public $receiver_name;
    public $receiver_account;
    public $receiver_identity;
    public $receiver_identity_valid_date;
    public $reason;

    public $receiver_bank_code;
    public $receiver_bank_branch;
    public $receiver_bank_subbranch;
    public $receiver_bank_province;
    public $receiver_bank_city;
    public $callback;

    public string $response = '';

    public function rules()
    {
        return [
            [
                [
                    'merchant_name',
                    'merchant_key',
                    'trade_no',
                    'account',
                    'amount',

                    'receiver_type',
                    'receiver_name',
                    'receiver_account',
                    'receiver_identity',
                    'reason',
                ],
                'required',
            ],
            [
                ['amount'],
                'integer',
                'min' => 1,
                'tooSmall' => '最小不能低于0.01元',
                'max' => 1000,
                'tooBig' => '最大不超过10.00元',
            ],
            [
                [
                    'receiver_identity_valid_date',
                    'receiver_bank_code',
                    'receiver_bank_branch',
                    'receiver_bank_subbranch',
                    'receiver_bank_province',
                    'receiver_bank_city',
                    'callback',
                ],
                'safe',
            ],
        ];
    }

    public function attributeLabels()
    {
        return [
            'merchant_name' => '商户名称',
            'merchant_key' => '商户订单号',
            'trade_no' => '交易流水号',
            'account' => '代付通道',
            'amount' => '金额(元)',
            'reason' => '代付说明',
            'receiver_type' => '账户类型',
            'receiver_name' => '收款账户名',
            'receiver_account' => '收款账号',
            'receiver_identity' => '收款证件号',
            'receiver_identity_valid_date' => '证件号有效期(yyyyMMdd-yyyyMMdd)',
            'receiver_bank_code' => '收款银行',
            'receiver_bank_branch' => '收款银行分行',
            'receiver_bank_subbranch' => '收款银行支行',
            'receiver_bank_province' => '收款银行分行省份',
            'receiver_bank_city' => '收款银行分行城市',
            'callback' => '回调地址',
        ];
    }

    public function submit(): void
    {
        if ($this->validate()) {
            $client = GrayPaymentComponent::instance();
            $client->autoWithdraw($this);

            $this->response = json_encode(
                $client->getResponse(),
                JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
            );
        }
    }
}
