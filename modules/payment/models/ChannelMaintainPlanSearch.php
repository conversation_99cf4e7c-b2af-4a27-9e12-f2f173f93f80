<?php

namespace payment\models;

use Carbon\Carbon;
use Exception;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;

/**
 * ChannelMaintainPlanSearch represents the model behind the search form about `payment\models\ChannelMaintainPlan`.
 */
class ChannelMaintainPlanSearch extends ChannelMaintainPlan
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'channel_maintain_plan_provider_code',
                    'channel_maintain_plan_product_type',
                    'channel_maintain_plan_bank_code',
                    'channel_maintain_plan_start_at',
                    'channel_maintain_plan_end_at',
                ],
                'required',
                'on' => 'create',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ChannelMaintainPlan::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['channel_maintain_plan_id'],
                'defaultOrder' => ['channel_maintain_plan_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        return $dataProvider;
    }

    /**
     * @return bool
     * @throws UserException
     * @throws InvalidConfigException
     * @throws \yii\db\Exception
     */
    public function savePlan(): bool
    {
        if (!$this->validate()) {
            return false;
        }

        if ($this->channel_maintain_plan_start_at >= $this->channel_maintain_plan_end_at) {
            $this->addError('channel_maintain_plan_provider_code', '维护开始时间必须小于维护结束时间');

            return false;
        }

        $timeOverlap = self::find()->where([
            'channel_maintain_plan_bank_code' => $this->channel_maintain_plan_bank_code,
            'channel_maintain_plan_provider_code' => $this->channel_maintain_plan_provider_code,
            'channel_maintain_plan_product_type' => $this->channel_maintain_plan_product_type,
        ])->andWhere([
            'or',
            [
                'and',
                ['<', 'channel_maintain_plan_start_at', $this->channel_maintain_plan_start_at],
                ['>', 'channel_maintain_plan_end_at', $this->channel_maintain_plan_start_at],
            ],
            [
                'and',
                ['<', 'channel_maintain_plan_start_at', $this->channel_maintain_plan_end_at],
                ['>', 'channel_maintain_plan_end_at', $this->channel_maintain_plan_end_at],
            ],
            [
                'and',
                ['=', 'channel_maintain_plan_start_at', $this->channel_maintain_plan_start_at],
                ['<=', 'channel_maintain_plan_end_at', $this->channel_maintain_plan_end_at],
            ],
            [
                'and',
                ['>=', 'channel_maintain_plan_start_at', $this->channel_maintain_plan_start_at],
                ['=', 'channel_maintain_plan_end_at', $this->channel_maintain_plan_end_at],
            ],
        ])->exists();
        if ($timeOverlap) {
            $this->addError('channel_maintain_plan_provider_code', '维护时间重叠');

            return false;
        }

        $datetime = Carbon::now()->toDateTimeString();
        $data = [];
        $signCompanyList = ProductBankSupport::find()
            ->innerJoinWith([
                'channel' => function (ActiveQuery $query) {
                    $query->onCondition(['channel_status' => 1]);
                },
            ], false)
            ->where([
                'product_bank_support_bank_code' => $this->channel_maintain_plan_bank_code,
                'product_bank_support_product_type' => $this->channel_maintain_plan_product_type,
                'product_bank_support_provider_code' => $this->channel_maintain_plan_provider_code,
            ])
            ->select('product_bank_support_sign_company_code')
            ->distinct()
            ->column();
        foreach ($signCompanyList as $signCompany) {
            $data[] = [
                $signCompany,
                $this->channel_maintain_plan_provider_code,
                $this->channel_maintain_plan_bank_code,
                $this->channel_maintain_plan_product_type,
                $this->channel_maintain_plan_start_at,
                $this->channel_maintain_plan_end_at,
                $datetime,
                $datetime,
            ];
        }

        $transaction = self::getDb()->beginTransaction();
        try {
            $affected = self::getDb()->createCommand()->batchInsert(self::tableName(), [
                'channel_maintain_plan_sign_company_code',
                'channel_maintain_plan_provider_code',
                'channel_maintain_plan_bank_code',
                'channel_maintain_plan_product_type',
                'channel_maintain_plan_start_at',
                'channel_maintain_plan_end_at',
                'channel_maintain_plan_created_at',
                'channel_maintain_plan_updated_at',
            ], $data)->execute();
            if ($affected !== count($signCompanyList)) {
                throw new UserException('创建条数据异常');
            }

            $transaction->commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollBack();
            $this->addError('channel_maintain_plan_provider_code', $e->getMessage());

            return false;
        }
    }

    public function getProductBankSupports(): ActiveQuery
    {
        return $this->hasMany(ProductBankSupport::class, [
            'product_bank_support_provider_code' => 'channel_maintain_plan_provider_code',
            'product_bank_support_product_type' => 'channel_maintain_plan_product_type',
            'product_bank_support_bank_code' => 'channel_maintain_plan_bank_code',
        ]);
    }
}
