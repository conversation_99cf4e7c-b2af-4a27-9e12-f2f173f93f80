<?php

namespace payment\models;

use Yii;

/**
 * This is the model class for table "card".
 *
 * @property int $card_id
 * @property string $card_num_mask 卡号打码明文
 * @property string $card_num 卡号密文
 * @property string $card_username_mask 账户名打码
 * @property string $card_username 用户名密文
 * @property string $card_id_num_mask 身份证号打码明文
 * @property string $card_id_num 身份证号密文
 * @property string|null $card_mobile 手机号密文
 * @property string $card_mobile_mask 手机号打码明文
 * @property string $card_bank_code 银行编号
 * @property string $card_type 卡类型: CC=信用卡 SCC=准贷记卡 PC=预付费卡 DC=储蓄卡
 * @property string|null $card_expiry_year 信用卡有效期年 (2017年：17)
 * @property string|null $card_expiry_month 信用卡有效期月 (08月：08)
 * @property string|null $card_cvv 信用卡背面后三位安全码
 * @property int $card_status 卡状态：0=未验证, 1=验证
 * @property string $card_created_at 创建时间
 * @property string $card_updated_at 更新时间
 */
class Card extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'card';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbPaysvr');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['card_status'], 'integer'],
            [['card_created_at', 'card_updated_at'], 'safe'],
            [['card_num_mask', 'card_num', 'card_username', 'card_id_num_mask', 'card_id_num', 'card_mobile', 'card_mobile_mask', 'card_bank_code'], 'string', 'max' => 32],
            [['card_username_mask'], 'string', 'max' => 63],
            [['card_type'], 'string', 'max' => 10],
            [['card_expiry_year', 'card_expiry_month'], 'string', 'max' => 2],
            [['card_cvv'], 'string', 'max' => 3],
            [['card_num'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'card_id' => 'Card ID',
            'card_num_mask' => '卡号打码明文',
            'card_num' => '卡号密文',
            'card_username_mask' => '账户名打码',
            'card_username' => '用户名密文',
            'card_id_num_mask' => '身份证号打码明文',
            'card_id_num' => '身份证号密文',
            'card_mobile' => '手机号密文',
            'card_mobile_mask' => '手机号打码明文',
            'card_bank_code' => '银行编号',
            'card_type' => '卡类型: CC=信用卡 SCC=准贷记卡 PC=预付费卡 DC=储蓄卡',
            'card_expiry_year' => '信用卡有效期年 (2017年：17)',
            'card_expiry_month' => '信用卡有效期月 (08月：08)',
            'card_cvv' => '信用卡背面后三位安全码',
            'card_status' => '卡状态：0=未验证, 1=验证',
            'card_created_at' => '创建时间',
            'card_updated_at' => '更新时间',
        ];
    }
}
