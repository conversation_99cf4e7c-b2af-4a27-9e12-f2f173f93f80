<?php

namespace payment\models;

use yii\db\ActiveQuery;

/**
 * This is the model class for table "withdraw_receipt".
 *
 * @property int          $withdraw_receipt_id
 * @property string|null  $withdraw_receipt_trade_no             请求流水号(业务幂等)
 * @property string       $withdraw_receipt_merchant_name        商户编号
 * @property string       $withdraw_receipt_merchant_key         商户流水号
 * @property int          $withdraw_receipt_amount               付款金额（分）
 * @property int          $withdraw_receipt_status               支付状态 0=新建, 1=处理中，2=成功，3=失败 4=冻结
 * @property string       $withdraw_receipt_channel_name         渠道名称
 * @property string|null  $withdraw_receipt_channel_key          渠道订单号
 * @property string|null  $withdraw_receipt_channel_resp_code    渠道状态码
 * @property string|null  $withdraw_receipt_channel_resp_message 渠道错误消息
 * @property string       $withdraw_receipt_started_at           发起时间
 * @property string       $withdraw_receipt_finished_at          完成时间
 * @property string       $withdraw_receipt_created_at           创建时间
 * @property string       $withdraw_receipt_updated_at           更新时间
 * @property string|null  $withdraw_receipt_redirect             通道方跳转URL
 * @property string       $withdraw_receipt_receiver_name        收款户名
 * @property string       $withdraw_receipt_receiver_no          收款帐号
 * @property string       $withdraw_receipt_receiver_identity    收款人证件号
 * @property string|null  $withdraw_receipt_receiver_bank_no     收款人银行行号
 * @property string|null  $withdraw_receipt_receiver_bank_name   收款人银行名称
 * @property string       $withdraw_receipt_receiver_bank_code   收款帐号发卡行
 * @property-read Channel $channel
 */
class WithdrawReceipt extends \yii\db\ActiveRecord
{
    use GetDbTrait;

    public const STATUS_LIST = ['新建', '处理中', '成功', '失败', '冻结'];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'withdraw_receipt';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['withdraw_receipt_amount', 'withdraw_receipt_status'], 'integer'],
            [
                [
                    'withdraw_receipt_started_at',
                    'withdraw_receipt_finished_at',
                    'withdraw_receipt_created_at',
                    'withdraw_receipt_updated_at',
                ],
                'safe',
            ],
            [
                [
                    'withdraw_receipt_trade_no',
                    'withdraw_receipt_merchant_name',
                    'withdraw_receipt_merchant_key',
                    'withdraw_receipt_channel_name',
                    'withdraw_receipt_channel_key',
                ],
                'string',
                'max' => 64,
            ],
            [
                [
                    'withdraw_receipt_channel_resp_code',
                    'withdraw_receipt_receiver_no',
                    'withdraw_receipt_receiver_identity',
                    'withdraw_receipt_receiver_bank_no',
                    'withdraw_receipt_receiver_bank_name',
                ],
                'string',
                'max' => 32,
            ],
            [['withdraw_receipt_channel_resp_message'], 'string', 'max' => 255],
            [['withdraw_receipt_redirect'], 'string', 'max' => 512],
            [['withdraw_receipt_receiver_name'], 'string', 'max' => 148],
            [['withdraw_receipt_receiver_bank_code'], 'string', 'max' => 16],
            [
                ['withdraw_receipt_channel_key', 'withdraw_receipt_channel_name'],
                'unique',
                'targetAttribute' => ['withdraw_receipt_channel_key', 'withdraw_receipt_channel_name'],
            ],
            [['withdraw_receipt_trade_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withdraw_receipt_id' => 'ID',
            'withdraw_receipt_trade_no' => '请求流水号(业务幂等)',
            'withdraw_receipt_merchant_name' => '商户名称',
            'withdraw_receipt_merchant_key' => '商户订单号',
            'withdraw_receipt_amount' => '付款金额(元)',
            'withdraw_receipt_status' => '交易状态',
            'withdraw_receipt_channel_name' => '支付通道',
            'withdraw_receipt_channel_key' => '支付通道订单号',
            'withdraw_receipt_channel_resp_code' => '渠道返回码',
            'withdraw_receipt_channel_resp_message' => '渠道返回信息',
            'withdraw_receipt_started_at' => '交易发起时间',
            'withdraw_receipt_finished_at' => '交易完成时间',
            'withdraw_receipt_created_at' => '创建时间',
            'withdraw_receipt_updated_at' => '更新时间',
            'withdraw_receipt_redirect' => '通道方跳转URL',
            'withdraw_receipt_receiver_name' => '收款户名',
            'withdraw_receipt_receiver_no' => '收款帐号',
            'withdraw_receipt_receiver_identity' => '收款人证件号',
            'withdraw_receipt_receiver_bank_no' => '收款人银行行号',
            'withdraw_receipt_receiver_bank_name' => '收款人银行名称',
            'withdraw_receipt_receiver_bank_code' => '收款帐号发卡行',
        ];
    }

    public function getChannel(): ActiveQuery
    {
        return $this->hasOne(Channel::class, ['channel_name' => 'withdraw_receipt_channel_name']);
    }
}
