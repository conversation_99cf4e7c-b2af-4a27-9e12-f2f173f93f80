<?php

namespace payment\models;

use Carbon\Carbon;
use xlerr\desensitise\Desensitise;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;

use function xlerr\desensitise\encrypt;

/**
 * WithdrawSearch represents the model behind the search form about `payment\models\Withdraw`.
 */
class WithdrawSearch extends Withdraw
{
    public $channelKey;
    public $channelName;
    public $createStartDate;
    public $createEndDate;
    public $finishStartDate;
    public $finishEndDate;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'channelKey',
                    'channelName',
                    'createStartDate',
                    'createEndDate',
                    'finishStartDate',
                    'finishEndDate',
                    'withdraw_merchant_key',
                    'withdraw_receiver_name',
                    'withdraw_status',
                    'withdraw_receiver_no',
                    'withdraw_receiver_identity',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Withdraw::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['withdraw_id'],
                'defaultOrder' => ['withdraw_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        if ($this->channelKey || $this->channelName) {
            $query->innerJoinWith([
                'withdrawReceipts' => function (ActiveQuery $query) {
                    $query->andFilterWhere([
                        'withdraw_receipt_channel_key' => $this->channelKey,
                        'withdraw_receipt_channel_name' => $this->channelName,
                    ]);
                },
            ]);
        }

        $receiverName = '';
        $receiverNo = '';
        $receiverIdentity = '';
        if ($this->withdraw_receiver_name) {
            $receiverName = encrypt($this->withdraw_receiver_name, Desensitise::TYPE_NAME)->hash;
        }
        if ($this->withdraw_receiver_no) {
            $receiverNo = encrypt($this->withdraw_receiver_no, Desensitise::TYPE_BANK_CARD_NUMBER)->hash;
        }
        if ($this->withdraw_receiver_identity) {
            $receiverIdentity = encrypt($this->withdraw_receiver_identity, Desensitise::TYPE_IDENTITY_NUMBER)->hash;
        }


        // grid filtering conditions
        $query->andFilterWhere([
            'withdraw_merchant_key' => $this->withdraw_merchant_key,
            'withdraw_status' => $this->withdraw_status,
            'withdraw_receiver_name' => $receiverName,
            'withdraw_receiver_no' => $receiverNo,
            'withdraw_receiver_identity' => $receiverIdentity,
        ]);

        $query->andFilterWhere([
            'and',
            ['>=', 'withdraw_created_at', $this->createStartDate],
            [
                '<',
                'withdraw_created_at',
                $this->createEndDate ? Carbon::parse($this->createEndDate)->addDay()->toDateString() : null,
            ],
            ['>=', 'withdraw_finished_at', $this->finishStartDate],
            [
                '<',
                'withdraw_finished_at',
                $this->finishEndDate ? Carbon::parse($this->finishEndDate)->addDay()->toDateString() : null,
            ],
        ]);

        return $dataProvider;
    }
}
