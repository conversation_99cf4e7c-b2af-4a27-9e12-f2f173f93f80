<?php

namespace payment\models;

use yii\db\ActiveQuery;

/**
 * This is the model class for table "withhold".
 *
 * @property int                    $withhold_id
 * @property string                 $withhold_merchant_name 商户名称
 * @property string                 $withhold_merchant_key  商户订单号
 * @property string                 $withhold_card_num      卡号
 * @property int                    $withhold_status        交易状态：0=新建, 1=处理中，2=成功，3=失败
 * @property int                    $withhold_status_stage  交易细分子状态：0=默认，1=通道不可用，2=用户取消订单
 * @property int                    $withhold_amount        交易金额（分）
 * @property string                 $withhold_callback      回调URL
 * @property string                 $withhold_remark        备注信息
 * @property string                 $withhold_capital       资金方
 * @property string                 $withhold_operator      操作者
 * @property string                 $withhold_started_at    交易发起时间
 * @property string                 $withhold_finished_at   交易完成时间
 * @property string                 $withhold_created_at
 * @property string                 $withhold_updated_at
 * @property string|null            $withhold_redirect      业务方跳转URL
 * @property string|null            $withhold_ruleset_code  支付路由规则集代码
 * @property string|null            $withhold_ledgers       分账信息
 * @property-read Ruleset           $ruleset
 * @property-read Card              $card
 * @property-read WithholdReceipt[] $withholdReceipts
 */
class Withhold extends \yii\db\ActiveRecord
{
    use GetDbTrait;

    public const STATUS_LIST = [
        '新建',
        '处理中',
        '成功',
        '失败',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'withhold';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['withhold_status', 'withhold_status_stage', 'withhold_amount'], 'integer'],
            [['withhold_started_at', 'withhold_finished_at', 'withhold_created_at', 'withhold_updated_at'], 'safe'],
            [
                ['withhold_merchant_name', 'withhold_merchant_key', 'withhold_remark', 'withhold_ruleset_code'],
                'string',
                'max' => 64,
            ],
            [['withhold_card_num', 'withhold_capital'], 'string', 'max' => 32],
            [['withhold_callback', 'withhold_redirect'], 'string', 'max' => 128],
            [['withhold_operator'], 'string', 'max' => 16],
            [['withhold_ledgers'], 'string', 'max' => 255],
            [
                ['withhold_merchant_name', 'withhold_merchant_key'],
                'unique',
                'targetAttribute' => ['withhold_merchant_name', 'withhold_merchant_key'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withhold_id' => 'ID',
            'withhold_merchant_name' => '商户名',
            'withhold_merchant_key' => '商户订单号',
            'withhold_card_num' => '卡号',
            'withhold_status' => '交易状态',
            'withhold_status_stage' => '交易细分子状态：0=默认，1=通道不可用，2=用户取消订单',
            'withhold_amount' => '交易金额(元)',
            'withhold_callback' => '回调URL',
            'withhold_remark' => '备注信息',
            'withhold_capital' => '资金方',
            'withhold_operator' => '操作者',
            'withhold_started_at' => '交易发起时间',
            'withhold_finished_at' => '交易完成时间',
            'withhold_created_at' => '交易创建时间',
            'withhold_updated_at' => 'Withhold Updated At',
            'withhold_redirect' => '业务方跳转URL',
            'withhold_ruleset_code' => '支付路由规则集代码',
            'withhold_ledgers' => '分账信息',
        ];
    }

    public function getCard(): ActiveQuery
    {
        return $this->hasOne(Card::class, ['card_num' => 'withhold_card_num']);
    }

    public function getWithholdReceipts(): ActiveQuery
    {
        return $this->hasMany(WithholdReceipt::class, [
            'withhold_receipt_merchant_key' => 'withhold_merchant_key',
            'withhold_receipt_merchant_name' => 'withhold_merchant_name',
        ]);
    }

    public function getRuleset(): ActiveQuery
    {
        return $this->hasOne(Ruleset::class, ['ruleset_code' => 'withhold_ruleset_code']);
    }
}
