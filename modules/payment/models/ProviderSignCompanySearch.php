<?php

namespace payment\models;

use yii\data\ActiveDataProvider;

/**
 * ProviderSignCompanySearch represents the model behind the search form about `payment\models\ProviderSignCompany`.
 */
class ProviderSignCompanySearch extends ProviderSignCompany
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'provider_sign_company_sign_company_code',
                    'provider_sign_company_provider_code',
                    'provider_sign_company_status',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ProviderSignCompany::find()->with(['signCompany', 'provider']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => ['provider_sign_company_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'provider_sign_company_sign_company_code' => $this->provider_sign_company_sign_company_code,
            'provider_sign_company_provider_code' => $this->provider_sign_company_provider_code,
            'provider_sign_company_status' => $this->provider_sign_company_status,
        ]);

        return $dataProvider;
    }
}
