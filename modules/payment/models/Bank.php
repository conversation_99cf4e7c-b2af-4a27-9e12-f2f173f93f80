<?php

namespace payment\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "bank".
 *
 * @property int    $bank_id
 * @property string $bank_code 银行缩写
 * @property string $bank_name 银行名称
 * @property string $bank_created_at
 * @property string $bank_updated_at
 * @property string $bank_serial
 */
class Bank extends ActiveRecord
{
    use GetDbTrait;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'bank';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['bank_code', 'bank_name'], 'required'],
            [['bank_created_at', 'bank_updated_at'], 'safe'],
            [['bank_code'], 'string', 'max' => 32],
            [['bank_name'], 'string', 'max' => 128],
            [['bank_serial'], 'string', 'max' => 20],
            [['bank_code'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'bank_id' => 'Bank ID',
            'bank_code' => '银行缩写',
            'bank_name' => '银行名称',
            'bank_created_at' => 'Bank Created At',
            'bank_updated_at' => 'Bank Updated At',
            'bank_serial' => 'Bank Serial',
        ];
    }
}
