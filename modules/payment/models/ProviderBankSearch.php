<?php

namespace payment\models;

use yii\data\ActiveDataProvider;

/**
 * ProviderBankSearch represents the model behind the search form about `payment\models\ProviderBank`.
 */
class ProviderBankSearch extends ProviderBank
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'provider_bank_code',
                    'provider_bank_provider_code',
                    'provider_bank_platform_bank_code',
                    'provider_bank_product_type',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ProviderBank::find()->with(['bank', 'provider']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['provider_bank_id'],
                'defaultOrder' => ['provider_bank_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'provider_bank_code' => $this->provider_bank_code,
            'provider_bank_provider_code' => $this->provider_bank_provider_code,
            'provider_bank_platform_bank_code' => $this->provider_bank_platform_bank_code,
        ]);

        if ($this->provider_bank_product_type) {
            $query->andWhere('FIND_IN_SET(:productType, provider_bank_product_type)', [
                'productType' => $this->provider_bank_product_type,
            ]);
        }

        return $dataProvider;
    }
}
