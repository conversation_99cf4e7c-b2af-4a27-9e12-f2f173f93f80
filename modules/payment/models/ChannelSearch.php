<?php

namespace payment\models;

use yii\data\ActiveDataProvider;

/**
 * ChannelSearch represents the model behind the search form about `payment\models\Channel`.
 */
class ChannelSearch extends Channel
{
    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'channel_sign_company_code',
                    'channel_provider_code',
                    'channel_provider_product_type',
                    'channel_status',
                    'channel_name',
                    'channel_alias',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Channel::find();

        // add conditions that should always apply here
        $query->with([
            'signCompany',
            'provider',
//            'keyValue',
        ]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => [
                    'channel_id' => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'channel_sign_company_code' => $this->channel_sign_company_code,
            'channel_provider_code' => $this->channel_provider_code,
            'channel_provider_product_type' => $this->channel_provider_product_type,
            'channel_status' => $this->channel_status,
        ]);

        $query->andFilterWhere([
            'and',
            ['like', 'channel_name', $this->channel_name],
            ['like', 'channel_alias', $this->channel_alias],
        ]);

        return $dataProvider;
    }
}
