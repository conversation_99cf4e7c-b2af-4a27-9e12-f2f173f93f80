<?php

namespace payment\models;

use yii\data\ActiveDataProvider;

/**
 * ProviderProductSearch represents the model behind the search form about `payment\models\ProviderProduct`.
 */
class ProviderProductSearch extends ProviderProduct
{
    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['provider_product_sign_company_code', 'provider_product_provider_code'], 'safe'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ProviderProduct::find()->with(['signCompany', 'provider']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => ['provider_product_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'provider_product_sign_company_code' => $this->provider_product_sign_company_code,
            'provider_product_provider_code' => $this->provider_product_provider_code,
        ]);

        return $dataProvider;
    }
}
