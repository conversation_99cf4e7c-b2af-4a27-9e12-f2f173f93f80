<?php

namespace payment\models;

use Carbon\Carbon;
use kvmanager\models\KeyValue;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "provider_product".
 *
 * @property int $provider_product_id
 * @property string $provider_product_sign_company_code    签约主体
 * @property string $provider_product_provider_code        渠道编号
 * @property string $provider_product_type                 产品类型
 * @property int $provider_product_fee_type                1:按笔计费,2:比例计费
 * @property float $provider_product_fee                   费用
 * @property float $provider_product_fee_rate              费率
 * @property string $provider_product_card_type            卡类型: CC=信用卡 SCC=准贷记卡 PC=预付费卡 DC=储蓄卡
 * @property int $provider_product_fee_max                 费用最高收费
 * @property int $provider_product_fee_min                 费用最低收费
 * @property string $provider_product_fee_status           费用状态。fee：收费，nofee：免费
 * @property int $provider_product_min_amount              最小金额
 * @property int $provider_product_max_amount              最大金额
 * @property string $provider_product_settle_type          清算类型 T0 T1
 * @property string $provider_product_use_holiday_plan     是否使用节假日计划默认关闭
 * @property float $provider_product_loaning_rate          垫资费率
 * @property int $provider_product_account_type            账户类型: 1=对私 2=对公
 * @property string $provider_product_start_at             配置生效时间
 * @property string $provider_product_end_at               配置结束时间
 * @property string $provider_product_create_at            创建时间
 * @property string $provider_product_update_at            修改时间
 * @property string $provider_product_status               open：启用，close：禁用
 * @property float $provider_product_verify_fee            鉴权绑卡费用（单次）
 * @property string $provider_product_loaning_formula      垫资计算公式
 * @property string $provider_product_round_type           进位方式，before=先进位后统计，after=先统计后进位
 * @property-read SignCompany $signCompany
 * @property-read Provider $provider
 */
class ProviderProduct extends ActiveRecord
{
    use GetDbTrait;

    public const STATUS_OPEN = 'open';
    public const STATUS_CLOSE = 'close';
    public const STATUS_LIST = [
        self::STATUS_OPEN => '启用',
        self::STATUS_CLOSE => '禁用',
    ];

    public const ACCOUNT_TYPE_LIST = [
        1 => '对私',
        2 => '对公',
        3 => '转账',
    ];

    public const FEE_STATUS_LIST = [
        'fee' => '收费',
        'nofee' => '免费',
    ];

    public const CARD_TYPE_LIST = [
        'CC' => '信用卡',
        'SCC' => '准贷记卡',
        'PC' => '预付费卡',
        'DC' => '储蓄卡',
    ];

    public const FEE_TYPE_LIST = [
        1 => '按笔计费',
        2 => '比例计费',
    ];

    public const HOLIDAY_PLAN_LIST = [
        'close' => '关闭',
        'open' => '开启',
    ];

    public const SETTLE_TYPE_LIST = [
        'T0' => 'T0',
        'T1' => 'T1',
        'D0' => 'D0',
        'D1' => 'D1',
    ];

    public const LOANING_FORMULA_LIST = [
        'T0' => [
            'M*R*T' => 'T0:M*R*T',
        ],
        'T1' => [
            'M*R*(1+N)' => 'T1:M*R*(1+N)',
            'M*R*H' => 'T1:M*R*H',
        ],
        'D0' => [
            'M*R*(1+N)' => 'D0:M*R*(1+N)',
        ],
        'D1' => [
            'M*R*H' => 'D1-1:M*R*H',
            'M*R*N*H' => 'D1-2:M*R*N*H',
        ],
    ];

    public const ROUTE_TYPE_LIST = [
        'before' => '先进位后统计',
        'after' => '先统计后进位',
    ];

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'provider_product_create_at',
                'updatedAtAttribute' => 'provider_product_update_at',
                'value' => fn() => Carbon::now()->toDateTimeString(),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'provider_product';
    }

    public static function typeList(): array
    {
        static $typeList;
        if ($typeList === null) {
            $typeList = [];
            $config = KeyValue::takeAsArray('product_type', 'payment');
            foreach ($config as $group) {
                foreach ($group as $type => $option) {
                    $typeList[$type] = $option['name'] ?? '-';
                }
            }
        }

        return $typeList;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'provider_product_sign_company_code', // 签约主体
                    'provider_product_type',
                    'provider_product_account_type',
                    'provider_product_fee',
                    'provider_product_fee_min',
                    'provider_product_min_amount',
                    'provider_product_start_at',
                    'provider_product_card_type',
                    'provider_product_settle_type',
                    'provider_product_loaning_rate',
                    'provider_product_use_holiday_plan',

                    'provider_product_provider_code',  // 渠道编号
                    'provider_product_status',
                    'provider_product_fee_status',
                    'provider_product_fee_rate',
                    'provider_product_fee_max',
                    'provider_product_max_amount',
                    'provider_product_end_at',
                    'provider_product_fee_type',
//                    'provider_product_loaning_formula', // 清算公式
                    'provider_product_verify_fee',
                    'provider_product_round_type',
                ],
                'required',
            ],
            [
                [
                    'provider_product_fee_type',
                    'provider_product_fee_max',
                    'provider_product_fee_min',
                    'provider_product_min_amount',
                    'provider_product_max_amount',
                    'provider_product_account_type',
                ],
                'integer',
            ],
            [
                [
                    'provider_product_fee',
                    'provider_product_fee_rate',
                    'provider_product_loaning_rate',
                    'provider_product_verify_fee',
                ],
                'number',
            ],
            [['provider_product_fee_status', 'provider_product_use_holiday_plan', 'provider_product_status'], 'string'],
            [
                [
                    'provider_product_start_at',
                    'provider_product_end_at',
                    'provider_product_create_at',
                    'provider_product_update_at',
                ],
                'safe',
            ],
            [
                ['provider_product_sign_company_code', 'provider_product_provider_code', 'provider_product_round_type'],
                'string',
                'max' => 32,
            ],
            [['provider_product_type'], 'string', 'max' => 16],
            [['provider_product_card_type'], 'string', 'max' => 10],
            [['provider_product_settle_type'], 'string', 'max' => 2],
            [['provider_product_loaning_formula'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'provider_product_id' => 'ID',
            'provider_product_sign_company_code' => '主体',
            'provider_product_provider_code' => '渠道',
            'provider_product_type' => '产品类型',
            'provider_product_fee_type' => '费率类型',
            'provider_product_fee' => '单笔费用(元)',
            'provider_product_fee_rate' => '费率',
            'provider_product_card_type' => '卡类型',
            'provider_product_fee_max' => '最高收费(元)',
            'provider_product_fee_min' => '最低收费(元)',
            'provider_product_fee_status' => '收费状态',
            'provider_product_min_amount' => '最小金额(元)',
            'provider_product_max_amount' => '最大金额(元)',
            'provider_product_settle_type' => '清算类型',
            'provider_product_use_holiday_plan' => '节假日计划',
            'provider_product_loaning_rate' => '垫资费率',
            'provider_product_account_type' => '账户类型',
            'provider_product_start_at' => '生效时间',
            'provider_product_end_at' => '结束时间',
            'provider_product_create_at' => '创建时间',
            'provider_product_update_at' => '修改时间',
            'provider_product_status' => '状态',
            'provider_product_verify_fee' => '绑卡单笔费用(元)',
            'provider_product_loaning_formula' => '清算公式',
            'provider_product_round_type' => '进位方式',
        ];
    }

    public function getSignCompany(): ActiveQuery
    {
        return $this->hasOne(SignCompany::class, [
            'sign_company_code' => 'provider_product_sign_company_code',
        ]);
    }

    public function getProvider(): ActiveQuery
    {
        return $this->hasOne(Provider::class, ['provider_code' => 'provider_product_provider_code']);
    }
}
