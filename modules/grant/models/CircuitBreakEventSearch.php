<?php

namespace grant\models;


use yii\data\ActiveDataProvider;

class Circuit<PERSON>reakEventSearch extends CircuitBreakEvent
{
    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['circuit_break_event_record_id',], 'required'],
            [['circuit_break_event_order_no',], 'safe',]
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['circuit_break_event_id'],
                'defaultOrder' => ['circuit_break_event_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }
        $query->andWhere(['circuit_break_event_record_id' => $this->circuit_break_event_record_id])
            ->andF<PERSON>er<PERSON>here(['circuit_break_event_order_no' => $this->circuit_break_event_order_no]);

        return $dataProvider;
    }
}

