<?php

namespace grant\models;

use Yii;

/**
 * This is the model class for table "periodic_file_record".
 *
 * @property int $periodic_file_record_id
 * @property string $periodic_file_record_loan_channel 资方名称
 * @property string $periodic_file_record_period_type 周期类型daily按日
 * @property string $periodic_file_record_file_type 文件类型
 * @property string $periodic_file_record_business_date 业务日期
 * @property string $periodic_file_record_file_digest 文件摘要
 * @property int $periodic_file_record_shard_num 文件分片编号
 * @property int $periodic_file_record_confirm_flag 文件分片编号
 * @property string $periodic_file_record_push_response 推送成功响应
 * @property int $periodic_file_record_status 记录状态0.新建;1.数据加载失败;2.数据转DTO失败;3.DTO转文件失败;4.推送文件失败;5.推送文件成功;9.推送未知异常错误
 * @property string $periodic_file_record_memo 备注
 * @property string $periodic_file_record_create_at
 * @property string $periodic_file_record_update_at
 * @property int $periodic_file_record_retry_times 重试次数
 */
class PeriodicFileRecord extends \yii\db\ActiveRecord
{

    public const STATUS_NEW = 0;
    public const STATUS_LOAD_FAIL = 1;
    public const STATUS_DTO_FAIL = 2;
    public const STATUS_FILE_FAIL = 3;
    public const STATUS_PUSH_FILE_FAIL = 4;
    public const STATUS_PUSH_FILE_SUCCESS = 5;
    public const STATUS_PUSH_UNKNOWN_ERR = 9;


    public const STATUS_LIST = [
        self::STATUS_NEW => '新建',
        self::STATUS_LOAD_FAIL => '数据加载失败',
        self::STATUS_DTO_FAIL => '数据转DTO失败',
        self::STATUS_FILE_FAIL => 'DTO转文件失败',
        self::STATUS_PUSH_FILE_FAIL => '推送文件失败',
        self::STATUS_PUSH_FILE_SUCCESS => '推送文件成功',
        self::STATUS_PUSH_UNKNOWN_ERR => '推送未知异常错误',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'periodic_file_record';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbGBiz');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['periodic_file_record_business_date', 'periodic_file_record_create_at', 'periodic_file_record_update_at'], 'safe'],
            [['periodic_file_record_shard_num', 'periodic_file_record_status', 'periodic_file_record_retry_times'], 'integer'],
            [['periodic_file_record_loan_channel', 'periodic_file_record_file_digest'], 'string', 'max' => 64],
            [['periodic_file_record_period_type', 'periodic_file_record_file_type'], 'string', 'max' => 32],
            [['periodic_file_record_push_response', 'periodic_file_record_memo'], 'string', 'max' => 2048],
            [['periodic_file_record_loan_channel', 'periodic_file_record_file_type', 'periodic_file_record_business_date', 'periodic_file_record_shard_num'], 'unique', 'targetAttribute' => ['periodic_file_record_loan_channel', 'periodic_file_record_file_type', 'periodic_file_record_business_date', 'periodic_file_record_shard_num']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'periodic_file_record_id' => 'Periodic File Record ID',
            'periodic_file_record_loan_channel' => '资方名称',
            'periodic_file_record_period_type' => '周期类型',
            'periodic_file_record_file_type' => '文件类型',
            'periodic_file_record_business_date' => '业务日期',
            'periodic_file_record_file_digest' => '文件摘要',
            'periodic_file_record_shard_num' => '文件分片编号',
            'periodic_file_record_push_response' => '推送成功响应',
            'periodic_file_record_status' => '记录状态',
            'periodic_file_record_memo' => '备注',
            'periodic_file_record_create_at' => '创建时间',
            'periodic_file_record_update_at' => '结束时间',
            'periodic_file_record_retry_times' => '重试次数',
        ];
    }
}
