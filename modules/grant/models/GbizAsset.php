<?php

namespace grant\models;

use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%asset}}".
 *
 * @property int      $asset_id                       资产行号，单表主键
 * @property string   $asset_item_no                  项目编号，有一系列规则，用来标识某个项目，可以从项目编号里看到很多信息
 * @property string   $asset_actual_grant_at          实际放款时间(到卡时间)
 * @property string   $asset_grant_at                 预计放款时间
 * @property string   $asset_due_at                   资产到期日
 * @property string   $asset_payoff_at                偿清时间
 * @property string   $asset_owner                    资产所有权者(KN-快牛、STB)
 * @property string   $asset_status                   资产状态
 * @property string   $asset_loan_channel             放款渠道
 * @property int      $asset_principal_amount         合同本金
 * @property string   $asset_type                     资产类型
 * @property string   $asset_sub_type
 * @property string   $asset_period_type              还款周期类型
 * @property int      $asset_period_count             还款总期数
 * @property string   $asset_product_category         资产类别名称：14天，30天，3个月，6个月
 * @property string   $asset_cmdb_product_number      资产产品在费率系统编号
 * @property string   $asset_create_at                创建时间
 * @property string   $asset_import_at                首次进件时间
 * @property string   $asset_effect_at                起息时间
 * @property string   $asset_update_at
 * @property string   $asset_from_system
 * @property int      $asset_granted_principal_amount 实际放款本金
 * @property string   $asset_alias_name               资产名称
 * @property int      $asset_interest_amount          利息总金额
 * @property int      $asset_fee_amount               费总金额
 * @property int      $asset_balance_amount           剩余未偿还总金额(分）包括：本，息，费
 * @property int      $asset_repaid_amount            已偿还总金额
 * @property int      $asset_total_amount             本息费总额。 =principalAmt+interestAmt+feeAmt =balance_amount+repaidAmt
 * @property int|null $asset_version                  资产修改版本号, 主动修改资产时才递增版本号
 * @property float    $asset_interest_rate            利率
 * @property string   $asset_from_system_name         资产来源系统名称
 * @property string   $asset_idnum_encrypt
 * @property string   $asset_from_app                 马甲包字段
 */
class GbizAsset extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%asset}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbGBiz');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['asset_item_no', 'asset_loan_channel', 'asset_type', 'asset_sub_type'], 'required'],
            [
                [
                    'asset_actual_grant_at',
                    'asset_grant_at',
                    'asset_due_at',
                    'asset_payoff_at',
                    'asset_create_at',
                    'asset_import_at',
                    'asset_effect_at',
                    'asset_update_at',
                ],
                'safe',
            ],
            [['asset_status', 'asset_period_type'], 'string'],
            [
                [
                    'asset_principal_amount',
                    'asset_period_count',
                    'asset_granted_principal_amount',
                    'asset_interest_amount',
                    'asset_fee_amount',
                    'asset_balance_amount',
                    'asset_repaid_amount',
                    'asset_total_amount',
                    'asset_version',
                ],
                'integer',
            ],
            [['asset_interest_rate'], 'number'],
            [['asset_item_no', 'asset_cmdb_product_number'], 'string', 'max' => 48],
            [['asset_owner', 'asset_from_system'], 'string', 'max' => 24],
            [['asset_loan_channel', 'asset_type', 'asset_sub_type'], 'string', 'max' => 32],
            [['asset_product_category'], 'string', 'max' => 16],
            [['asset_alias_name'], 'string', 'max' => 64],
            [['asset_from_system_name'], 'string', 'max' => 10],
            [['asset_idnum_encrypt'], 'string', 'max' => 128],
            [['asset_from_app'], 'string', 'max' => 100],
            [['asset_item_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'asset_id'                       => '资产行号，单表主键',
            'asset_item_no'                  => '项目编号，有一系列规则，用来标识某个项目，可以从项目编号里看到很多信息',
            'asset_actual_grant_at'          => '实际放款时间(到卡时间)',
            'asset_grant_at'                 => '预计放款时间',
            'asset_due_at'                   => '资产到期日',
            'asset_payoff_at'                => '偿清时间',
            'asset_owner'                    => '资产所有权者(KN-快牛、STB)',
            'asset_status'                   => '资产状态',
            'asset_loan_channel'             => '放款渠道',
            'asset_principal_amount'         => '合同本金',
            'asset_type'                     => '资产类型',
            'asset_sub_type'                 => 'Asset Sub Type',
            'asset_period_type'              => '还款周期类型',
            'asset_period_count'             => '还款总期数',
            'asset_product_category'         => '资产类别名称：14天，30天，3个月，6个月',
            'asset_cmdb_product_number'      => '资产产品在费率系统编号',
            'asset_create_at'                => '创建时间',
            'asset_import_at'                => '首次进件时间',
            'asset_effect_at'                => '起息时间',
            'asset_update_at'                => 'Asset Update At',
            'asset_from_system'              => 'Asset From System',
            'asset_granted_principal_amount' => '实际放款本金',
            'asset_alias_name'               => '资产名称',
            'asset_interest_amount'          => '利息总金额',
            'asset_fee_amount'               => '费总金额',
            'asset_balance_amount'           => '剩余未偿还总金额(分）包括：本，息，费',
            'asset_repaid_amount'            => '已偿还总金额',
            'asset_total_amount'             => '本息费总额。 =principalAmt+interestAmt+feeAmt =balance_amount+repaidAmt',
            'asset_version'                  => '资产修改版本号, 主动修改资产时才递增版本号',
            'asset_interest_rate'            => '利率',
            'asset_from_system_name'         => '资产来源系统名称',
            'asset_idnum_encrypt'            => 'Asset Idnum Encrypt',
            'asset_from_app'                 => '马甲包字段',
        ];
    }
}
