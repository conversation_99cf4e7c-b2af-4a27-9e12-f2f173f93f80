<?php

namespace grant\models;

use Carbon\Carbon;
use yii\base\InvalidConfigException;
use yii\data\ActiveDataProvider;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "synctask".
 *
 * @property int    $synctask_id
 * @property string $synctask_type
 * @property string $synctask_key
 * @property string $synctask_from_system
 * @property string $synctask_request_data
 * @property string $synctask_response_data
 * @property string $synctask_memo
 * @property string $synctask_status
 * @property string $synctask_create_at
 * @property string $synctask_update_at
 * @property string $synctask_order_no
 */
class GbizSynctask extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'synctask';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbGBiz');
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['synctask_request_data', 'synctask_response_data', 'synctask_memo', 'synctask_status'], 'string'],
            [['synctask_create_at', 'synctask_update_at'], 'safe'],
            [['synctask_type', 'synctask_key', 'synctask_from_system'], 'string', 'max' => 50],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'synctask_id' => '任务ID',
            'synctask_type' => '任务类型',
            'synctask_key' => '任务键值',
            'synctask_from_system' => '任务来源',
            'synctask_request_data' => '任务内容',
            'synctask_response_data' => '任务结果',
            'synctask_memo' => '备注内容',
            'synctask_status' => '任务状态',
            'synctask_create_at' => '创建日期',
            'synctask_update_at' => '最后更新日期',
            'synctask_order_no' => '任务编号',
        ];
    }

    /**
     * @param array $params
     * @return ActiveDataProvider
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = self::find();

        if (!empty($params['synctaskId'])) {
            $query->andWhere(['synctask_id' => $params['synctaskId']]);
        } elseif (!empty($params['synctaskKey'])) {
            $query->andWhere(['synctask_key' => $params['synctaskKey']]);
        } else {
            if (!empty($params['startTime'])) {
                $query->andWhere(['>=', 'synctask_create_at', $params['startTime']]);
            }

            if (!empty($params['endTime'])) {
                $query->andWhere([
                    '<',
                    'synctask_create_at',
                    Carbon::parse($params['endTime'])->addDay()->floorDay()->toDateTimeString(),
                ]);
            }

            if (!empty($params['synctaskStatus'])) {
                $query->andWhere(['synctask_status' => $params['synctaskStatus']]);
            }

            if (!empty($params['synctaskId'])) {
                $query->andWhere(['synctask_id' => $params['synctaskId']]);
            }

            if (!empty($params['synctaskType'])) {
                $query->andWhere(['=', 'synctask_type', $params['synctaskType']]);
            }
        }
        $query->select(['*']);

        $query->orderBy(['synctask_id' => SORT_DESC]);

        return new ActiveDataProvider([
            'query' => $query,
        ]);
    }
}
