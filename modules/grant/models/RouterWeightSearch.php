<?php

namespace grant\models;

use yii\data\ActiveDataProvider;

/**
 * RouterWeightSearch represents the model behind the search form about `grant\models\RouterWeight`.
 */
class RouterWeightSearch extends RouterWeight
{
    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['router_weight_status'], 'default', 'value' => self::STATUS_ACTIVE],
            [
                [
                    'router_weight_desc',
                    'router_weight_status',
                    'router_weight_first_route_status',
                    'router_weight_second_route_status',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = RouterWeight::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'defaultOrder' => [
                    'router_weight_create_at' => SORT_DESC,
                    'router_weight_id'        => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query
            ->andFilterWhere([
                'router_weight_status'              => $this->router_weight_status,
                'router_weight_first_route_status'  => $this->router_weight_first_route_status,
                'router_weight_second_route_status' => $this->router_weight_second_route_status,
            ])
            ->andFilterWhere(['like', 'router_weight_desc', $this->router_weight_desc]);

        return $dataProvider;
    }
}
