<?php

namespace grant\models;

use Yii;

/**
 * This is the model class for table "router_load_record".
 *
 * @property int $router_load_record_id 主键
 * @property string $router_load_record_key 路由请求唯一标识
 * @property string $router_load_record_item_no 资产编号
 * @property string $router_load_record_rule_code 资金规则编码
 * @property int $router_load_record_principal_amount 路由金额
 * @property string $router_load_record_status 路由状态：routed-路由，imported-导入，applied-进件到资方，changed-已切资方
 * @property string|null $router_load_record_channel 资方渠道
 * @property string $router_load_record_sub_type 资产子类型, single | multiple
 * @property int $router_load_record_period_count 资产类型数量
 * @property string $router_load_record_period_type 资产周期类型, month | day
 * @property int|null $router_load_record_period_days 资产周期天数
 * @property string $router_load_record_sub_order_type 子订单类型
 * @property string $router_load_record_route_day 路由日期
 * @property string $router_load_record_from_system 资产来源系统
 * @property int|null $router_load_record_total_record_id 路由总表记录Id
 * @property string $router_load_record_create_at 创建日期
 * @property string $router_load_record_import_at 进件时间
 * @property string $router_load_record_update_at 修改时间
 * @property string|null $router_load_record_idnum 身份证号码
 * @property string $router_load_record_product_code 放款渠道业务产品code
 * @property string $router_load_record_extend_info 扩展信息
 */
class RouterLoadRecord extends \yii\db\ActiveRecord
{
    public $updateCreateAt;
    public const STATUS_ROUTED = 'routed';
    public const STATUS_IMPORTED = 'imported';
    public const STATUS_APPLIED = 'applied';
    public const STATUS_CHANGED = 'changed';
    public const STATUS_CANCED = 'canced';

    public const STATUS_LIST = [
        self::STATUS_ROUTED => '已路由',
        self::STATUS_IMPORTED => '已导入',
        self::STATUS_APPLIED => '已进件',
        self::STATUS_CHANGED => '已切资方',
        self::STATUS_CANCED => '已取消'
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'router_load_record';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbGBiz');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['router_load_record_principal_amount', 'router_load_record_period_count', 'router_load_record_period_days', 'router_load_record_total_record_id'], 'integer'],
            [['router_load_record_create_at', 'router_load_record_import_at', 'router_load_record_update_at'], 'safe'],
            [['router_load_record_key', 'router_load_record_rule_code'], 'string', 'max' => 64],
            [['router_load_record_item_no'], 'string', 'max' => 48],
            [['router_load_record_status'], 'string', 'max' => 20],
            [['router_load_record_channel', 'router_load_record_idnum'], 'string', 'max' => 32],
            [['router_load_record_sub_type', 'router_load_record_period_type', 'router_load_record_sub_order_type', 'router_load_record_from_system', 'router_load_record_product_code'], 'string', 'max' => 50],
            [['router_load_record_route_day'], 'string', 'max' => 10],
            [['router_load_record_extend_info'], 'string', 'max' => 2048],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'router_load_record_id' => '主键',
            'router_load_record_key' => '路由请求唯一标识',
            'router_load_record_item_no' => '资产编号',
            'router_load_record_rule_code' => '资金规则编码',
            'router_load_record_principal_amount' => '路由金额',
            'router_load_record_status' => '路由状态：routed-路由，imported-导入，applied-进件到资方，changed-已切资方',
            'router_load_record_channel' => '资方渠道',
            'router_load_record_sub_type' => '资产子类型, single | multiple',
            'router_load_record_period_count' => '资产类型数量',
            'router_load_record_period_type' => '资产周期类型, month | day',
            'router_load_record_period_days' => '资产周期天数',
            'router_load_record_sub_order_type' => '子订单类型',
            'router_load_record_route_day' => '路由日期',
            'router_load_record_from_system' => '资产来源系统',
            'router_load_record_total_record_id' => '路由总表记录Id',
            'router_load_record_create_at' => '创建日期',
            'router_load_record_import_at' => '进件时间',
            'router_load_record_update_at' => '修改时间',
            'router_load_record_idnum' => '身份证号码',
            'router_load_record_product_code' => '放款渠道业务产品code',
            'router_load_record_extend_info' => '扩展信息',
        ];
    }
}
