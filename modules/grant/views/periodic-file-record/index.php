<?php

use common\models\Asset;
use grant\models\PeriodicFileRecord;
use grant\models\PeriodicFileRecordSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ActiveDataProvider;
use yii\grid\CheckboxColumn;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $searchModel PeriodicFileRecordSearch */
/* @var $dataProvider ActiveDataProvider */

$this->title = '日终文件推送管理';
$this->params['breadcrumbs'][] = $this->title;
?>

    <div class="box box-default search">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa fa-search"></i>
                条件筛选
            </div>
        </div>
        <div class="box-body">
            <?php
            $form = ActiveForm::begin([
                'action' => [''],
                'method' => 'get',
                'type' => ActiveForm::TYPE_INLINE,
                'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
            ]) ?>
            <?= $form->field($searchModel, 'periodic_file_record_loan_channel')->widget(Select2::class, [
                'data' => Asset::channelList(true),
                'pluginOptions' => [
                    'allowClear' => true,
                    'width' => '200px',
                ],
                'options' => [
                    'prompt' => $searchModel->getAttributeLabel('periodic_file_record_loan_channel'),
                ],
            ]) ?>

            <?= $form->field($searchModel, 'periodic_file_record_file_type') ?>

            <?= $form->field($searchModel, 'periodic_file_record_status')->widget(Select2::class, [
                'data' => PeriodicFileRecord::STATUS_LIST,
                'hideSearch' => true,
                'pluginOptions' => [
                    'allowClear' => true,
                    'width' => '200px',
                ],
                'options' => [
                    'prompt' => '状态',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'createStartDate')->widget(DatePicker::class) ?>

            <?= $form->field($searchModel, 'createEndDate')->widget(DatePicker::class) ?>

            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

            <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']) ?>

            <?php
            ActiveForm::end() ?>
        </div>
    </div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => CheckboxColumn::class,
            'checkboxOptions' => function (PeriodicFileRecord $model) {
                $flag = $model->periodic_file_record_status !== PeriodicFileRecord::STATUS_PUSH_FILE_SUCCESS;

                return [
                    'disabled' => $flag,
                    'style' => 'display:' . ($flag ? 'none' : 'block'),
                ];
            },
        ],
        [
            'class' => DialogActionColumn::class,
            'template' => '{reset} {view} ',
            'header' => Html::button('批量重推', [
                'class' => 'btn btn-xs btn-danger batch-reset',
            ]),
            'buttons' => [
                'reset' => static function ($url, PeriodicFileRecord $model) {
                    return Html::button('重新推送', [
                        'class' => 'btn btn-xs btn-primary reset-btn',
                        'data' => [
                            'id' => $model->periodic_file_record_id
                        ]
                    ]);
                },
            ],
            'visibleButtons' => [
                'reset' => static function (PeriodicFileRecord $model) {
                    return $model->periodic_file_record_status === PeriodicFileRecord::STATUS_PUSH_FILE_SUCCESS;
                },
            ]
        ],
        'periodic_file_record_loan_channel',
        'periodic_file_record_period_type',
        'periodic_file_record_file_type',
        'periodic_file_record_business_date',
        [
            'attribute' => 'periodic_file_record_status',
            'format' => ['in', PeriodicFileRecord::STATUS_LIST],
        ],
        'periodic_file_record_retry_times',
        'periodic_file_record_memo',
        'periodic_file_record_create_at',
    ],
]);
$resetUrl = Url::to(['batch-reset']);
$js = <<<JS
    $(document)
        .on('click', 'button.reset-btn', function (e) {
            const id = e.target.getAttribute("data-id")
            resetRequest([id])
        })
        .on('click', 'button.batch-reset', function () {
            const ids = $('[name=\'selection[]\']:checked').map(function () { 
                return $(this).val(); 
            }).get();
            
            if (!ids.length) {
                layer.alert('请先勾选需要修改的数据!');
            } else {
                resetRequest(ids)
            }
        });

    function resetRequest(ids) {
        layer.confirm('重置记录状态后，定时任务会重新捞取并执行推送,确认继续?', {
            title: '确认重置',
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.post('{$resetUrl}', { ids: ids.join(',') }, function (data, status) {
                if (data.code === 0) {
                    layer.msg(data.message);
                     setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                } else {
                    layer.alert(data.message);
                }
            }).fail(function () {
               layer.alert('网络请求错误,请稍候再试!');
          });
        });
    }
JS;

$this->registerJs($js);


