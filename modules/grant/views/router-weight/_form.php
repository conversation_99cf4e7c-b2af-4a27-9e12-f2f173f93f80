<?php

use grant\models\RouterWeight;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model RouterWeight */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'router_weight_code')->textInput([
        'maxlength' => true,
    ]) ?>

    <?= $form->field($model, 'router_weight_type')->widget(Select2::class, [
        'data'       => RouterWeight::types(),
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'router_weight_desc')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'router_weight_rule_content')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode' => CodeEditor::MODE_Text,
        ],
    ]) ?>

    <?= $form->field($model, 'router_weight_value')->textInput() ?>

    <?= $form->field($model, 'router_weight_status')->widget(Select2::class, [
        'data'       => RouterWeight::status(),
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'router_weight_first_route_status')->widget(Select2::class, [
        'data'       => RouterWeight::firstRouteStatus(),
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'router_weight_second_route_status')->widget(Select2::class, [
        'data'       => RouterWeight::secondRouteStatus(),
        'hideSearch' => true,
    ]) ?>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
