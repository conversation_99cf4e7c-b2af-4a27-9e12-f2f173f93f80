<?php

use grant\models\RouterWeight;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model grant\models\RouterWeight */

$this->title                   = $model->router_weight_id;
$this->params['breadcrumbs'][] = ['label' => '权重管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('编辑', ['update', 'id' => $model->router_weight_id], ['class' => 'btn btn-primary']) ?>
    <?= Html::a('返回列表', ['index'], ['class' => 'btn btn-default']) ?>
</p>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model'      => $model,
            'options'    => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                [
                    'attribute'      => 'router_weight_desc',
                    'captionOptions' => [
                        'style' => 'width: 100px',
                    ],
                ],
                'router_weight_type',
                'router_weight_code',
                'router_weight_rule_content:ntext',
                'router_weight_value',
                [
                    'attribute' => 'router_weight_status',
                    'format'    => ['in', RouterWeight::status()],
                ],
                [
                    'attribute' => 'router_weight_first_route_status',
                    'format'    => ['in', RouterWeight::status()],
                ],
                [
                    'attribute' => 'router_weight_second_route_status',
                    'format'    => ['in', RouterWeight::status()],
                ],
                'router_weight_create_name',
                'router_weight_update_name',
                'router_weight_create_at',
                'router_weight_update_at',
            ],
        ]) ?>
    </div>
</div>
