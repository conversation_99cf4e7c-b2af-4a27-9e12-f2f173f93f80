<?php

use grant\models\CircuitBreakAction;
use grant\models\CircuitBreakRecord;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\helpers\StringHelper;
use yii\helpers\Url;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model CircuitBreakRecord */

$this->title = '查看';

?>
    <div class="box box-info">
        <div class="box-header with-border">
            <div class="box-title">详情</div>
        </div>
        <div class="box-body no-padding">
            <?= DetailView::widget([
                'model' => $model,
                'options' => [
                    'class' => 'table table-striped',
                ],
                'attributes' => [
                    'circuit_break_record_id',
                    'circuit_break_record_name',
                    'circuit_break_record_status',
                    'circuit_break_record_trigger_rule',
                    'circuit_break_record_create_at',
                    'circuit_break_record_update_at',
                    [
                        'attribute' => 'circuit_break_record_data_snapshot',
                        'format' => 'raw',
                        'value' => CodeEditor::widget([
                            'name' => 'circuit_break_record_data_snapshot',
                            'value' => $model->circuit_break_record_data_snapshot,
                            'clientOptions' => [
                                'readOnly' => true,
                                'mode' => CodeEditor::MODE_JSON,
                                'maxLines' => 40,
                            ],
                        ]),
                    ],
                ],
            ]) ?>
        </div>
    </div>

<?= GridView::widget([
    'layout' => '<div class="box-header with-border"><div class="box-title">熔断action列表</div></div><div class="box-body table-responsive no-padding">{items}</div>',
    'dataProvider' => new ActiveDataProvider([
        'query' => $model->getActions(),
        'sort' => [
            'attributes' => ['circuit_break_action_id'],
            'defaultOrder' => ['circuit_break_action_id' => SORT_DESC],
        ],
        'pagination' => false,
    ]),
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{view}',
            'buttons' => [
                'view' => static function ($url, CircuitBreakAction $model) {
                    $url = Url::to(['circuit-break-action/view', 'id' => $model->circuit_break_action_id]);
                    return ActionColumn::newButton('查看', $url, [
                        'class' => 'btn-facebook layer-dialog',
                    ]);
                },
            ]
        ],
        'circuit_break_action_type',
        'circuit_break_action_status',
        [
            'label' => '挂起条件/告警内容',
            'attribute' => 'circuit_break_action_data',
            'format' => static function ($val) {
                return Html::tag('span', StringHelper::truncate($val, 20), [
                    'title' => $val
                ]);
            },
        ],
        [
            'label' => '熔断Action配置',
            'attribute' => 'circuit_break_action_config',
            'format' => static function ($val) {
                $val = (string)$val;
                if ($data = json_decode($val)) {
                    return Html::tag('span', StringHelper::truncate($val, 30), [
                        'title' => json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
                    ]);
                }

                return $val;
            },
        ],
        'circuit_break_action_create_at',
        'circuit_break_action_update_at',
        'circuit_break_action_memo',
    ],
]) ?>