<?php

use grant\models\CircuitBreakEvent;
use grant\models\CircuitBreakEventSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\helpers\StringHelper;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel CircuitBreakEventSearch */

$this->title = '熔断事件列表';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{close} ',
            'visibleButtons' => [
                'close' => static function (CircuitBreakEvent $model) {
                    return $model->circuit_break_event_status === 'open';
                },
            ],
            'buttons' => [
                'close' => static function ($url, CircuitBreakEvent $model) {
                    return Html::button('关闭', [
                        'class' => 'btn btn-xs btn-primary close-btn',
                        'data' => [
                            'id' => $model->circuit_break_event_id
                        ]
                    ]);
                },
            ],
        ],

        'circuit_break_event_type',
        'circuit_break_event_order_no',
        'circuit_break_event_status',
        'circuit_break_event_fire_at',
        'circuit_break_event_create_at',
        'circuit_break_event_update_at',
        'circuit_break_event_memo',
        [
            'label' => '熔断事件原始数据',
            'attribute' => 'circuit_break_event_data',
            'format' => static function ($val) {
                return Html::tag('span', StringHelper::truncate($val, 20), [
                    'title' => $val
                ]);
            },
        ],
    ],
]);
$url = Url::to(['close']);

$js = <<<JS
    $(document)
        .on('click', 'button.close-btn', function (e) {
            const id = e.target.getAttribute("data-id")
            layer.confirm('是否确认关闭事件?', {
                btn: ['确定', '取消'] //按钮
            }, function () {
                $.post('{$url}', { id: id }, function (data, status) {
                    if (data.code === 0) {
                        layer.msg(data.message);
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        layer.alert(data.message);
                    }
                }).fail(function () {
                   layer.alert('网络请求错误,请稍候再试!');
              });
            });
        });
JS;

$this->registerJs($js);
