<?php

use common\models\Asset;
use dashboard\grid\MoneyTotalDataColumn;
use dashboard\grid\TotalDataColumn;
use grant\models\GbizAssetLoanRecordSearch;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel GbizAssetLoanRecordSearch */

$this->title = '资金方放款原因统计';

$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'label'     => '资金方',
            'attribute' => 'channel',
            'format'    => ['in', Asset::channelList(), '-'],
            'footer'    => '合计',
        ],
        [
            'label'     => '笔数',
            'attribute' => 'count',
            'class'     => TotalDataColumn::class,
        ],
        [
            'label'     => '金额(元)',
            'class'     => MoneyTotalDataColumn::class,
            'attribute' => 'pushAmount',
        ],
        [
            'attribute' => 'memo',
            'label'     => '原因',
            'footer'    => '-',
        ],
    ],
]);
