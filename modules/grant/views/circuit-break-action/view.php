<?php

use grant\models\CircuitBreakAction;
use xlerr\CodeEditor\CodeEditor;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model CircuitBreakAction */

$this->title = $model->circuit_break_action_id;
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">熔断Action详情</div>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'circuit_break_action_type',
                'circuit_break_action_status',
                [
                    'attribute' => 'circuit_break_action_data',
                    'format' => 'raw',
                    'value' => CodeEditor::widget([
                        'name' => 'circuit_break_action_data',
                        'value' => $model->circuit_break_action_data,
                        'clientOptions' => [
                            'readOnly' => true,
                            'mode' => CodeEditor::MODE_Text,
                            'maxLines' => 20,
                        ],
                    ]),
                ],
                [
                    'attribute' => 'circuit_break_action_config',
                    'format' => 'raw',
                    'value' => CodeEditor::widget([
                        'name' => 'circuit_break_action_config',
                        'value' => $model->circuit_break_action_config,
                        'clientOptions' => [
                            'readOnly' => true,
                            'mode' => CodeEditor::MODE_JSON,
                            'minLines' => 5,
                        ],
                    ]),
                ],
                'circuit_break_action_create_at',
                'circuit_break_action_update_at'
            ],
            'template' => "<tr><th width='10%'>{label}</th><td>{value}</td></tr>",
        ]) ?>
    </div>
</div>
