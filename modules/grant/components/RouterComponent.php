<?php

namespace grant\components;

use grant\models\RouterCapitalPlan;
use grant\models\RouterCapitalRule;
use grant\models\RouterWeight;
use GuzzleHttp\RequestOptions;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;

class RouterComponent extends RequestClient
{
    use ComponentTrait;

    public $fromSystem = 'BIZ';

    /**
     * @param string $prefix
     *
     * @return string
     */
    protected function generateUniqid($prefix = '')
    {
        return $prefix . hash('crc32', uniqid() . microtime());
    }

    /**
     * 创建或修改权重
     *
     * @param RouterWeight $weight
     *
     * @return bool
     */
    public function weightSaveOrUpdate(RouterWeight $weight)
    {
        return $this->post('router-base/weight-config/saveOrUpdate', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'type'        => 'SaveOrUpdateWeight',
                'key'         => $this->generateUniqid('SaveOrUpdateWeight'),
                'data'        => [
                    'id'                  => $weight->router_weight_id,
                    'code'                => $weight->router_weight_code,
                    'desc'                => $weight->router_weight_desc,
                    'type'                => $weight->router_weight_type,
                    'value'               => (int)$weight->router_weight_value,
                    'rule_content'        => $weight->router_weight_rule_content,
                    'status'              => $weight->router_weight_status,
                    'first_route_status'  => $weight->router_weight_first_route_status,
                    'second_route_status' => $weight->router_weight_second_route_status,
                    'create_at'           => $weight->router_weight_create_at,
                    'update_at'           => $weight->router_weight_update_at,
                    'create_name'         => $weight->router_weight_create_name,
                    'update_name'         => $weight->router_weight_update_name,
                ],
            ],
        ]);
    }

    /**
     * 创建或修改资金规则
     *
     * @param RouterCapitalRule $rule
     *
     * @return bool
     */
    public function ruleSaveOrUpdate(RouterCapitalRule $rule)
    {
        return $this->post('router-base/capital-rule/saveOrUpdate', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'type'        => 'SaveOrUpdateRule',
                'key'         => $this->generateUniqid('SaveOrUpdateRule'),
                'data'        => [
                    'rule_code'                => $rule->router_capital_rule_code,
                    'rule_content'             => $rule->router_capital_rule_content,
                    'rule_create_at'           => $rule->router_capital_rule_create_at,
                    'rule_desc'                => $rule->router_capital_rule_desc,
                    'rule_family'              => $rule->router_capital_rule_family,
                    'rule_status'              => $rule->router_capital_rule_status,
                    'rule_type'                => $rule->router_capital_rule_type,
                    'rule_update_at'           => $rule->router_capital_rule_update_at,
                    'rule_weight'              => (int)$rule->router_capital_rule_weight,
                    'rule_sort'                => (int)$rule->router_capital_rule_sort,
                    'rule_allow_overflow_rate' => (float)$rule->router_capital_rule_allow_overflow_rate,
                    'rule_activation_group'    => $rule->router_capital_rule_activation_group,
                    'rule_limit_type'          => $rule->router_capital_rule_limit_type,
                    'product_code'             => $rule->router_capital_rule_product_code,
                ],
            ],
        ]);
    }

    /**
     * 创建或修改资金计划
     *
     * @param RouterCapitalPlan $plan
     *
     * @return bool
     */
    public function planSaveOrUpdate(RouterCapitalPlan $plan)
    {
        return $this->post('router-base/capital-plan/saveOrUpdate', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'type'        => 'SaveOrUpdatePlan',
                'key'         => $this->generateUniqid('SaveOrUpdatePlan'),
                'data'        => [
                    'create_at'   => $plan->router_capital_plan_create_at,
                    'update_at'   => $plan->router_capital_plan_update_at,
                    'plan_amount' => (int)$plan->router_capital_plan_amount,
                    'plan_date'   => $plan->router_capital_plan_date,
                    'plan_desc'   => $plan->router_capital_plan_desc,
                    'plan_label'  => $plan->router_capital_plan_label,
                    'update_user' => $plan->operator->username,
                ],
            ],
        ]);
    }
}
