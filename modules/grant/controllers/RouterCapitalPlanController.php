<?php

namespace grant\controllers;

use grant\models\RouterCapitalPlan;
use grant\models\RouterCapitalRule;
use Throwable;
use Yii;
use yii\db\Expression;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\Response;

/**
 * RouterCapitalPlanController implements the CRUD actions for RouterCapitalPlan model.
 */
class RouterCapitalPlanController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'save' => ['POST'],
                ],
            ],
        ];
    }

    public function actionIndex(): string
    {
        $plans = RouterCapitalPlan::find()
            ->where(['>=', 'router_capital_plan_date', new Expression('date(now())')])
            ->select([
                'date'   => 'router_capital_plan_date',
                'code'   => 'router_capital_plan_label',
                'amount' => 'router_capital_plan_amount',
                'memo'   => 'router_capital_plan_update_memo',
            ])
            ->asArray()
            ->all();

        $memo = ArrayHelper::map($plans, 'date', 'memo', 'code');

        $plans = ArrayHelper::map($plans, 'date', function ($data) {
            return ($data['amount'] ?? 0) / 100 / 10000;
        }, 'code');

        $rules = RouterCapitalRule::find()
            ->where([
                'router_capital_rule_status' => [
                    RouterCapitalRule::STATUS_DRAFT,
                    RouterCapitalRule::STATUS_RELEASE,
                ],
            ])
            ->select([
                'code'   => 'router_capital_rule_code',
                'desc'   => 'router_capital_rule_desc',
                'family' => 'router_capital_rule_family',
            ])
            ->orderBy([
                'router_capital_rule_sort' => SORT_ASC,
            ])
            ->asArray()
            ->all();

        $rules    = ArrayHelper::map($rules, 'code', 'desc', 'family');
        $ruleList = array_keys($rules);
        $ruleList = array_combine($ruleList, $ruleList);

        return $this->render('index', [
            'rules'     => $rules,
            'ruleList'  => $ruleList,
            'plans'     => $plans,
            'memo'      => $memo,
            'curFamily' => Yii::$app->getRequest()->get('family'),
        ]);
    }

    /**
     * @return Response
     */
    public function actionSave(): Response
    {
        $request  = Yii::$app->getRequest();
        $session  = Yii::$app->getSession();
        $post     = (array)$request->post('values');
        $hasError = false;
        foreach ($post as $date => $group) {
            foreach ($group as $code => $amount) {
                if (!is_numeric($amount)) {
                    continue;
                }

                try {
                    RouterCapitalPlan::savePlan($code, $date, (float)$amount);
                } catch (Throwable $e) {
                    $hasError = true;
                    $session->setFlash('error', $e->getMessage());
                }
            }
        }

        if (!$hasError) {
            $session->setFlash('success', '保存成功');
        }

        return $this->redirect(['index', 'family' => $request->post('family')]);
    }
}
