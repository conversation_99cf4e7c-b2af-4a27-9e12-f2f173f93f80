<?php

namespace grant\controllers;

use grant\models\CircuitBreakAction;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

class CircuitBreakActionController extends Controller
{
    /**
     * @param $id
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * @param string $id
     * @return CircuitBreakAction
     * @throws NotFoundHttpException
     */
    protected function findModel(string $id): CircuitBreakAction
    {
        if (($model = CircuitBreakAction::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}