<?php

namespace grant\controllers;

use grant\models\PeriodicFileRecord;
use grant\models\PeriodicFileRecordSearch;
use Throwable;
use yii\base\UserException;
use yii\db\Transaction;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class PeriodicFileRecordController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
            ],
        ];
    }

    public function actionIndex(): string
    {
        $searchModel = new PeriodicFileRecordSearch();
        $dataProvider = $searchModel->search($this->request->get());

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @param $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * @throws NotFoundHttpException
     */
    protected function findModel(int $id): PeriodicFileRecord
    {
        if (($model = PeriodicFileRecord::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }


    public function actionBatchReset(): Response
    {
        $ids = $this->request->post('ids');
        $ids = explode(',', $ids);
        if (empty($ids)) {
            return $this->asJson([
                'code' => -1,
                'message' => '请选择需要重置的记录!',
            ]);
        }


        /** @var Transaction $transaction */
        $transaction = PeriodicFileRecord::getDb()->beginTransaction();
        try {
            $affects = PeriodicFileRecord::updateAll(
                [
                    'periodic_file_record_status' => PeriodicFileRecord::STATUS_NEW,
                    'periodic_file_record_push_response' => '',
                    'periodic_file_record_memo' => '',
                ],
                [
                    'periodic_file_record_id' => $ids,
                    'periodic_file_record_status' => array_keys(PeriodicFileRecord::STATUS_LIST),
                ]
            );

            if ($affects !== count($ids)) {
                throw new UserException('重置失败,请稍候再试!');
            }

            $transaction->commit();

            return $this->asJson([
                'code' => 0,
                'message' => '重置成功!',
            ]);
        } catch (Throwable $exception) {
            $transaction->rollBack();

            return $this->asJson([
                'code' => -1,
                'message' => $exception->getMessage(),
            ]);
        }
    }

    public function actionBatchUpdate($id): Response
    {
        if (empty($id)) {
            return $this->asJson([
                'code' => 1,
                'message' => '缺少参数',
            ]);
        }

        $affects = PeriodicFileRecord::updateAll([
            'periodic_file_record_confirm_flag' => 1,
        ], [
            'periodic_file_record_id' => $id,
            'periodic_file_record_status' => PeriodicFileRecord::STATUS_PUSH_FILE_SUCCESS,
        ]);

        if ($affects !== 1) {
            return $this->asJson([
                'code' => 1,
                'message' => '修改失败, 状态不为成功不允许修改.',
            ]);
        }

        return $this->asJson([
            'code' => 0,
            'message' => '操作成功',
        ]);
    }
}
