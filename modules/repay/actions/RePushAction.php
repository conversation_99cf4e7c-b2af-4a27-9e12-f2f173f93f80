<?php

namespace repay\actions;

use repay\components\CentralComponent;
use repay\models\operate\RePush;
use Yii;
use yii\base\Action;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class RePushAction extends Action
{
    public function run(): string
    {
        $model = new RePush();
        $request = $this->controller->request;
        $session = Yii::$app->getSession();
        if ($request->isPost && $model->load($request->post()) && $model->validate()) {
            $trans = array_values((array)Json::decode($model->content));
            foreach ($trans as &$row) {
                if (isset($row['itemNo'], $row['periodStart'], $row['periodEnd'])) {
                    $row = ArrayHelper::filter($row, [
                        'itemNo',
                        'periodStart',
                        'periodEnd',
                    ]);
                } else {
                    $trans = false;
                    break;
                }
            }
            if ($trans === false) {
                $session->addFlash('error', '参数格式错误');
            } else {
                $client = CentralComponent::instance();
                if (!$client->rePush($trans)) {
                    $session->setFlash('error', $client->getError());
                } else {
                    $model = new RePush();
                    $session->setFlash('success', '操作成功');
                }
            }
        }

        return $this->controller->render('re-push', [
            'model' => $model,
        ]);
    }
}
