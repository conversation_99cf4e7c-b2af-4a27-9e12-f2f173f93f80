<?php

namespace repay\actions;

use repay\components\RepayComponent;
use repay\models\operate\WithholdWithoutProject;
use Yii;
use yii\base\Action;

class  WithholdWithoutProjectAction extends Action
{
    public function run(): string
    {
        $model   = new WithholdWithoutProject();
        $request = $this->controller->request;
        $session = Yii::$app->getSession();
        if (Yii::$app->request->isPost) {
            if ($model->load($request->post()) && $model->validate()) {
                $client = RepayComponent::instance();
                if (!$client->withholdWithoutProject($model)) {
                    $session->setFlash('error', $client->getError());
                } else {
                    $model = new WithholdWithoutProject();
                    $session->setFlash('success', '操作成功');
                }
            }
        }

        return $this->controller->render('withhold-without-project', [
            'model' => $model,
        ]);
    }
}
