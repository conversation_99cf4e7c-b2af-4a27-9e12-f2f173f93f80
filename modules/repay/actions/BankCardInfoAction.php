<?php

namespace repay\actions;

use repay\models\Card;
use xlerr\desensitise\Desensitise;
use yii\base\Action;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\web\Response;

use function xlerr\desensitise\decrypt;
use function xlerr\desensitise\encrypt;

/**
 * 根据银行卡号获取银行卡信息
 */
class BankCardInfoAction extends Action
{
    /**
     * @return array
     * @throws InvalidConfigException
     * @throws UserException
     */
    public function run()
    {
        $this->controller->response->format = Response::FORMAT_JSON;

        $cardNum = trim($this->controller->request->get('cardNum'));
        if (empty($cardNum)) {
            return [
                'code' => 1,
                'msg' => '缺少参数: cardNum',
            ];
        }

        $cardNumEncrypt = encrypt($cardNum, Desensitise::TYPE_BANK_CARD_NUMBER)->hash;
        /**
         * @var array{username:string,idNum:string,cardNum:string,mobile:string,bankCode:string,bankName:string} $cardInfo
         */
        $cardInfo = Card::find()->where(['card_acc_num_encrypt' => $cardNumEncrypt])->select([
            'username' => 'card_acc_name_encrypt',
            'idNum' => 'card_acc_id_num_encrypt',
            'cardNum' => 'card_acc_num_encrypt',
            'mobile' => 'card_acc_tel_encrypt',
            'bankCode' => 'card_bank_code',
            'bankName' => 'card_bank_name',
        ])->asArray()->one();

        if (empty($cardInfo)) {
            return [
                'code' => 1,
                'msg' => '未查询到银行卡信息, 卡号: ' . $cardNum,
            ];
        }

        foreach (['username', 'idNum', 'cardNum', 'mobile'] as $attr) {
            if (!empty($cardInfo[$attr])) {
                $cardInfo[$attr] = decrypt($cardInfo[$attr], true);
            }
        }

        return [
            'code' => 0,
            'msg' => 'ok',
            'data' => $cardInfo,
        ];
    }
}
