<?php

namespace repay\controllers;

use common\models\Asset;
use repay\models\offlinerefund\OfflineRefundCashRecharge;
use repay\models\offlinerefund\OfflineRefundFinancialFlow;
use repay\models\offlinerefund\OfflineRefundNonAsset;
use repay\models\offlinerefund\OfflineRefundNonCashRecharge;
use repay\models\offlinerefund\QsqOfflineRepay;
use repay\tasks\QsqOfflineWithhold;
use Throwable;
use xlerr\common\helpers\MoneyHelper;
use Yii;
use yii\web\Controller;
use yii\web\Response;
use yii\web\UploadedFile;
use function xlerr\adminlte\userFullName;
use function xlerr\desensitise\batchDecrypt;

class OfflineRefundController extends Controller
{
    /**
     * 上传流水
     *
     * @return string
     */
    public function actionFinancialFlow(): string
    {
        $model = new OfflineRefundFinancialFlow();
        if (Yii::$app->request->isPost) {
            $model = new OfflineRefundFinancialFlow();
            $model->file = UploadedFile::getInstance($model, 'file');
            $params = Yii::$app->request->post();
            unset($params['file']);
            $model->load($params);
            if ($model->uploadFile()) {
                Yii::$app->session->setFlash('success', '上传成功!');
            }
        }

        return $this->render('financial-flow', [
            'model' => $model,
        ]);
    }

    /**
     * 现金贷线下充值还款
     *
     * @return Response|string
     */
    public function actionCashRecharge()
    {
        $model = new OfflineRefundCashRecharge();
        if (Yii::$app->request->isGet) {
            $model->itemNo = Yii::$app->request->get('itemNo') ?? '';
            $model->outstandingAmount = OfflineRefundCashRecharge::outstandingAmount($model->itemNo);
            $asset = OfflineRefundCashRecharge::asset($model->itemNo);
            $model->name = $asset->individualBorrow->individual_name ?? '';
            $model->idNum = $asset->individualBorrow->individual_idnum ?? '';
        }
        if (Yii::$app->request->isPost) {
            $params = Yii::$app->request->post();
            $model->load($params);
            if ($model->submit($params)) {
                Yii::$app->session->setFlash('success', '提交成功!');

                return $this->redirect(['cash-recharge']);
            }
        }

        return $this->render('cash-recharge', [
            'model' => $model,
        ]);
    }

    /**
     * @return string|Response
     */
    public function actionNonCashRecharge()
    {
        $model = new OfflineRefundNonCashRecharge();

        if ($this->request->getIsPost()) {
            $session = Yii::$app->getSession();
            try {
                if ($model->submit($this->request->post())) {
                    $session->setFlash('success', '提交成功!');

                    return $this->redirect(['non-cash-recharge']);
                }
            } catch (Throwable $e) {
                $session->setFlash('error', $e->getMessage());
            }
        }

        return $this->render('non-cash-recharge', [
            'model' => $model,
        ]);
    }

    public function actionQsqOfflineRepay()
    {
        $model = new QsqOfflineRepay();

        if ($this->request->getIsPost()) {
            $session = Yii::$app->getSession();
            try {
                if ($model->submit($this->request->post())) {
                    QsqOfflineWithhold::make([
                        'batchNo' => $model->batchNo,
                        'owner' => $model->owner,
                        'operator' => userFullName()
                    ]);
                    $session->setFlash('success', '提交成功!');

                    return $this->redirect(['qsq-offline-repay']);
                }
            } catch (Throwable $e) {
                $session->setFlash('error', $e->getMessage());
            }
        }
        return $this->render('qsq-offline-repay', [
            'model' => $model,
        ]);
    }

    /**
     * @return Response|string
     */
    public function actionNonAssetRecharge()
    {
        $model = new OfflineRefundNonAsset();

        if (Yii::$app->request->isPost) {
            $params = Yii::$app->request->post();
            if ($model->submit($params)) {
                Yii::$app->session->setFlash('success', '提交成功!');

                return $this->redirect(['non-asset-recharge']);
            }
        }

        return $this->render('non-asset', [
            'model' => $model,
        ]);
    }


    /**
     * @return array
     */
    public function actionAsset(): array
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $params = Yii::$app->request->queryParams;
        if (empty($params['itemNo'])) {
            return [];
        }
        if (OfflineRefundCashRecharge::asset($params['itemNo']) !== null) {
            $results = [
                [
                    'id' => $params['itemNo'],
                    'text' => $params['itemNo'],
                ],
            ];

            return ['results' => $results];
        }

        return ['results' => []];
    }

    public function actionUser(): array
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $params = Yii::$app->request->post();
        if (empty($params['itemNo'])) {
            return [
                'code' => 1,
                'msg' => '资产编号不能为空!',
                'data' => [
                ],
            ];
        }
        $asset = OfflineRefundCashRecharge::asset($params['itemNo']);
        if ($asset && $outstandingAmount = OfflineRefundCashRecharge::outstandingAmount($params['itemNo'])) {
            return [
                'code' => 0,
                'data' => [
                    'outstandingAmount' => (int)$outstandingAmount,
                    'name' => $asset->individualBorrow->individual_name,
                    'idNum' => $asset->individualBorrow->individual_idnum,
                ],
                'msg' => '获取成功',
            ];
        }

        return [
            'code' => 1,
            'msg' => '资产无任何信息',
            'data' => [
            ],
        ];
    }

    public function actionAssetsWithQsq(): array
    {
        $this->response->format = Response::FORMAT_JSON;

        $itemNos = $this->request->post('itemNos');

        if (empty($itemNos)) {
            return [
                'code' => 1,
                'msg' => '资产编号不能为空!',
                'data' => [],
            ];
        }

        $existAssetsCount = array_count_values(array_column($itemNos, 'assetItemNo'));
        $itemNoLists = array_map('strval', array_keys($existAssetsCount));

        /**
         * @var array{'asset_item_no':Asset} $assets
         */
        $assets = Asset::find()
            ->where(['asset_item_no' => $itemNoLists])
            ->indexBy('asset_item_no')
            ->all();

        $notAsset = array_diff($itemNoLists, array_keys($assets));

        $data = [];
        foreach ($itemNos as $asset) {
            if (!in_array($asset['assetItemNo'], $notAsset)) {
                $arr = [
                    'assetItemNo' => $assets[$asset['assetItemNo']]->asset_item_no,
                    'idNum' => $assets[$asset['assetItemNo']]->individualBorrow->individual_idnum_encrypt ?? null,
                    'name' => $assets[$asset['assetItemNo']]->individualBorrow->individual_name_encrypt ?? null,
                    'status' => $assets[$asset['assetItemNo']]->asset_status === Asset::STATUS_REPAY ? 1 : -1,
                    'repayAmount' => $asset['repayAmount'],
                    'repayDate' => $asset['repayDate'],
                ];
                $data['assets'][] = $arr;
            } else {
                $arr = [
                    'assetItemNo' => $asset['assetItemNo'],
                    'repayAmount' => $asset['repayAmount'],
                    'repayDate' => $asset['repayDate'],
                    'status' => -1,
                    'idNum' => null,
                    'name' => null,
                ];
                $data['not_assets'][] = $arr;
            }
        }

        /**
         * @var array{'asset_item_no':string} $idNumDecrypt
         */
        $idNumDecrypt = batchDecrypt(array_column($data['assets'] ?? [], 'idNum', 'assetItemNo'), true);
        /**
         * @var array{'asset_item_no':string} $nameDecrypt
         */
        $nameDecrypt = batchDecrypt(array_column($data['assets'] ?? [], 'name', 'assetItemNo'), true);

        $data = array_map(function ($item) use ($idNumDecrypt, $nameDecrypt, $existAssetsCount) {
            $item['idNum'] = $idNumDecrypt[$item['idNum']] ?? '';
            $item['name'] = $nameDecrypt[$item['name']] ?? '';
            $item['err'] = $existAssetsCount[$item['assetItemNo']] ?? 1;
            return $item;
        }, array_merge($data['assets'] ?? [], $data['not_assets'] ?? []));

        usort($data, function ($a, $b) {
            return $b['err'] - $a['err'];
        });

        return [
            'code' => 0,
            'data' => $data,
            'msg' => '获取成功',
        ];
    }

    public function actionAssets(): array
    {
        $this->response->format = Response::FORMAT_JSON;

        $itemNos = $this->request->post('itemNos');

        if (empty($itemNos)) {
            return [
                'code' => 1,
                'msg' => '资产编号不能为空!',
                'data' => [],
            ];
        }

        $itemNos = preg_split('/\s*;\s*/', trim($itemNos, ' ;'), -1, PREG_SPLIT_NO_EMPTY);
        $assets = Asset::findAll(['asset_item_no' => $itemNos]);
        $data = [];
        foreach ($assets as $asset) {
            $data[] = [
                'assetItemNo' => $asset->asset_item_no,
                'outstandingAmount' => MoneyHelper::f2y(OfflineRefundCashRecharge::outstandingAmount($asset->asset_item_no)),
                'idNum' => $asset->individualBorrow->individual_idnum ?? null,
                'name' => $asset->individualBorrow->individual_name ?? null,
            ];
        }

        return [
            'code' => 0,
            'data' => $data,
            'msg' => '获取成功',
        ];
    }
}
