<?php

namespace repay\controllers;

use Yii;
use repay\models\CardBind;
use repay\models\CardBindSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CardBindController implements the CRUD actions for CardBind model.
 */
class CardBindController extends Controller
{
    /**
     * Lists all CardBind models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new CardBindSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CardBind model.
     *
     * @param int $id Card Bind ID
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the CardBind model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id Card Bind ID
     *
     * @return CardBind the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): CardBind
    {
        if (($model = CardBind::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
