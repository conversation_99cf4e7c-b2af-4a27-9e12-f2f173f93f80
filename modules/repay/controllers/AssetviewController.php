<?php

declare(strict_types=1);

namespace repay\controllers;

use repay\models\search\RepaySearch;
use Yii;
use yii\web\Controller;

class AssetviewController extends Controller
{
    public function actionRepay(): string
    {
        $params = Yii::$app->request->getQueryParams();
        $searchModel  = new RepaySearch();
        $dataProvider = $searchModel->search($params);

        return $this->render('repay', [
            'model'        => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
}
