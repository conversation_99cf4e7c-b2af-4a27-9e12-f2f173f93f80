<?php

namespace repay\controllers;

use repay\components\RepayComponent;
use repay\models\WithholdDetail;
use repay\models\WithholdDetailSearch;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * WithholdDetailController implements the CRUD actions for WithholdDetail model.
 */
class WithholdDetailController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all WithholdDetail models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new WithholdDetailSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new WithholdDetail model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return string
     */
    public function actionCreate(): string
    {
        $model = new WithholdDetail();

        if ($model->load(Yii::$app->request->post()) && $model->submit()) {
            Yii::$app->session->setFlash('success', '添加代扣明细成功');

            return Html::script('window.top.reloadCurrentTab()');
        }

        $model->withhold_detail_serial_no = $this->request->get('withhold_detail_serial_no');

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing WithholdDetail model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id 主键
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionUpdate(int $id): string
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->submit()) {
            Yii::$app->session->setFlash('success', '修改代扣明细成功');

            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing WithholdDetail model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id 主键
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws InvalidConfigException
     */
    public function actionDelete(int $id): Response
    {
        $model = $this->findModel($id);
        $client = RepayComponent::instance();
        $session = Yii::$app->session;
        if ($client->withholdDetailDelete($model)) {
            $session->addFlash('success', '删除成功');
        } else {
            $session->addFlash('error', '接口: ' . $client->getError());
        }

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * Finds the WithholdDetail model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id 主键
     *
     * @return WithholdDetail the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): WithholdDetail
    {
        if (($model = WithholdDetail::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
