<?php

namespace repay\controllers;

use repay\models\QsqOfflineAsset;
use repay\models\QsqOfflineAssetSearch;
use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

class QsqOfflineAssetController extends Controller
{
    public function actionIndex(): string
    {
        $searchModel = new QsqOfflineAssetSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    protected function findModel(int $id): QsqOfflineAsset
    {
        if (($model = QsqOfflineAsset::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}