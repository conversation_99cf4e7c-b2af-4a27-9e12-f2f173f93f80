<?php

namespace repay\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * ProvisionSearch represents the model behind the search form of `repay\models\Provision`.
 */
class ProvisionSearch extends Provision
{
    public $startDate;
    public $endDate;

    //金额
    public $provision_amount_total;
    public $decrease_amount_success;
    public $decrease_amount_pending;
    public $decrease_amount_cancel;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['startDate'], 'default', 'value' => Carbon::yesterday()->toDateString()],
            [['endDate'], 'default', 'value' => Carbon::today()->toDateString()],
            [
                [
                    'provision_type',
                    'provision_item_no',
                    'provision_recharge_serial_no',
                ],
                'filter',
                'filter' => function ($value) {
                    return trim((string)$value);
                },
            ],
        ];
    }


    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = self::find()->select([
            'provision_type',
            'provision_recharge_serial_no',
            'provision_item_no',
            'provision_amount_total' => 'sum(provision_amount)',
            'decrease_amount_success' => 'sum(if(provision_status = \'close\', provision_amount, 0))',
            'decrease_amount_pending' => 'sum(if(provision_status in (\'open\', \'process\'), provision_amount, 0))',
            'decrease_amount_cancel' => 'sum(if(provision_status = \'cancel\', provision_amount, 0))',
            'provision_create_at',
            'provision_update_at',
            'provision_expire_date',
            'provision_source'
        ])->groupBy('provision_recharge_serial_no');

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['provision_create_at'],
                'defaultOrder' => ['provision_create_at' => SORT_DESC],
            ],
        ]);

        $this->load($params);
        if (!$this->validate()) {
            return $dataProvider;
        }

        if ($this->provision_item_no || $this->provision_recharge_serial_no) {
            $startDate = null;
            $endDate = null;
        } else {
            $startDate = $this->startDate;
            $endDate = Carbon::parse($this->endDate)->addDay()->toDateString();
        }

        $query
            ->andFilterWhere([
                'provision_item_no' => $this->provision_item_no,
                'provision_type' => $this->provision_type,
                'provision_recharge_serial_no' => $this->provision_recharge_serial_no,
            ])
            ->andFilterWhere(['>=', 'provision_create_at', $startDate])
            ->andFilterWhere(['<', 'provision_create_at', $endDate]);

        return $dataProvider;
    }
}
