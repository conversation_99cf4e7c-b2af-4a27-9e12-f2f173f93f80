<?php

namespace repay\models;

/**
 * This is the model class for table "withhold".
 */
class Withhold extends \common\models\Withhold
{
    public $is_expired;

    public function rules(): array
    {
        return [
            [
                [
                    'withhold_status',
                    'is_expired',
                ],
                'required',
            ],
            [
                [
                    'withhold_third_serial_no',
                    'withhold_channel_key',
                    'withhold_finish_at',
                    'withhold_comment',
                ],
                'safe',
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return array_merge(parent::attributeLabels(), [
            'withhold_serial_no' => '代扣序列号',
            'withhold_channel' => '代扣通道',
            'withhold_create_at' => '创建时间',
            'withhold_status' => '状态',
            'withhold_finish_at' => '支付完成时间',
            'withhold_channel_key' => '支付序列号',
            'withhold_third_serial_no' => '第三方序列号',
            'is_expired' => '是否需要超时',
        ]);
    }
}
