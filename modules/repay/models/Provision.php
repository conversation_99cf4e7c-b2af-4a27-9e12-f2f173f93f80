<?php

namespace repay\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "provision".
 *
 * @property int $provision_id 拨备垫资主键
 * @property string $provision_type 拨备垫资类型，arbitration=>仲裁;asset_void=>资产作废;asset_reverse=>资产冲正;clear_error=>清分错误;manual_decrease=>手动减免;capital_decrease=>资方要求减免;coupon_decrease=>优惠券减免;asset_decrease=>资产减免
 * @property string $provision_recharge_serial_no 拨备垫资充值序列号
 * @property string $provision_withhold_serial_no 拨备代扣序列号
 * @property string $provision_item_type 拨备垫资项目类型：assset=>资产，combo_order=>第三单
 * @property string $provision_item_no 拨备垫资项目编号
 * @property string $provision_tran_type 交易类型
 * @property string $provision_tran_no 还款计划编号
 * @property int $provision_tran_period 期次
 * @property int $provision_amount 拨备垫资金额
 * @property string $provision_date 拨备垫资日期
 * @property string $provision_status 拨备金垫资状态
 * @property string $provision_create_at 创建时间
 * @property string $provision_update_at 更新时间
 * @property string|null $provision_source 减免来源
 * @property string|null $provision_expire_date 预计过期时间
 * @property int|null $provision_origin_amount 原始金额
 */
class Provision extends ActiveRecord
{
    public const TYPE_ARBITRATION = 'arbitration';   // 仲裁
    public const TYPE_ASSET_VOID = 'asset_void';    // 资产作废
    public const TYPE_ASSET_REVERSE = 'asset_reverse'; // 资产冲正
    public const TYPE_CLEAR_ERROR = 'clear_error';   // 清分错误
    public const TYPE_MANUAL_DECREASE = 'manual_decrease';   // 手动减免
    public const TYPE_CAPITAL_DECREASE = 'capital_decrease';   // 资方要求减免
    public const TYPE_COUPON_DECREASE = 'coupon_decrease';   // 优惠券减免
    public const TYPE_ASSET_DECREASE = 'asset_decrease';   // 资产减免

    public const TYPE_LIST = [
        self::TYPE_ARBITRATION => '仲裁',
        self::TYPE_ASSET_VOID => '资产作废',
        self::TYPE_ASSET_REVERSE => '资产冲正',
        self::TYPE_CLEAR_ERROR => '清分错误',
        self::TYPE_MANUAL_DECREASE => '手动减免',
        self::TYPE_CAPITAL_DECREASE => '资方要求减免',
        self::TYPE_COUPON_DECREASE => '优惠券减免',
        self::TYPE_ASSET_DECREASE => '资产减免',
    ];


    public const ITEM_TYPE_ASSET = "asset";
    public const ITEM_TYPE_COMBO_ORDER = "combo_order";

    public const ITEM_LIST = [
        self::ITEM_TYPE_ASSET => '资产',
        self::ITEM_TYPE_COMBO_ORDER => '第三单',
    ];

    public const STATUS_OPEN = 'open';

    public const STATUS_PROCESS = 'process';

    public const STATUS_CLOSE = 'close';
    public const STATUS_CANCEL = 'cancel';

    public const STATUS_LIST = [
        self::STATUS_OPEN => 'open',
        self::STATUS_CLOSE => 'close',
        self::STATUS_PROCESS => 'process',
        self::STATUS_CANCEL => 'cancel',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'provision';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbRBiz');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['provision_type', 'provision_item_type', 'provision_status'], 'string'],
            [['provision_tran_period', 'provision_amount', 'provision_origin_amount'], 'integer'],
            [['provision_date', 'provision_create_at', 'provision_update_at', 'provision_expire_date'], 'safe'],
            [
                [
                    'provision_recharge_serial_no',
                    'provision_withhold_serial_no',
                    'provision_item_no',
                    'provision_tran_no'
                ],
                'string',
                'max' => 64
            ],
            [['provision_tran_type'], 'string', 'max' => 20],
            [['provision_source'], 'string', 'max' => 32],
            [
                ['provision_item_no', 'provision_tran_period', 'provision_tran_type', 'provision_recharge_serial_no'],
                'unique',
                'targetAttribute' => [
                    'provision_item_no',
                    'provision_tran_period',
                    'provision_tran_type',
                    'provision_recharge_serial_no'
                ]
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'provision_id' => 'ID',
            'provision_type' => '类型',
            'provision_recharge_serial_no' => '操作序列号',
            'provision_withhold_serial_no' => '拨备代扣序列号',
            'provision_item_type' => '项目类型',
            'provision_item_no' => '资产编号',
            'provision_tran_type' => '交易类型',
            'provision_tran_no' => '还款计划编号',
            'provision_tran_period' => '期次',
            'provision_amount' => '金额',
            'provision_date' => '日期',
            'provision_status' => '状态',
            'provision_create_at' => '创建时间',
            'provision_update_at' => '更新时间',
            'provision_source' => '减免来源',
            'provision_expire_date' => '预计过期时间',
            'provision_origin_amount' => '原始金额',
        ];
    }
}
