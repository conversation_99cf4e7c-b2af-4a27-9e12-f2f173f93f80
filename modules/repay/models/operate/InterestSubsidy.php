<?php

namespace repay\models\operate;

use yii\base\Model;

/**
 * Class InterestSubsidy
 *
 * @package repay\models\operate
 */
class InterestSubsidy extends Model
{
    public string $assetItemNo = '';
    public $period;
    public $feeType;
    public $amount = 0;
    public int $repushDcs = 0;

    public const SEND_MQ = ['否', '是'];

    public function rules(): array
    {
        return [
            [
                [
                    'assetItemNo',
                    'period',
                    'feeType',
                    'amount',
                    'repushDcs',
                ],
                'required',
            ],
            [['period', 'amount'], 'integer'],
            [['period', 'amount'], 'filter', 'filter' => 'intval'],
            [['repushDcs'], 'in', 'range' => [0, 1]],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'assetItemNo' => '资产编号',
            'period'      => '期次',
            'feeType'     => '费用类型',
            'amount'      => '金额',
            'repushDcs'   => '是否推送清结算',
        ];
    }

    public function fields(): array
    {
        return [
            'item_no'    => 'assetItemNo',
            'period'     => 'period',
            'type'       => 'feeType',
            'amount'     => 'amount',
            'repush_dcs' => function () {
                return (bool)$this->repushDcs;
            },
        ];
    }
}
