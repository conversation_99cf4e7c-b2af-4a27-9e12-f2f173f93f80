<?php

namespace repay\models\operate;

use common\models\AssetTran;
use xlerr\desensitise\Desensitise;
use Yii;
use yii\base\Model;
use function xlerr\desensitise\encrypt;

/**
 * Class AssetVoidWithhold
 *
 * @package repay\models\operate
 */
class AssetVoidWithhold extends Model
{
    public $refNo;
    public $amount;
    public $cardNum;
    public $mobile;
    public $username;
    public $idNum;
    public $callback = 'http://biz-repay-svc.biz.svc.cluster.local/paysvr/trade/callback';
    public $tradeType = 'asset_void';
    public $owner = 'KN';
    public $withholdType = 'auto';

    public function rules(): array
    {
        return [
            [
                [
                    'refNo',
                    'amount',
                    'cardNum',
                    'mobile',
                    'username',
                    'idNum',
                ],
                'required',
            ],
            [['amount'], 'integer', 'min' => 1, 'max' => 99999999900],
        ];
    }

    public function formName(): string
    {
        return '';
    }

    public function attributeLabels(): array
    {
        return [
            'refNo' => '资产编号',
            'amount' => '金额(元)',
            'cardNum' => '银行卡号',
            'mobile' => '手机号',
            'username' => '姓名',
            'idNum' => '持卡人身份证号',
            'callback' => '回调地址',
            'tradeType' => '交易类型',
            'owner' => '资产所属',
            'withholdType' => '代扣类型',
        ];
    }

    public function fields(): array
    {
        return [
            'ref_no' => 'refNo',
            'trade_type' => 'tradeType',
            'owner' => 'owner',
            'amount' => 'amount',
            'mobile_encrypt' => function () {
                return encrypt($this->mobile, Desensitise::TYPE_PHONE_NUMBER)->hash;
            },
            'id_num_encrypt' => function () {
                return encrypt($this->idNum, Desensitise::TYPE_IDENTITY_NUMBER)->hash;
            },
            'username_encrypt' => function () {
                return encrypt($this->username, Desensitise::TYPE_NAME)->hash;
            },
            'card_num_encrypt' => function () {
                return encrypt($this->cardNum, Desensitise::TYPE_BANK_CARD_NUMBER)->hash;
            },
            'withhold_type' => 'withholdType',
            'callback' => 'callback',
            'user_active' => function () {
                return false;
            },
            'operator' => function () {
                return Yii::$app->user->identity->username ?? null;
            },
        ];
    }

    public function verify(): bool
    {
        $totalAmount = AssetTran::find()->where([
            'asset_tran_type' => 'repayprincipal',
            'asset_tran_status' => 'nofinish',
            'asset_tran_asset_item_no' => $this->refNo
        ])->sum('asset_tran_balance_amount') ?? 0;

        return (int)$totalAmount === (int)$this->amount;
    }
}
