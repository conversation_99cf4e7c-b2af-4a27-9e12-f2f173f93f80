<?php

namespace repay\models\operate;

use Yii;
use yii\base\Model;

/**
 * Class IncreaseFee
 *
 * @package repay\models\operate
 */
class IncreaseFee extends Model
{
    public string $assetItemNo = '';
    public int $period = 1;
    public int $amount = 0;
    public string $comment = '';
    public int $sendChangeMq = 0;

    public const SEND_MQ = ['否', '是'];

    public function rules(): array
    {
        return [
            [
                [
                    'assetItemNo',
                    'period',
                    'amount',
                    'sendChangeMq',
                ],
                'required',
            ],
            [['comment'], 'string'],
            [['sendChangeMq'], 'in', 'range' => [0, 1]],
            [['period'], 'integer'],
            [
                ['amount'],
                'integer',
                'min'      => 1,
                'max'      => 9999999999,
                'tooSmall' => '{attribute}不能小于0.01元',
                'tooBig'   => '{attribute}不能大于99999999.99元',
            ],

        ];
    }

    public function attributeLabels(): array
    {
        return [
            'assetItemNo'  => '资产编号',
            'period'       => '期次',
            'amount'       => '增加金额(元)',
            'comment'      => '备注',
            'sendChangeMq' => '是否发ChangeMQ',
        ];
    }

    public function fields()
    {
        return [
            'asset_item_no'  => 'assetItemNo',
            'period'         => 'period',
            'amount'         => 'amount',
            'comment'        => 'comment',
            'send_change_mq' => 'sendChangeMq',
            'type'           => function () {
                return 'lateinterest';
            },
            'operator_id'    => function () {
                return Yii::$app->user->identity->id;
            },
            'operator_name'  => function () {
                return Yii::$app->user->identity->username;
            },
        ];
    }
}
