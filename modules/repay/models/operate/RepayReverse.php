<?php

namespace repay\models\operate;

use Yii;
use yii\base\Model;

/**
 * Class RepayReverse
 *
 * @package repay\models\operate
 */
class RepayReverse extends Model
{
    public string $assetItemNo = '';
    public string $serialNo = '';
    public string $comment = '';
    public int $sendChangeMq = 0;

    public const SEND_MQ = ['否', '是'];

    public function rules(): array
    {
        return [
            [
                [
                    'assetItemNo',
                    'serialNo',
                    'sendChangeMq',
                ],
                'required',
            ],
            [['comment'], 'string'],
            [['sendChangeMq'], 'in', 'range' => [0, 1]],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'assetItemNo'  => '资产编号',
            'serialNo'     => '充值序列号',
            'comment'      => '备注',
            'sendChangeMq' => '是否发ChangeMQ',
        ];
    }

    public function fields()
    {
        return [
            'serial_no'      => 'serialNo',
            'asset_item_no'  => 'assetItemNo',
            'comment'        => 'comment',
            'send_change_mq' => 'sendChangeMq',
            'operator_id'    => function () {
                return Yii::$app->user->identity->id;
            },
            'operator_name'  => function () {
                return Yii::$app->user->identity->username;
            },
        ];
    }
}
