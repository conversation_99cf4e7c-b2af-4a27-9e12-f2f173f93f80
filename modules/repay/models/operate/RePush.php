<?php

namespace repay\models\operate;

use yii\base\Model;

class RePush extends Model
{
    public $content = '[
    {
        "itemNo": 1,
        "periodStart": 1,
        "periodEnd": 2
    }
]
';

    public function rules(): array
    {
        return [
            [
                [
                    'content',
                ],
                'required',
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'content' => '推送内容',
        ];
    }
}

