<?php

namespace repay\models\offlinerefund;

use common\models\Asset;
use repay\components\RepayComponent;
use yii\base\Model;
use yii\base\UserException;

class OfflineRefundNonCashRecharge extends Model
{

    /**
     * @var string
     */
    public string $flowNo = '';

    /**
     * @var array
     */
    public array $name = [];

    /**
     * @var int
     */
    public int $rechargeAmount = 0;

    /**
     * @var array
     */
    public array $idNum = [];

    /**
     * @var string
     */
    public string $itemNos = '';

    /**
     * @var array
     */
    public array $assetItemNo = [];

    /**
     * @var string
     */
    public string $owner = 'KN';

    /**
     * @var array|null
     */
    public ?array $outstandingAmount = null;

    /**
     * @var
     */
    public $subform;


    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [
                ['flowNo', 'name', 'rechargeAmount', 'idNum', 'itemNos', 'owner', 'assetItemNo', 'outstandingAmount',],
                'required',
            ],
            [
                ['rechargeAmount'],
                'integer',
                'min'      => 1,
                'max'      => 9999999999,
                'tooSmall' => '{attribute}不能小于0.01元',
                'tooBig'   => '{attribute}不能大于99999999.99元',
            ],
            [
                ['flowNo'],
                'exist',
                'skipOnError'     => false,
                'targetClass'     => TransactionFlowRecord::class,
                'targetAttribute' => ['flowNo' => 'transaction_flow_record_no'],
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'name'           => '姓名',
            'flowNo'         => '流水编号',
            'rechargeAmount' => '充值金额(元)',
            'idNum'          => '身份证号',
            'itemNos'        => '资产编号',
            'subform'        => '资产列表',
            'owner'          => '账户owner',
        ];
    }

    /**
     * @param array $params
     *
     * @return bool
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\base\UserException
     */
    public function submit(array $params): bool
    {
        $this->load($params);
        if (!$this->validate()) {
            return false;
        }

        /** @var TransactionFlowRecord $one */
        $one = TransactionFlowRecord::findOne([
            'transaction_flow_record_no' => $this->flowNo,
        ]);

        if ($one->transaction_flow_record_amount - $one->transaction_flow_record_repaid_amount < 0) {
            $this->addError('rechargeAmount', '充值金额大于余额!');

            return false;
        }

        //获取第一笔资产编号,以第一笔资产为该用户的身份证号,姓名
        $this->assetItemNo = array_unique($this->assetItemNo);

        $asset = Asset::findOne(['asset_item_no' => current($this->assetItemNo)]);
        if (empty($asset->individualBorrow)) {
            throw new UserException('通过资产未找到借款人');
        }

        $client = RepayComponent::instance();
        $result = $client->offlineWithhold(
            $this->flowNo,
            $asset->individualBorrow->individual_idnum_encrypt,
            $this->owner,
            $this->rechargeAmount,
            array_map(function ($itemNo) {
                return ['item_no' => $itemNo];
            }, $this->assetItemNo)
        );

        if (!$result) {
            throw new UserException('接口: ' . $client->getError());
        }

        return true;
    }
}
