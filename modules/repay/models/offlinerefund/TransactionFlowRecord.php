<?php

namespace repay\models\offlinerefund;

use common\models\Asset;
use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * Class TransactionFlowRecord
 *
 * @property int    $transaction_flow_record_id
 * @property string $transaction_flow_record_no
 * @property string $transaction_flow_record_download_url
 * @property int    $transaction_flow_record_amount
 * @property int    $transaction_flow_record_repaid_amount
 * @property string $transaction_flow_record_create_at
 * @property string $transaction_flow_record_update_at
 * @package common\models
 */
class TransactionFlowRecord extends ActiveRecord
{

    /**
     * @var int 充值金额
     */
    public int $transactionFlowRecordAmount = 0;

    /**
     * @var string
     */
    public string $owner = '';
    /**
     * @var string
     */
    public string $itemNo = '';

    /**
     * @var string
     */
    public string $type = '';

    public static function tableName(): string
    {
        return 'transaction_flow_record';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbRBiz');
    }


    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [
                [
                    'transaction_flow_record_no',
                    'transactionFlowRecordAmount',
                    'owner',
                    'itemNo',
                ],
                'required',
            ],
            [
                ['transactionFlowRecordAmount'],
                'integer',
                'min'      => 1,
                'max'      => 9999999999,
                'tooSmall' => '{attribute}不能小于0.01元',
                'tooBig'   => '{attribute}不能大于99999999.99元',
            ],
            [
                ['type'],
                'in',
                'range' => ['provision_settle_asset', 'provision_settle_noloan'],
            ],
            ['transaction_flow_record_no', 'exist',],
            [
                ['itemNo'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => Asset::class,
                'targetAttribute' => ['itemNo' => 'asset_item_no'],
            ],
        ];
    }


    /**
     * @return array|ActiveRecord[]
     */
    public static function recordList(): array
    {
        return self::find()->select([
                'transaction_flow_record_no',
            ])->where([
                '>',
                '`transaction_flow_record_amount` - `transaction_flow_record_repaid_amount`',
                '0',
            ])->indexBy('transaction_flow_record_no')->column() ?? [];
    }
}
