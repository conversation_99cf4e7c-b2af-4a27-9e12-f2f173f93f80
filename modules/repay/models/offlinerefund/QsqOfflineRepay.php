<?php

namespace repay\models\offlinerefund;

use common\models\Asset;
use repay\models\QsqOfflineAsset;
use Throwable;
use xlerr\common\helpers\MoneyHelper;
use yii\base\Model;
use yii\base\UserException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use function xlerr\adminlte\userFullName;

class QsqOfflineRepay extends Model
{
    /**
     * @var string
     */
    public string $owner = 'KN';

    /**
     * @var
     */
    public $subform;

    public $ruleId;

    public $batchNo;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [
                ['owner', 'subform', 'ruleId', 'batchNo'],
                'required',
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'subform' => '资产列表',
            'owner' => '账户owner',
            'ruleId' => '收付款规则',
            'batchNo' => '批次号',
        ];
    }

    /**
     * @param array $params
     *
     * @return bool
     * @throws UserException
     */
    public function submit(array $params): bool
    {
        $this->load($params);
        if (!$this->validate()) {
            return false;
        }
        $subform = Json::decode($this->subform);
        $itemNos = ArrayHelper::getColumn($subform, 'assetItemNo');
        $db = QsqOfflineAsset::getDb();
        $transaction = $db->beginTransaction();
        if ($this->isBatchNoDuplicate() && $this->hasProcessingAssetData($itemNos) && $this->isPaidOff($itemNos)) {
            try {
                $command = $db->createCommand();
                $columns = [
                    'item_no',
                    'repay_date',
                    'amount',
                    'batch_no',
                    'settlement_id',
                    'operator',
                    'status'
                ];
                $data = [];
                foreach ($subform as $item) {
                    $status = ($item['status'] == -1 && empty($item['idNum']) && empty($item['name'])) ? QsqOfflineAsset::STATUS_SUCCESS : QsqOfflineAsset::STATUS_INIT;
                    $data[] = [
                        $item['assetItemNo'],
                        $item['repayDate'],
                        MoneyHelper::y2f($item['repayAmount']),
                        $this->batchNo,
                        $this->ruleId,
                        userFullName(),
                        $status
                    ];
                }

                $affectedCountList = $command->batchInsert(QsqOfflineAsset::tableName(), $columns, $data)->execute();

                if ($affectedCountList < 1) {
                    throw new UserException('资产数据插入失败,请稍后再试!');
                }

                $transaction->commit();
                return true;
            } catch (Throwable $exception) {
                $transaction->rollBack();
                throw new UserException($exception->getMessage());
            }
        }
        return false;
    }

    /**
     * @throws UserException
     */
    protected function isPaidOff(array $itemNos = []): bool
    {
        $ret = Asset::find()
            ->where(['asset_item_no' => array_unique($itemNos)])
            ->andWhere(['<>', 'asset_status', Asset::STATUS_REPAY])
            ->select('asset_item_no')
            ->indexBy('asset_id')
            ->column();

        if (!empty($ret)) {
            throw new UserException(vsprintf('资产编号为:[%s] 已被结清,请重新筛选再提交!', [implode(',', $ret)]));
        }

        return true;
    }

    /**
     * @return bool
     * @throws UserException
     */
    protected function isBatchNoDuplicate(): bool
    {
        $exists = QsqOfflineAsset::find()->where(['batch_no' => $this->batchNo])->exists();
        if ($exists) {
            throw new UserException(vsprintf('批次号:[%s] 的文件被重复提交!', [$this->batchNo]));
        }
        return true;
    }

    /**
     * @return bool
     * @throws UserException
     */
    protected function hasProcessingAssetData(array $itemNos = []): bool
    {
        $ret = QsqOfflineAsset::find()->where(['item_no' => $itemNos, 'status' => QsqOfflineAsset::STATUS_PROCESS])->select('item_no')->indexBy('id')->column();

        if (!empty($ret)) {
            throw new UserException(vsprintf('资产编号为:[%s] 正在处理中,请重新筛选提交!', [implode(',', $ret)]));
        }

        return true;
    }
}
