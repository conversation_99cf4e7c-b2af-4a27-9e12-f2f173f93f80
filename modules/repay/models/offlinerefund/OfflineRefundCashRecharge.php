<?php

namespace repay\models\offlinerefund;

use common\models\Asset;
use common\models\AssetTran;
use repay\components\RepayComponent;
use Throwable;
use Yii;
use yii\base\Model;

class OfflineRefundCashRecharge extends Model
{
    /**
     * @var string
     */
    public string $itemNo = '';

    /**
     * @var int
     */
    public int $outstandingAmount = 0;

    /**
     * @var string
     */
    public string $flowNo = '';

    /**
     * @var string
     */
    public string $name = '';

    /**
     * @var int
     */
    public int $rechargeAmount = 0;

    /**
     * @var array
     */
    public array $type = [];

    /**
     * @var string
     */
    public string $idNum = '';

    /**
     * @var string
     */
    public string $owner = 'KN';

    /**
     * @var Asset
     */
    public Asset $asset;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [
                [
                    'name',
                    'idNum',
                ],
                'string',
            ],
            [
                [
                    'itemNo',
                    'outstandingAmount',
                    'flowNo',
                    'rechargeAmount',
                    'owner',
                    'type',
                ],
                'required',
            ],
            [
                ['outstandingAmount'],
                'integer',
                'min'      => 1,
                'max'      => 9999999999,
                'tooSmall' => '{attribute}不能小于0.01元',
                'tooBig'   => '{attribute}不能大于99999999.99元',
            ],
            [
                ['flowNo'],
                'exist',
                'skipOnError'     => false,
                'targetClass'     => TransactionFlowRecord::class,
                'targetAttribute' => ['flowNo' => 'transaction_flow_record_no'],
            ],
            [
                ['itemNo'],
                'exist',
                'skipOnError'     => false,
                'targetClass'     => Asset::class,
                'targetAttribute' => ['itemNo' => 'asset_item_no'],
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'itemNo'            => '资产编号',
            'outstandingAmount' => '剩余应还金额(元)',
            'name'              => '姓名',
            'flowNo'            => '流水编号',
            'rechargeAmount'    => '充值金额(元)',
            'type'              => '结清类型',
            'idNum'             => '身份证号',
            'owner'             => '账户owner',
        ];
    }

    /**
     * 获取资产应还金额
     *
     * @param string $itemNo
     *
     * @return int
     */
    public static function outstandingAmount(string $itemNo): int
    {
        return (int)AssetTran::find()
            ->where([
                'asset_tran_asset_item_no' => $itemNo,
            ])
            ->andWhere(['<>', 'asset_tran_type', 'grant'])
            ->sum('asset_tran_balance_amount');
    }


    /**
     * @param string|null $itemNo
     *
     * @return Asset
     */
    public static function asset(string $itemNo): ?Asset
    {
        return Asset::findOne(['asset_item_no' => $itemNo]);
    }

    public function submit(array $items): bool
    {
        if (!$this->validate()) {
            return false;
        }
        /**
         * @var Asset $asset
         */
        $asset = self::asset($items['itemNo']);

        /**
         * @var TransactionFlowRecord $one
         */
        $one = TransactionFlowRecord::findOne([
            'transaction_flow_record_no' => $items['flowNo'],
        ]);

        if ($one->transaction_flow_record_amount - $one->transaction_flow_record_repaid_amount < 0) {
            $this->addError('rechargeAmount', '充值金额大于余额!');

            return false;
        }
        try {
            $assetItems = [
                'item_no'                 => $items['itemNo'],
                'provision_settle_asset'  => in_array('provision_settle_asset', $this->type, true),
                'provision_settle_noloan' => in_array('provision_settle_noloan', $this->type, true),
            ];
            $objet      = RepayComponent::instance();

            return $objet->offlineWithhold(
                $this->flowNo,
                $asset->individualBorrow->individual_idnum_encrypt,
                $this->owner,
                $this->rechargeAmount,
                [
                    $assetItems,
                ]
            );
        } catch (Throwable $exception) {
            Yii::$app->session->setFlash('error', $exception->getMessage());

            return false;
        }
    }
}
