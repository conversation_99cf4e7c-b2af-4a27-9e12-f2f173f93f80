<?php

use kartik\widgets\DateTimePicker;
use repay\models\Withhold;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model Withhold */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'withhold_third_serial_no')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'withhold_channel_key')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'withhold_finish_at')->widget(DateTimePicker::class, [
        'type' => DateTimePicker::TYPE_INPUT,
        'options' => [
            'autocomplete' => 'off',
        ],
        'pluginOptions' => [
            'todayBtn' => 'linked',
            'format' => 'yyyy-mm-dd hh:ii:ss',
            'todayHighlight' => true,
            'autoclose' => true,
        ],
    ]) ?>

    <?= $form->field($model, 'withhold_status')->widget(Select2::class, [
        'data' => Withhold::STATUS_LIST,
        'hideSearch' => true,
        'options' => [
            'prompt' => $model->getAttributeLabel('withhold_status'),
        ],
    ]) ?>

    <?= $form->field($model, 'is_expired')->widget(Select2::class, [
        'data' => ['否', '是'],
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'withhold_comment')->textarea() ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
