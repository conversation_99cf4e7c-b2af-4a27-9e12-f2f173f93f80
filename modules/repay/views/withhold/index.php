<?php

use repay\models\Withhold;
use repay\models\WithholdSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel WithholdSearch */

$this->title = '代扣记录维护';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', [
    'model' => $searchModel,
]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{details} {update}',
            'buttons' => [
                'details' => static function ($url, Withhold $model) {
                    return ActionColumn::newButton('查询明细', [
                        'withhold-detail/index',
                        'withhold_detail_serial_no' => $model->withhold_serial_no,
                    ], [
                        'class' => 'btn-info',
                        'target' => '_blank',
                    ]);
                },
                'update' => static function ($url) {
                    return ActionColumn::newButton('修改', $url, [
                        'class' => 'btn-primary layer-dialog',
                    ]);
                },
            ],
        ],

        'withhold_serial_no',
        'withhold_channel',
        'withhold_create_at',
        [
            'attribute' => 'withhold_status',
        ],
        'withhold_finish_at',
        'withhold_channel_key',
        'withhold_third_serial_no',
    ],
]);
