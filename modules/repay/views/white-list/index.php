<?php

use common\models\Asset;
use repay\models\WhiteList;
use repay\models\WhiteListSearch;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel WhiteListSearch */

$this->title = '灰度白名单';
$this->params['breadcrumbs'][] = $this->title;
?>

<?= $this->render('_search', ['model' => $searchModel]); ?>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns' => [

        [
            'attribute' => 'white_list_channel',
            'format' => ['in', Asset::channelList()],
        ],
        [
            'attribute' => 'white_list_val',
        ],
        [
            'attribute' => 'white_list_type',
            'format' => ['in', WhiteList::TYPE_LIST],
        ],
        'white_list_operator',
        // 'white_list_comment',
        // 'white_list_status',
        'white_list_create_at',
        'white_list_expire_time',
        // 'white_list_update_at',
    ],
]); ?>
