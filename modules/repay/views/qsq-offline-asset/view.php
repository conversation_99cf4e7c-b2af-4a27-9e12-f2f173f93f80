<?php

use repay\models\CardBind;
use repay\models\QsqOfflineAsset;
use xlerr\common\grid\MoneyDataColumn;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model CardBind */

?>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'options' => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                'batch_no',
                'item_no',
                [
                    'label' => '金额',
                    'attribute' => 'amount',
                    'format' => ['f2y', true],
                ],
                'repay_date',
                [
                    'label' => '状态',
                    'attribute' => 'status',
                    'format' => ['in', QsqOfflineAsset::STATUS_LIST]
                ],
                'create_at',
                'update_at'
            ],
        ]) ?>
    </div>
</div>
