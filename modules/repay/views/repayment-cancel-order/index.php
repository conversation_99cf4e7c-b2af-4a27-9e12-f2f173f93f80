<?php

use repay\models\RepaymentCancelOrder;
use repay\models\RepaymentCancelOrderSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\helpers\Html;

/** @var yii\web\View $this */
/** @var repay\models\RepaymentCancelOrderSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = '取消资产还款';
$this->params['breadcrumbs'][] = $this->title;
echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{details} {cancelList}',
            'buttons' => [
                'details' => static function ($url, RepaymentCancelOrderSearch $model) {
                    return DialogActionColumn::newButton('订单明细', [
                        'details',
                        'orderNo' => $model->order_no,
                    ], [
                        'class' => 'btn-info layer-dialog',
                    ]);
                },
                'cancelList' => static function ($url, RepaymentCancelOrderSearch $model) {
                    return DialogActionColumn::newButton('操作记录', [
                        'cancel-list',
                        'orderNo' => $model->order_no,
                    ], [
                        'class' => 'btn-primary layer-dialog',
                    ]);
                },
            ],
        ],
        'order_no',
        [
            'label' => '资产编号',
            'format' => 'raw',
            'value' => function (RepaymentCancelOrderSearch $item) {
                return Html::a($item['asset_item_no'], ['/webtask/task/index', 'system' => 'dbRBiz', 'task_order_no' => $item['asset_item_no']], [
                    'title' => '查看',
                    'target' => '_blank',
                ]);
            },
        ],
        'asset_loan_channel',
        [
            'label' => '业务类型',
            'attribute' => 'biz_type',
            'format' => ['in', RepaymentCancelOrder::BIZ_TYPE_LIST],
        ],
        [
            'label' => '状态',
            'attribute' => 'status',
            'format' => ['in', RepaymentCancelOrder::STATUS_LIST],
        ],
        'apply_at',
        'finish_at',
        'start_period',
        'end_period',
        [
            'label' => '创建时间',
            'attribute' => 'created_at',
        ],
    ],
]);


