<?php

use xlerr\CodeEditor\CodeEditor;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model \repay\models\CentralSendmsg */

$this->title                   = $model->sendmsg_id;
$this->params['breadcrumbs'][] = ['label' => 'Central Sendmsgs', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('编辑', ['update', 'id' => $model->sendmsg_id], ['class' => 'btn btn-primary']) ?>
    <?= Html::a('首页', ['index'], ['class' => 'btn btn-default']) ?>
</p>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title"><?= 'Detail' ?></div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model'      => $model,
            'options'    => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                'sendmsg_id',
                'sendmsg_order_no',
                'sendmsg_type',
                [
                    'attribute' => 'sendmsg_content',
                    'format'    => function ($val) {
                        return CodeEditor::widget([
                            'name'          => 'sendmsg_content',
                            'value'         => $val,
                            'clientOptions' => [
                                'readOnly' => true,
                                'mode'     => CodeEditor::MODE_JSON,
                            ],
                        ]);
                    },
                ],
                [
                    'attribute' => 'sendmsg_response_data',
                    'format'    => function ($val) {
                        return CodeEditor::widget([
                            'name'          => 'sendmsg_response_data',
                            'value'         => $val,
                            'clientOptions' => [
                                'readOnly' => true,
                                'mode'     => CodeEditor::MODE_JSON,
                            ],
                        ]);
                    },
                ],
                'sendmsg_memo',
                'sendmsg_tosystem',
                'sendmsg_status',
                'sendmsg_next_run_at',
                'sendmsg_version',
                'sendmsg_priority',
                'sendmsg_retrytimes',
                'sendmsg_create_at',
                'sendmsg_update_at',
            ],
        ]) ?>
    </div>
</div>
