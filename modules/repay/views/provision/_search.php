<?php


use repay\models\Provision;
use repay\models\ProvisionSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ProvisionSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>
        <?= $form->field($model, 'provision_type', ['options' => [
            'style' => 'min-width: 150px',
        ]])->widget(Select2::class, [
            'data' => Provision::TYPE_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '类型',
            ]
        ]) ?>
        <?= $form->field($model, 'provision_recharge_serial_no') ?>
        <?= $form->field($model, 'provision_item_no') ?>
        <?= $form->field($model, 'startDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建开始时间',
                'autocomplete' => 'off',
            ],
        ]) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建结束时间',
                'autocomplete' => 'off',
            ],
        ]) ?>
        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>

        <?= Html::a('新增减免结清', ['remain'], ['class' => 'btn btn-success layer-dialog',]) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>