<?php

use repay\models\operate\WriteOffAsset;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/** @var WriteOffAsset $model */
/** @var View $this */

$this->title = '拨备结清资产';

?>

<div class="box box-default">
    <div class="box-header with-border">
        <div class="box-title">拨备结清资产</div>
    </div>

    <?php
    $form = ActiveForm::begin([
        'action' => [''],
        'method' => 'post',
    ]) ?>
    <div class="box-body">

        <?= $form->field($model, 'assetItemNo') ?>

        <?= $form->field($model, 'provisionType')->widget(Select2::class, [
            'data'       => WriteOffAsset::PROVISION_TYPE,
            'hideSearch' => true,
        ]) ?>

        <?= $form->field($model, 'period') ?>

        <?= $form->field($model, 'amount')->widget(MoneyInput::class) ?>

        <?= $form->field($model, 'comment') ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('提交', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>
    <?php
    ActiveForm::end() ?>
</div>
