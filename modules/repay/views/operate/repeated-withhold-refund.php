<?php

use repay\models\operate\RepeatedWithholdRefund;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\MoneyInput;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View                   $this
 * @var RepeatedWithholdRefund $model
 */

$this->title = '重复代扣退款';

$this->params['breadcrumbs'][] = $this->title;

?>

<?php
$form = ActiveForm::begin([
    'action' => [''],
    'method' => 'post',
]) ?>
<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">退款基本信息</div>
    </div>
    <div class="box-body">
        <?= $form->field($model, 'refundWithholdSerialNo') ?>

        <?= $form->field($model, 'refundAmount')->widget(MoneyInput::class) ?>

        <?= $form->field($model, 'repayWithholdSerialNo') ?>

    </div>
</div>

<div class="box box-info">
    <div class="box-header with-border">
        <div class="box-title">
            退款资产信息<span class="text-danger">（可不填写，不填则直接平账，不会有逆操作）</span>
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-default btn-box-tool add-asset">增加退款资产信息</button>
        </div>
    </div>
    <div class="box-body">
        <div class="row asset-info">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">资产编号</label>
                    <?= Html::textInput('refundAssetInfos[item_no][]', null, [
                        'class' => 'form-control',
                    ]) ?>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">退款金额(元)</label>
                    <?= MoneyInput::widget([
                        'name'  => 'refundAssetInfos[amount][]',
                        'class' => 'form-control',
                    ]) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="box box-solid box-default">
    <div class="box-footer">
        <?= Html::submitButton('提交', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>
</div>
<?php
ActiveForm::end() ?>

<script type="text/javascript">
    <?php $this->beginBlock('jsBlock') ?>
    (function ($) {
        const template = $('div.asset-info:eq(0)'),
            container = template.parent(),
            addAssetBtn = $('.add-asset'),
            data = <?= json_encode($model->getDetailList()) ?> || []

        addAssetBtn.on('click', () => {
            let temp = template.clone(),
                amountInput = temp.find('input[id]')

            temp.find('input').val('')
            amountInput.removeAttr('id')

            container.append(temp)

            amountInput.inputmask({
                alias: 'iY2F'
            })
        })

        data.forEach((v, i) => {
            if (i) {
                addAssetBtn.click()
            }
            window.setTimeout(() => {
                $('input[name="refundAssetInfos[item_no][]"]:eq(' + i + ')').val(v.item_no);
                $('input[name="refundAssetInfos[amount][]"]:eq(' + i + ')').val(v.amount);
            }, 100)
        })
    })(jQuery);
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['jsBlock']) ?>
</script>
