<?php

use xlerr\common\widgets\ActiveForm;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model \dcs\tasks\AccountBalanceClear */

$this->title = '清空资产余额操作';

$this->params['breadcrumbs'][] = ['label' => '清空资产余额操作', 'url' => ['account-balance-clear']];
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title"><?= $this->title ?></h3>
    </div>

    <?php
    $form = ActiveForm::begin([
        'action' => [''],
    ]); ?>

    <div class="box-body">
        <div class="form-group highlight-addon field-recharge-serialno required">
            <label class="control-label has-star" for="item-no">资产编号</label>
            <?= Html::textInput('itemNo', Yii::$app->request->post('itemNo'), ['class' => 'form-control']) ?>
        </div>


    </div>
    <div class="box-footer">
        <?= Html::submitButton('清空', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>
    <?php ActiveForm::end(); ?>

</div>

