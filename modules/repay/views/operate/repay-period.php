<?php

use repay\models\operate\Recharge;
use repay\models\operate\RepayPeriod;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/** @var Recharge $model */
/** @var View $this */

$this->title = 'RBIZ还清本期';

?>

<div class="box box-default">
    <div class="box-header with-border">
        <div class="box-title">还款</div>
    </div>

    <?php
    $form = ActiveForm::begin([
        'action' => [''],
        'method' => 'post',
    ]) ?>
    <div class="box-body">

        <?= $form->field($model, 'assetItemNo') ?>

        <?= $form->field($model, 'period') ?>

        <?= $form->field($model, 'serialNo') ?>

        <?= $form->field($model, 'sendChangeMq')->widget(Select2::class, [
            'data'       => RepayPeriod::SEND_MQ,
            'hideSearch' => true,
        ]) ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('提交', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>
    <?php
    ActiveForm::end() ?>
</div>
