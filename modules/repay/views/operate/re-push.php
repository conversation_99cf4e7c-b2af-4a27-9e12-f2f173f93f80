<?php

use repay\models\operate\WithholdWithoutProject;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use yii\helpers\Html;
use yii\web\View;

/** @var WithholdWithoutProject $model */
/** @var View $this */

$this->title = '重新推送清结算';

?>

<div class="box box-default">
    <div class="box-header with-border">
        <div class="box-title">重新推送清结算</div>
    </div>

    <?php
    $form = ActiveForm::begin([
        'action' => [''],
        'method' => 'post',
    ]) ?>
    <div class="box-body">

        <?= $form->field($model, 'content')->widget(CodeEditor::class, [
            'clientOptions' => [
                'mode' => CodeEditor::MODE_JSON,
                'minLines' => 10,
                'maxLines' => 40,
            ],
        ]) ?>

        <pre>数据格式必须为:
[
    {
        "itemNo": 1, // 资产编号
        "periodStart": 1, // 开始期次
        "periodEnd": 2 // 结束期次
    },
    {
        "itemNo": 2,
        "periodStart": 3,
        "periodEnd": 4
    }
]</pre>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('提交', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>
    <?php
    ActiveForm::end() ?>
</div>
