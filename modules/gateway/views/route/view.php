<?php

use gateway\approvals\RouteApproval;
use gateway\models\Route;
use gateway\models\RouteParam;
use kartik\detail\DetailView;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model Route */

$this->title = $model->route_tenant . ':' . $model->route_group . ':' . $model->route_code;
$this->params['breadcrumbs'][] = ['label' => '路由管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

$approval = new RouteApproval();
$approvalModel = current(
    $approval->queryNotEndOfBusiness([
        'id' => $model->route_id,
    ])
);
?>
<p>
    <?php
    if (!$approvalModel) {
        echo Html::a('编辑', ['update', 'id' => $model->route_id], [
            'class' => 'btn btn-primary',
        ]);
    }
    ?>

    <?= Html::a('路径调试', ['debug', 'id' => $model->route_id], [
        'class' => 'btn btn-warning layer-dialog',
    ]) ?>

    <?php
    if ($approvalModel) {
        echo Html::a(
            vsprintf('%s申请, 审核中', [
                [
                    Route::STATUS_ACTIVE => '上线',
                    Route::STATUS_INACTIVE => '下线',
                ][$approvalModel->route_status],
            ]), ['#'],
            [
                'class' => 'btn btn-default',
                'disabled' => true,
            ]
        );
    } elseif ($model->route_status === Route::STATUS_ACTIVE) {
        echo Html::a('申请下线', ['inactive', 'id' => $model->route_id], [
            'class' => 'btn btn-danger',
            'title' => '申请下线',
            'data' => [
                'confirm' => '确定要发起申请下线审核吗?',
                'method' => 'post',
            ],
        ]);
    } else {
        echo Html::a('申请上线', ['active', 'id' => $model->route_id], [
            'class' => 'btn btn-success',
            'title' => '申请上线',
            'data' => [
                'confirm' => '确定要发起申请上线审核吗?',
                'method' => 'post',
            ],
        ]);
    }
    ?>
</p>
<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'krajeeDialogSettings' => [
                'overrideYiiConfirm' => false,
            ],
            'hAlign' => DetailView::ALIGN_LEFT,
            'enableEditMode' => false,
            'labelColOptions' => [
                'style' => 'width:10%',
            ],
            'valueColOptions' => [
                'style' => 'width:40%',
            ],
            'attributes' => [
                [
                    'columns' => [
                        [
                            'attribute' => 'route_tenant',
                            'format' => ['in', Route::tenants()],
                        ],
                        [
                            'attribute' => 'route_group',
                            'value' => $model->getGroup(),
                        ],
                    ],
                ],
                [
                    'columns' => [
                        'route_code',
                        'route_name',
                    ],
                ],
                [
                    'columns' => [
                        'route_path',
                        'route_service_path',
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'route_service_code',
                            'value' => vsprintf('%s: %s', [
                                $model->service->service_name,
                                $model->service->service_uri,
                            ]),
                        ],
                        [
                            'attribute' => 'route_status',
                            'format' => ['in', Route::STATUS_LIST],
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'route_access_type',
                            'format' => [
                                'in',
                                [
                                    'public' => '公网访问',
                                    'internal' => '内网访问',
                                ],
                            ],
                        ],
                        'route_version',
                    ],
                ],
                [
                    'columns' => [
                        'route_create_at',
                        'route_update_at',
                    ],
                ],

                [
                    'columns' => [
                        'route_memo',
                        'route_order',
                    ],
                ],
                [
                    'columns' => [
                        'route_metadata',
                        [
                            'attribute' => '',
                            'value' => '',
                        ],
                    ],
                ],
            ],
        ]) ?>
    </div>
</div>

<?= GridView::widget([
    'dataProvider' => new ActiveDataProvider([
        'query' => $model->getRouteParams(),
        'sort' => [
            'defaultOrder' => ['route_param_order' => SORT_ASC],
        ],
    ]),
    'columns' => [
        [
            'header' => Html::a('创建路由参数', ['route-param/create', 'route_code' => $model->route_code], [
                'class' => 'btn btn-xs btn-success layer-dialog',
                'data' => [
                    'layer-window' => 'self',
                ],
            ]),
            'class' => DialogActionColumn::class,
            'template' => '{view} {update} {active} {inactive}',
            'urlCreator' => static function (string $action, RouteParam $model) {
                return Url::to(['route-param/' . $action, 'id' => $model->route_param_id]);
            },
            'buttonOptions' => [
                'data' => [
                    'layer-window' => 'self',
                ],
            ],
            'buttons' => [
                'active' => static function ($url) {
                    return ActionColumn::newButton('启用', $url, [
                        'class' => 'btn-success',
                        'data' => [
                            'confirm' => '确定要启用吗?',
                            'method' => 'post',
                        ],
                    ]);
                },
                'inactive' => static function ($url) {
                    return ActionColumn::newButton('停用', $url, [
                        'class' => 'btn-danger',
                        'data' => [
                            'confirm' => '确定要停用吗?',
                            'method' => 'post',
                        ],
                    ]);
                },
            ],
            'visibleButtons' => [
                'active' => static function (RouteParam $routeParam) {
                    return $routeParam->route_param_status === RouteParam::STATUS_INACTIVE;
                },
                'inactive' => static function (RouteParam $routeParam) {
                    return $routeParam->route_param_status === RouteParam::STATUS_ACTIVE;
                },
            ],
        ],
        [
            'attribute' => 'route_param_type',
            'format' => ['in', RouteParam::TYPE_LIST],
        ],
        'route_param_name',
        'route_param_value:NJson',
        [
            'attribute' => 'route_param_status',
            'format' => ['in', RouteParam::STATUS_LIST],
        ],
        'route_param_order',
        'route_param_memo',
        'route_param_create_at',
        'route_param_update_at',
    ],
]) ?>
