<?php

use gateway\models\Route;
use gateway\models\RouteSearch;
use gateway\models\Service;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $auditDataProvider ArrayDataProvider */
/* @var $searchModel RouteSearch */

$this->title = '路由管理';

$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

if ($auditDataProvider->getTotalCount()) {
    echo GridView::widget([
        'dataProvider' => $auditDataProvider,
        'options' => [
            'class' => 'box box-warning',
        ],
        'layout' => '<div class="box-body table-responsive no-padding">{items}</div>',
        'rowOptions' => static function (Route $model) {
            return [
                'style' => 'background-color: ' . [
                        Route::STATUS_ACTIVE => '#dff0d8',
                        Route::STATUS_INACTIVE => '#fcf8e3',
                    ][$model->audit->approval->getData('status')],
            ];
        },
        'columns' => [
            [
                'header' => '待审核数据',
                'class' => DialogActionColumn::class,
                'template' => '{view} {audit} {undo}',
                'buttons' => [
                    'audit' => static function ($url) {
                        return DialogActionColumn::newButton('审核', $url, [
                            'class' => 'btn-facebook layer-dialog',
                        ]);
                    },
                    'undo' => static function ($url) {
                        return DialogActionColumn::newButton('撤销', $url, [
                            'class' => 'btn-danger',
                            'data' => [
                                'confirm' => '您确定要撤销该审核吗',
                                'method' => 'post',
                            ],
                        ]);
                    },
                ],
                'visibleButtons' => [
                    'audit' => static fn(Route $model) => $model->audit->approvalEntries->auditable(),
                    'undo' => static fn(Route $model) => $model->audit->audit_creator_id === Yii::$app->getUser()->getId(),
                ],
            ],
            [
                'attribute' => 'route_tenant',
                'format' => ['in', Route::tenants()],
            ],
            [
                'attribute' => 'route_group',
                'value' => 'group',
            ],
            'route_code',
            'route_name',
            [
                'attribute' => 'route_access_type',
                'format' => ['in', ['public' => '公网访问', 'internal' => '内网访问']],
            ],
            [
                'attribute' => 'route_path',
                'format' => function ($routePath) {
                    return Html::tag('div', $routePath, [
                        'style' => 'width: 300px;word-break: break-all;white-space: normal;',
                    ]);
                }
            ],
            [
                'attribute' => 'route_service_code',
                'value' => 'service',
                'format' => function (Service $service) {
                    return Html::a($service->service_name, ['service/view', 'id' => $service->service_id], [
                        'class' => 'layer-dialog',
                    ]);
                },
            ],
            [
                'attribute' => 'route_service_path',
                'format' => function ($routeServicePath) {
                    return Html::tag('div', $routeServicePath, [
                        'style' => 'width: 300px;word-break: break-all;white-space: normal;',
                    ]);
                }
            ],
        ],
    ]);
}

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{view} {update}',
        ],

        [
            'attribute' => 'route_tenant',
            'format' => ['in', Route::tenants()],
        ],
        [
            'attribute' => 'route_group',
            'value' => 'group',
        ],
        [
            'attribute' => 'route_name',
//            'format' => ['truncate', 5],
        ],
        [
            'attribute' => 'route_access_type',
            'format' => ['in', ['public' => '公网访问', 'internal' => '内网访问']],
        ],
        [
            'attribute' => 'route_path',
            'format' => function ($routePath) {
                return Html::tag('div', $routePath, [
                    'style' => 'width: 300px;word-break: break-all;white-space: normal;',
                ]);
            }
        ],
        [
            'attribute' => 'route_service_code',
            'value' => 'service',
            'format' => function (Service $service) {
                return Html::a($service->service_name, ['service/view', 'id' => $service->service_id], [
                    'class' => 'layer-dialog',
                ]);
            },
        ],
        [
            'attribute' => 'route_service_path',
            'format' => function ($routeServicePath) {
                return Html::tag('div', $routeServicePath, [
                    'style' => 'width: 300px;word-break: break-all;white-space: normal;',
                ]);
            }
        ],
        'route_metadata',
        [
            'attribute' => 'route_status',
            'format' => ['in', Route::STATUS_LIST],
        ],
        'route_memo',
        'route_order',
        'route_version',
        'route_create_at',
        'route_update_at',
    ],
]);
