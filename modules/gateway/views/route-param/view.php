<?php

use gateway\models\RouteParam;
use kvmanager\models\KeyValue;
use xlerr\CodeEditor\CodeEditor;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model RouteParam */

$this->title = $model->route_param_type . ':' . $model->route_param_name;

$this->params['breadcrumbs'][] = ['label' => '路由参数管理'];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?php
    $supportDebugPlugins = KeyValue::take('route_config', 'gateway');
    $supportDebugPlugins = (array)($supportDebugPlugins['support_debug_plugins'] ?? null);
    if (in_array($model->route_param_name, $supportDebugPlugins, true)) {
        echo Html::a('脚本调试', ['debug', 'id' => $model->route_param_id], [
            'class' => 'btn btn-warning layer-dialog',
        ]);
    }
    ?>
</p>
<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'options' => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                [
                    'attribute' => 'route_param_type',
                    'format' => ['in', RouteParam::TYPE_LIST],
                ],
                'route_param_name',
                [
                    'attribute' => 'route_param_value',
                    'format' => 'raw',
                    'value' => CodeEditor::widget([
                        'name' => '_route_param_value',
                        'value' => $model->route_param_value,
                        'clientOptions' => [
                            'mode' => CodeEditor::MODE_JSON,
                            'minLines' => 3,
                            'maxLines' => 10,
                            'readOnly' => true,
                        ],
                    ]),
                ],
                'route_param_order',
                [
                    'attribute' => 'route_param_status',
                    'format' => ['in', RouteParam::STATUS_LIST],
                ],
                [
                    'attribute' => 'route_param_memo',
                    'format' => 'raw',
                    'value' => CodeEditor::widget([
                        'name' => '_route_param_memo',
                        'value' => $model->route_param_memo,
                        'clientOptions' => [
                            'mode' => CodeEditor::MODE_JSON,
                            'minLines' => 3,
                            'maxLines' => 10,
                            'readOnly' => true,
                        ],
                    ]),
                ],
                'route_param_create_at',
                'route_param_update_at',
            ],
        ]) ?>
    </div>
</div>
