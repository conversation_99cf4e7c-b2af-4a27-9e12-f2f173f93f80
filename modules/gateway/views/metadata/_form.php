<?php

use gateway\models\Metadata;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model Metadata */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'metadata_type')->widget(Select2::class, [
        'data'       => Metadata::TYPE_LIST,
        'hideSearch' => true,
        'disabled'   => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'metadata_name')->textInput([
        'maxlength' => true,
        'disabled'  => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'metadata_params')->widget(CodeEditor::class, [
        'clientOptions' => [
            'model'    => CodeEditor::MODE_JSON,
            'minLines' => 3,
            'maxLines' => 40,
        ],
    ]) ?>

    <?= $form->field($model, 'metadata_order')->textInput() ?>

    <?= $form->field($model, 'metadata_source_type')->widget(Select2::class, [
        'data'       => Metadata::SOURCE_TYPE_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'metadata_source_script')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode'     => CodeEditor::MODE_Groovy,
            'minLines' => 3,
            'maxLines' => 40,
        ],
    ]) ?>

    <?= $form->field($model, 'metadata_status')->widget(Select2::class, [
        'data'       => Metadata::STATUS_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'metadata_memo')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode'     => CodeEditor::MODE_Text,
            'minLines' => 3,
            'maxLines' => 10,
        ],
    ]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>

<script>
    <?php $this->beginBlock('js') ?>
    const sourceTypeEl = $('#<?= Html::getInputId($model, 'metadata_source_type') ?>'),
        sourceScriptEl = $('#<?= Html::getInputId($model, 'metadata_source_script') ?>')

    sourceTypeEl.on('change', function () {
        if ($(this).val() === '<?= Metadata::SOURCE_TYPE_BUILTIN?>') {
            sourceScriptEl.parents('div.form-group').hide(0);
        } else {
            sourceScriptEl.parents('div.form-group').show(0);
        }
    }).change();
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>