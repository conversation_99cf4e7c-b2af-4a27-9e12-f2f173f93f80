<?php

use gateway\models\Service;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model Service */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'service_code')->textInput([
        'maxlength' => true,
        'disabled'  => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'service_name')->textInput([
        'maxlength' => true,
    ]) ?>

    <?= $form->field($model, 'service_type')->widget(Select2::class, [
        'data'       => Service::TYPE_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'service_uri')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'service_instances') ?>

    <?= $form->field($model, 'service_liveness_probe')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'service_memo')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'service_metadata')->textInput(['maxlength' => true]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', ['class' => 'btn btn-primary']) ?>
</div>

<?php ActiveForm::end(); ?>
