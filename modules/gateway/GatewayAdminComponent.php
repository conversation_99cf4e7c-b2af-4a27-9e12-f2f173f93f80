<?php

namespace gateway;

use gateway\models\debug\RoutePath;
use gateway\models\debug\RoutePlugin;
use GuzzleHttp\RequestOptions;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;

class GatewayAdminComponent extends RequestClient
{
    use ComponentTrait;

    public const GUZZLE_HTTP_STATUS_OK = 200;

    public string $fromSystem = 'BIZ';

    /**
     * 路由路径调试
     *
     * @see https://git.kuainiujinke.com/biz_global/biz-gateway/-/wikis/%E8%B7%AF%E7%94%B1%E8%B7%AF%E5%BE%84%E8%B0%83%E8%AF%95API
     *
     * @param RoutePath $model
     *
     * @return bool
     */
    public function routePathDebug(RoutePath $model): bool
    {
        return $this->post('route/admin/debug/path', [
            RequestOptions::JSON => [
                'type' => 'RoutePathDebug',
                'key' => uuid_create(),
                'from_system' => $this->fromSystem,
                'data' => $model->toArray(),
            ],
        ]);
    }

    /**
     * @param RoutePlugin $model
     *
     * @return bool
     */
    public function routePluginDebug(RoutePlugin $model): bool
    {
        return $this->post('route/admin/debug/plugin', [
            RequestOptions::JSON => [
                'type' => 'RoutePluginDebug',
                'key' => uuid_create(),
                'from_system' => $this->fromSystem,
                'data' => [
                    'input_data' => json_decode($model->inputData, false) ?? $model->inputData,
                    'route_plugin_id' => $model->routePluginId,
                    'assert_scripts' => json_decode($model->assertScripts, false) ?? $model->assertScripts,
                ],
            ],
        ]);
    }
}
