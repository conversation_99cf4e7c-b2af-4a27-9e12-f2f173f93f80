<?php

namespace gateway\controllers;

use gateway\GatewayAdminComponent;
use gateway\models\debug\RoutePlugin;
use gateway\models\Metadata;
use gateway\models\RouteParam;
use gateway\models\RouteParamSearch;
use gateway\models\TestCase;
use kartik\depdrop\DepDropAction;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * RouteParamController implements the CRUD actions for RouteParam model.
 */
class RouteParamController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                    'active' => ['POST'],
                    'inactive' => ['POST'],
                ],
            ],
        ];
    }

    public function actions(): array
    {
        return [
            'name-list' => [
                'class' => DepDropAction::class,
                'outputCallback' => function ($metadataType): array {
                    return Metadata::find()
                        ->where([
                            'metadata_type' => $metadataType,
                        ])
                        ->select([
                            'id' => 'metadata_name',
                            'name' => 'metadata_name',
                        ])
                        ->asArray()
                        ->all();
                },
            ],
        ];
    }

    /**
     * Lists all RouteParam models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new RouteParamSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single RouteParam model.
     *
     * @param int $id ID
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new RouteParam model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @param string $route_code
     *
     * @return string
     */
    public function actionCreate(string $route_code): string
    {
        $model = new RouteParam();

        $model->route_param_route_code = $route_code;

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing RouteParam model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id ID
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionUpdate(int $id): string
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * @param int $id ID
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws StaleObjectException
     * @throws Throwable
     */
    public function actionActive(int $id): Response
    {
        $model = $this->findModel($id);

        $model->route_param_status = RouteParam::STATUS_ACTIVE;
        $model->update();

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * @param int $id ID
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws StaleObjectException|Throwable
     */
    public function actionInactive(int $id): Response
    {
        $model = $this->findModel($id);

        $model->route_param_status = RouteParam::STATUS_INACTIVE;
        $model->update();

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * Finds the RouteParam model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id ID
     *
     * @return RouteParam the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): RouteParam
    {
        if (($model = RouteParam::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionDebug(int $id, int $testCaseId = null): string
    {
        $routeParam = $this->findModel($id);
        $model = new RoutePlugin([
            'routePluginId' => $routeParam->route_param_id,
            'testCase' => $testCaseId,
        ]);
        if ($model->testCase) {
            $testCase = TestCase::findOne($model->testCase);
            $model->inputData = $testCase->test_case_input;
            $model->assertScripts = $testCase->test_case_asserts;
            $model->responseData = $testCase->test_case_output;
        }

        if ($this->request->isPost && $model->load($this->request->post()) && $model->validate()) {
            $client = GatewayAdminComponent::instance();
            if (!$client->routePluginDebug($model)) {
                $model->addError('inputData', 'API:' . $client->getError());
            }

            $model->responseData = Json::encode($client->getResponse());
        }

        return $this->render('debug', [
            'plugin' => $routeParam,
            'pluginName' => $routeParam->route_param_name,
            'model' => $model,
        ]);
    }

    public function actionSaveTestCase($id): Response
    {
        $post = $this->request->post();
        $testCaseId = (int)ArrayHelper::getValue($post, 'RoutePlugin.testCase');
        if ($testCaseId) {
            $testCase = TestCase::findOne($testCaseId);
        } else {
            $testCase = new TestCase();
            $testCase->test_case_name = $this->request->get('test_case_name');
            $testCase->test_case_resource_id = $id;
            $testCase->test_case_resource_type = 'plugin';
        }
        $testCase->test_case_input = ArrayHelper::getValue($post, 'RoutePlugin.inputData');
        $testCase->test_case_asserts = ArrayHelper::getValue($post, 'RoutePlugin.assertScripts');
        $testCase->test_case_output = ArrayHelper::getValue($post, 'RoutePlugin.responseData');
        $session = Yii::$app->getSession();
        if (!$testCase->save()) {
            $session->setFlash('error', '保存测试用例失败:' . Json::encode($testCase->getErrors()));
        } else {
            $session->setFlash('success', '保存测试用例成功');
        }

        return $this->redirect(['debug', 'id' => $id, 'testCaseId' => $testCase->test_case_id]);
    }
}
