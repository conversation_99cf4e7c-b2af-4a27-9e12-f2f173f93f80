<?php

namespace gateway\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use gateway\models\RouteParam;

/**
 * RouteParamSearch represents the model behind the search form about `gateway\models\RouteParam`.
 */
class RouteParamSearch extends RouteParam
{
    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['route_param_id', 'route_param_order'], 'integer'],
            [
                [
                    'route_param_type',
                    'route_param_name',
                    'route_param_value',
                    'route_param_route_code',
                    'route_param_status',
                    'route_param_memo',
                    'route_param_create_at',
                    'route_param_update_at',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = RouteParam::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'route_param_id' => $this->route_param_id,
            'route_param_order' => $this->route_param_order,
            'route_param_create_at' => $this->route_param_create_at,
            'route_param_update_at' => $this->route_param_update_at,
        ]);

        $query->andFilterWhere(['like', 'route_param_type', $this->route_param_type])
            ->andFilterWhere(['like', 'route_param_name', $this->route_param_name])
            ->andFilterWhere(['like', 'route_param_value', $this->route_param_value])
            ->andFilterWhere(['like', 'route_param_route_code', $this->route_param_route_code])
            ->andFilterWhere(['like', 'route_param_status', $this->route_param_status])
            ->andFilterWhere(['like', 'route_param_memo', $this->route_param_memo]);

        return $dataProvider;
    }
}
