<?php

namespace gateway\models;

use yii\data\ActiveDataProvider;

/**
 * MetadataSearch represents the model behind the search form about `gateway\models\Metadata`.
 */
class MetadataSearch extends Metadata
{
    public function formName(): string
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'metadata_type',
                    'metadata_name',
                    'metadata_source_type',
                    'metadata_status',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = Metadata::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'defaultOrder' => ['metadata_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'metadata_type'        => $this->metadata_type,
            'metadata_source_type' => $this->metadata_source_type,
            'metadata_status'      => $this->metadata_status,
        ])->andFilterWhere(['like', 'metadata_name', $this->metadata_name]);

        return $dataProvider;
    }
}
