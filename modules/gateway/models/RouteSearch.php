<?php

namespace gateway\models;

use yii\data\ActiveDataProvider;

/**
 * RouteSearch represents the model behind the search form about `gateway\models\Route`.
 */
class RouteSearch extends Route
{
    public function formName(): string
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'route_tenant',
                    'route_group',
                    'route_access_type',
                    'route_name',
                    'route_service_code',
                    'route_path',
                    'route_status',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Route::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => ['route_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'route_tenant' => $this->route_tenant,
            'route_group' => $this->route_group,
            'route_access_type' => $this->route_access_type,
            'route_service_code' => $this->route_service_code,
            'route_status' => $this->route_status,
        ])->andFilterWhere([
            'and',
            ['like', 'route_name', $this->route_name],
            ['like', 'route_path', $this->route_path],
        ]);

        return $dataProvider;
    }
}
