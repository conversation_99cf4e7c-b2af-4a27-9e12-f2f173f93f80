<?php

namespace system\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * Class OperateLogSearch
 *
 * @package system\models
 */
class OperateLogSearch extends OperateLog
{
    public $startDate;
    public $endDate;
    public $keywords;

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        $defaultDate = Carbon::now()->toDateString();

        return [
            [['startDate'], 'default', 'value' => $defaultDate],
            [['endDate'], 'default', 'value' => $defaultDate],
            [['type'], 'in', 'range' => array_keys(self::typeList())],
            [['keywords'], 'filter', 'filter' => fn($val) => trim((string)$val)],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'attributes'   => ['operate_at'],
                'defaultOrder' => ['operate_at' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'AND',
            ['=', 'type', $this->type],
            ['>=', 'operate_at', $this->startDate],
            ['<', 'operate_at', Carbon::parse($this->endDate)->addDay()->toDateString()],
            [
                'OR',
                ['like', 'request_url', $this->keywords],
                ['like', 'user_name', $this->keywords],
            ],
        ]);

        return $dataProvider;
    }

    public function attributeLabels(): array
    {
        return array_merge(parent::attributeLabels(), [
            'keywords' => '关键词',
        ]);
    }
}
