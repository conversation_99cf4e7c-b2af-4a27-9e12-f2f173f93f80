<?php

namespace system\models;

use common\traits\PortalDbTrait;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%operate_log}}".
 *
 * @property int $id             主键
 * @property string $request_url    请求地址
 * @property string $request_params 请求参数
 * @property string $request_method 请求方法
 * @property int $type           操作类型:1-登录 2-新增 3-编辑 4-删除 5-登出
 * @property string $table          操作的表名称
 * @property string $old_attr       编辑时旧的数据信息
 * @property string $new_attr       新增及编辑时候的最新数据信息
 * @property string $server_ip      服务器IP
 * @property string $client_ip      客户端IP
 * @property int $user_id        用户ID
 * @property string $user_name      用户名
 * @property string $comment        操作说明
 * @property string $operate_at     访问时间
 * @property string $create_at      创建时间
 * @property string $update_at      更新时间
 */
class OperateLog extends ActiveRecord
{
    use PortalDbTrait;

    public const TYPE_LOGIN = 1;
    public const TYPE_INSERT = 2;
    public const TYPE_UPDATE = 3;
    public const TYPE_DELETE = 4;
    public const TYPE_LOGOUT = 5;
    public const TYPE_TRANSFER = 6;
    public const TYPE_WITHDRAW = 7;
    public const TYPE_ACCESS = 8;
    public const TYPE_ACTION = 9;

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%operate_log}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [
                [
                    'request_url',
                    'request_method',
                    'type',
                    'server_ip',
                    'client_ip',
                    'user_id',
                    'user_name',
                    'operate_at',
                ],
                'required',
            ],
            [['request_query', 'request_body', 'old_attr', 'new_attr'], 'string'],
            [['type', 'user_id'], 'integer'],
            [['request_url', 'user_name'], 'string', 'max' => 255],
            [['request_method', 'server_ip', 'client_ip'], 'string', 'max' => 45],
            [['table'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => '主键',
            'request_url' => '请求地址',
            'request_query' => '请求参数',
            'request_body' => '请求体',
            'request_method' => '请求方法',
            'type' => '操作类型',
            'table' => '表名',
            'old_attr' => '旧数据',
            'new_attr' => '新数据',
            'server_ip' => '服务器IP',
            'client_ip' => '客户端IP',
            'user_id' => '用户ID',
            'user_name' => '用户名',
            'operate_at' => '时间',
        ];
    }

    /**
     * 下拉框类型
     *
     * @return array
     */
    public static function typeList(): array
    {
        return [
            self::TYPE_LOGIN => '登录',
            self::TYPE_INSERT => '新增',
            self::TYPE_UPDATE => '编辑',
            self::TYPE_DELETE => '删除',
            self::TYPE_LOGOUT => '登出',
            self::TYPE_TRANSFER => '转账',
            self::TYPE_WITHDRAW => '提现',
            self::TYPE_ACCESS => '访问',
            self::TYPE_ACTION => '操作',
        ];
    }
}
