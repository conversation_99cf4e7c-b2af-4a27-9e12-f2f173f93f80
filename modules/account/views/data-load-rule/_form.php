<?php

use account\models\DataLoadRule;
use xlerr\common\widgets\DatePicker;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model \account\models\DataLoadRule */
?>

<?php
$form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'write_table')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'field_mapping')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode' => CodeEditor::MODE_JSON,
        ],
    ]) ?>

    <?= $form->field($model, 'source_sql')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode' => CodeEditor::MODE_MySQL,
        ],
    ]) ?>

    <?= $form->field($model, 'source_data_source')->widget(Select2::class, [
        'data'       => [
            'gbiz'           => 'gbiz',
            'rbiz'           => 'rbiz',
            'capital'        => 'capital',
            'tq_paysvr'      => 'tq_paysvr',
            'account'        => 'account',
            'biz'            => 'biz',
            'jn_deposit'     => 'jn_deposit',
            'jn_deposit_qjj' => 'jn_deposit_qjj',
            'jn_deposit_rs'  => 'jn_deposit_rs',
        ],
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'target_data_source')->widget(Select2::class, [
        'data'       => [
            'account' => 'account',
        ],
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'status')->widget(Select2::class, [
        'data'       => DataLoadRule::STATUS,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'start_date')->widget(DatePicker::class) ?>

    <?= $form->field($model, 'end_date')->widget(DatePicker::class) ?>

    <?= $form->field($model, 'convert_bean_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'desc')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode'     => CodeEditor::MODE_Text,
            'minLines' => 3,
        ],
    ]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', ['class' => 'btn btn-primary']) ?>
</div>

<?php
ActiveForm::end(); ?>
