<?php

use account\models\DataLoadRule;
use xlerr\CodeEditor\CodeEditor;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model DataLoadRule */

$this->title = '查看';
?>
<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'options' => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                'id',
                'name',
                'write_table',
                [
                    'attribute' => 'field_mapping',
                    'format' => 'raw',
                    'value' => CodeEditor::widget([
                        'id' => 'fm',
                        'name' => 'fm',
                        'value' => $model->field_mapping,
                        'clientOptions' => [
                            'mode' => CodeEditor::MODE_JSON,
                        ],
                    ]),
                ],
                [
                    'attribute' => 'source_sql',
                    'format' => 'raw',
                    'value' => CodeEditor::widget([
                        'id' => 'ss',
                        'name' => 'ss',
                        'value' => $model->source_sql,
                        'clientOptions' => [
                            'mode' => CodeEditor::MODE_MySQL,
                        ],
                    ]),
                ],
                'source_data_source',
                'target_data_source',
                [
                    'attribute' => 'status',
                    'format' => ['in', DataLoadRule::STATUS],
                ],
                'start_date',
                'end_date',
                'convert_bean_name',
                'desc',
                'create_at',
                'updated_at',
            ],
        ]) ?>
    </div>
</div>
