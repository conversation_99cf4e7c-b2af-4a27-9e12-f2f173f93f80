<?php

use account\models\DataLoadRule;
use account\models\DataLoadRuleSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel DataLoadRuleSearch */

$this->title = '数据加载规则管理';
$this->params['breadcrumbs'][] = $this->title;
?>

<?= $this->render('_search', ['model' => $searchModel]); ?>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns' => [
        ['class' => DialogActionColumn::class],

        //        'id',
        'name',
        'write_table',
        'field_mapping:NJson',
        'source_sql:ntext',
        'source_data_source',
        'target_data_source',
        [
            'attribute' => 'status',
            'format' => ['in', DataLoadRule::STATUS],
        ],
        'start_date',
        'end_date',
        'convert_bean_name',
        // 'desc',
        'create_at',
        // 'updated_at',

    ],
]);
