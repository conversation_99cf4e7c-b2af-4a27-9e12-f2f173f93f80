<?php

namespace dcs\approvals;

use common\models\Liabilities;
use waterank\audit\ClientApproval;
use waterank\audit\FlowKeyTrait;
use waterank\audit\models\Audit;
use waterank\audit\task\BusinessEndTask;
use yii\base\UserException;

class LiabilitiesApproval extends ClientApproval
{
    use FlowKeyTrait;

    public function type(): string
    {
        return 'liabilitiesApproval';
    }

    /**
     * @param array $response
     *
     * @return array
     */
    public function submittedCallback(array $response): array
    {
        $id = $this->getData('id');

        return [
            $id => Liabilities::STATUS_AUDITING,
        ];
    }

    /**
     * @param Audit $audit
     * @param       $status
     *
     * @return void
     * @throws UserException
     */
    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        $id = $this->getData('id');

        $liabilitiesStatus = Liabilities::STATUS_FAILURE;
        if ($audit->audit_status === Audit::STATUS_SUCCESS) {
            $liabilitiesStatus = Liabilities::STATUS_SUCCESS;

            BusinessEndTask::process([
                'audit_id' => $audit->audit_id,
                'status' => Audit::BUSINESS_END,
                'status_detail' => [
                    $id => Liabilities::STATUS_SUCCESS,
                ],
                'memo' => '审核通过',
            ]);
        }

        $affected = (int)Liabilities::updateAll([
            'status' => $liabilitiesStatus,
        ], [
            'id' => $id,
            'status' => Liabilities::STATUS_AUDITING,
        ]);
        if ($affected !== 1) {
            throw new UserException('修改状态失败');
        }

        if($liabilitiesStatus === Liabilities::STATUS_SUCCESS){

        }
    }
}
