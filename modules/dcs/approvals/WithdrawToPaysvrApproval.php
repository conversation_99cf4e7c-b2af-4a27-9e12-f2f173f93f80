<?php

namespace dcs\approvals;

use Carbon\Carbon;
use dcs\models\DepbankManual;
use dcs\tasks\DepbankOrderApply;
use Exception;
use Throwable;
use waterank\audit\ClientApproval;
use waterank\audit\FlowKeyTrait;
use waterank\audit\models\Audit;
use waterank\audit\task\BusinessEndTask;
use xlerr\task\models\Task;
use yii\base\UserException;

class WithdrawToPaysvrApproval extends ClientApproval
{
    use FlowKeyTrait;

    /**
     * @param Audit $audit
     * @param $status
     *
     * @return void
     * @throws Throwable
     * @throws UserException
     */
    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        $affected = DepbankManual::updateAll([
            'status' => $audit->audit_status === Audit::STATUS_SUCCESS ? DepbankManual::STATUS_OA_SUCCESS : DepbankManual::STATUS_FAIL,
            'result' => $audit->audit_status === Audit::STATUS_SUCCESS ? 'OA审核成功' : 'OA审核被拒绝',
            'oa_id' => $audit->audit_oa_id
        ], [
            'order_no' => $this->getData('order_no'),
            'status' => DepbankManual::STATUS_OA_PROCESS,
        ]);

        if ($affected !== 1) {
            throw new Exception('修改状态失败');
        }

        if ($audit->audit_status === Audit::STATUS_SUCCESS) {
            DepbankOrderApply::make([
                'orderNo' => $this->getData('order_no'),
                'orderType' => $this->getData('order_type'),
                'amount' => $this->getData('amount'),
                'outAcctNo' => $this->getData('out_account_no'),
                'outAcctBank' => $this->getData('out_account_bank'),
                'inAcctNo' => $this->getData('in_account_no'),
                'inAcctBank' => $this->getData('in_account_bank'),
                'memo' => ''/**@note 请求depbank的memo始终传空 备注是存管自己在维护 */
            ], [
                'task_priority' => Task::PRIORITY_1,
            ]);

            BusinessEndTask::process([
                'audit_id' => $audit->audit_id,
                'status' => Audit::BUSINESS_END,
                'status_detail' => [$this->getData('order_no') => 'success'],
                'finish_time' => Carbon::now()->toDateTimeString(),
                'memo' => '审核通过',
            ]);
        }
    }

    /**
     * @return array
     * @throws Throwable
     */
    public function submittedCallback(array $response): array
    {
        $manualModels = call_user_func_array([DepbankManual::class, 'import'], [$this->getData()]);

        $affected = DepbankManual::updateAll([
            'status' => DepbankManual::STATUS_OA_PROCESS,
            'oa_id' => $response['data']['entry_id']
        ], [
            'order_no' => $manualModels->order_no,
            'status' => DepbankManual::STATUS_NEW,
        ]);

        if ($affected !== 1) {
            throw new Exception('修改状态失败');
        }

        return [
            DepbankManual::STATUS_OA_PROCESS => $manualModels->order_no
        ];
    }
}
