<?php

namespace dcs\components;

use GuzzleHttp\RequestOptions;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;

class ContractsvrComponent extends RequestClient
{
    use ComponentTrait;

    public const GUZZLE_HTTP_STATUS_OK = 200;

    public string $fromSystem = 'BIZ';

    /**
     * https://git.kuainiujinke.com/foundation-services/contractsvr/-/wikis/Api/1.8%E3%80%81%E4%B8%8B%E8%BD%BD%E5%90%88%E5%90%8C%E8%AF%81%E6%98%8E%E6%96%87%E4%BB%B6
     * 下载合同证明文件
     * @param array $data
     * @return bool
     */
    public function evidenceReport(array $data): bool
    {
        return $this->post('v3/contract/evidence-report', [
            RequestOptions::JSON => $data
        ]);
    }
}
