<?php

namespace dcs\api;

use dcs\tasks\AssetInfoQuery;
use dcs\tasks\AssetRepayInfoQuery;
use Throwable;
use yii\rest\Controller;

class FkController extends Controller
{
    protected function verbs(): array
    {
        return [
            '*' => ['post'],
        ];
    }

    /**
     * @return array
     * @throws Throwable
     */
    public function actionFkAssetInfo(): array
    {
        $response = (new AssetRepayInfoQuery())->invoke([
            'itemNo' => $this->request->post('apply_code'),
        ]);

        if (!isset($response['msg'])) {
            $response['msg'] = $response['message'] ?? 'unknow';
        }

        return $response;
    }

    /**
     * 查询资产状态信息接口
     *
     * @return array
     * @throws Throwable
     */
    public function actionQueryAssetsInfo(): array
    {
        return (new AssetInfoQuery())->invoke([
            'itemNo' => $this->request->post('asset_no_list'),
        ]);
    }
}
