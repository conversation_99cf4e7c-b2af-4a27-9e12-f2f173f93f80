<?php

namespace dcs\api;

use dcs\tasks\IndividualAssetQuery;
use Throwable;
use yii\rest\Controller;
use yii\web\Request;

/**
 * @property-read Request $request
 */
class IndividualController extends Controller
{
    /**
     * @return array
     */
    protected function verbs(): array
    {
        return [
            'assets' => ['get'],
        ];
    }

    /**
     * @see https://git.kuainiujinke.com/labs/biz/wikis/biz%E5%AF%B9%E5%A4%96%E5%BC%80%E6%94%BEapi%E6%96%87%E6%A1%A3
     * @return array
     * @throws Throwable
     */
    public function actionAssets(): array
    {
        return (new IndividualAssetQuery())->invoke($this->request->get());
    }
}
