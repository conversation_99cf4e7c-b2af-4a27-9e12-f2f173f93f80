<?php

namespace dcs\controllers;

use dcs\models\CleanNoLoanProvisionRate;
use dcs\models\CleanNoLoanProvisionRateSearch;
use Yii;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * CleanNoLoanProvisionRateController implements the CRUD actions for CleanNoLoanProvisionRate model.
 */
class CleanNoLoanProvisionRateController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'inactive' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * 列表
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new CleanNoLoanProvisionRateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * 创建
     *
     * @return string
     */
    public function actionCreate(): string
    {
        $model = new CleanNoLoanProvisionRate();
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }


    /**
     * 修改模型
     *
     * @param int $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionUpdate(int $id): string
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }


    /**
     * 获取OfflineFeeRule模型
     *
     * @param int $id
     *
     * @return CleanNoLoanProvisionRate
     * @throws NotFoundHttpException
     */
    protected function findModel(int $id): CleanNoLoanProvisionRate
    {
        if (($model = CleanNoLoanProvisionRate::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
