<?php

namespace dcs\controllers;

use Carbon\Carbon;
use dcs\models\PeriodicFileRecord;
use dcs\models\PeriodicFileRecordSearch;
use Throwable;
use Yii;
use yii\db\Transaction;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class PeriodicFileRecordController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
            ],
        ];
    }

    public function actionIndex(): string
    {
        $searchModel = new PeriodicFileRecordSearch();
        $dataProvider = $searchModel->search($this->request->get());

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @param $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * @throws NotFoundHttpException
     */
    protected function findModel(int $id): PeriodicFileRecord
    {
        if (($model = PeriodicFileRecord::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }


    public function actionCancel(int $id): Response
    {
        /** @var Transaction $transaction */
        $transaction = PeriodicFileRecord::getDb()->beginTransaction();

        try {
            $affected = PeriodicFileRecord::updateAll(
                [
                    'periodic_file_record_status' => 'canceled',
                ],
                [
                    'periodic_file_record_id' => $id,
                    'periodic_file_record_status' => [
                        'initial', 'enqueued'
                    ]
                ]
            );

            if ($affected >= 0) {
                $transaction->commit();
            }
            Yii::$app->getSession()->setFlash('success', '更新成功!');
        } catch (Throwable $exception) {
            $transaction->rollBack();
            Yii::$app->getSession()->setFlash('error', '更新失败!');
        }
        return $this->redirect($this->request->getReferrer());
    }

    public function actionBatchReset(): Response
    {
        $ids = $this->request->post('ids');
        $ids = explode(',', $ids);
        if (empty($ids)) {
            return $this->asJson([
                'code' => -1,
                'message' => '请选择需要重置的记录!',
            ]);
        }


        /** @var Transaction $transaction */
        $transaction = PeriodicFileRecord::getDb()->beginTransaction();
        try {
            PeriodicFileRecord::updateAll(
                [
                    'periodic_file_record_status' => 'initial',
                    'periodic_file_record_target_file_url' => '',
                    'periodic_file_record_store_file_url' => '',
                    'periodic_file_record_memo' => '',
                    'periodic_file_record_execute_time' => Carbon::now()->toDateTimeString()
                ],
                [
                    'periodic_file_record_id' => $ids,
                ]
            );

            $transaction->commit();

            return $this->asJson([
                'code' => 0,
                'message' => '重推成功!',
            ]);
        } catch (Throwable $exception) {
            $transaction->rollBack();

            return $this->asJson([
                'code' => -1,
                'message' => $exception->getMessage(),
            ]);
        }
    }
}
