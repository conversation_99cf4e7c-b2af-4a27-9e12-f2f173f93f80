<?php

namespace dcs\controllers;

use dcs\models\AssetReverse;
use dcs\models\AssetReverseSearch;
use Exception;
use Throwable;
use Yii;
use yii\base\UserException;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\Response;

/**
 * 资产异常结清
 * AccountController implements the CRUD actions for Account model.
 */
class AssetReverseController extends Controller
{
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'confirm' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Account models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel  = new AssetReverseSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel'  => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new Account model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return string
     * @throws Throwable
     * @throws \yii\db\Exception
     */
    public function actionCreate(): string
    {
        $model   = new AssetReverse();
        $request = Yii::$app->getRequest();

        if ($request->isPost) {
            $model->load($request->post());
            if ($model->import()) {
                return Html::script('window.top.reloadCurrentTab()');
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * 确认结清
     *
     * @return Response
     */
    public function actionConfirm(): Response
    {
        $session = Yii::$app->getSession();
        $ids     = Yii::$app->getRequest()->post('selection');

        $transaction = AssetReverse::getDb()->beginTransaction();
        try {
            if (empty($ids)) {
                throw new UserException('请选择需要结清的数据');
            }

            $result = AssetReverse::updateAll([
                'status' => AssetReverse::STATUS_SUCCESS,
            ], [
                'status' => AssetReverse::STATUS_NEED_CONFIRM,
                'id'     => $ids,
            ]);

            if (count($ids) !== $result) {
                throw new UserException('操作失败');
            }

            $session->setFlash('success', '操作成功');
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            $session->setFlash('error', $e->getMessage());
        }

        return $this->redirect(['index']);
    }
}
