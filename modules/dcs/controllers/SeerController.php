<?php

namespace dcs\controllers;

use Carbon\Carbon;
use common\models\Asset;
use dcs\services\SeerDepositService;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\helpers\CsvHelper;
use yii\base\InvalidConfigException;
use yii\db\Exception;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\RangeNotSatisfiableHttpException;
use yii\web\Request;
use yii\web\Response;

/**
 * @property Request $request
 */
class SeerController extends Controller
{
    /**
     * @return string|Response
     * @throws Exception
     * @throws InvalidConfigException
     * @throws KVException
     * @throws RangeNotSatisfiableHttpException
     */
    public function actionDeposit()
    {
        $seerService = new SeerDepositService([
            'channelList' => array_intersect_key( // 获取有保证金的资金方列表
                Asset::channelList(true),
                (array)(KeyValue::take('biz_offline_rate_map')['deposit'] ?? null)
            ),
        ]);

        $dataProvider = $seerService->search($this->request->get());

        if ($this->request->get('download')) {
            $fileName = sprintf('保证金预估_%s.csv', Carbon::now()->toDateString()); //设置文件名

            $stream = CsvHelper::build($dataProvider->getModels(), $this->columns());

            return $this->response->sendStreamAsFile($stream, $fileName);
        }

        return $this->render('deposit', [
            'searchModel' => $seerService,
            'dataProvider' => $dataProvider,
            'columns' => $this->columns(),
        ]);
    }

    /**
     * @return array
     * @throws KVException
     */
    public function columns(): array
    {
        return [
            [
                'label' => '资方',
                'attribute' => 'channel',
                'format' => ['in', Asset::channelList()],
            ],
            [
                'label' => '统计日期',
                'attribute' => 'reportDate',
            ],
            [
                'label' => '星期',
                'format' => 'dayOfWeekHuman',
                'value' => function ($row) {
                    return Carbon::parse($row['reportDate'])->dayOfWeek;
                },
            ],
            [
                'label' => '期初实际存量余额',
                'class' => MoneyDataColumn::class,
                'attribute' => 'initialLoanBalance',
            ],
            [
                'label' => '实际放款',
                'class' => MoneyDataColumn::class,
                'attribute' => 'actualLoanAmount',
            ],
            [
                'label' => '实际还款',
                'class' => MoneyDataColumn::class,
                'attribute' => 'actualRepayAmount',
            ],
            [
                'label' => '期末实际存量余额',
                'class' => MoneyDataColumn::class,
                'attribute' => 'loanBalance',
            ],
            [
                'label' => '实际保证金增减',
                'class' => MoneyDataColumn::class,
                'attribute' => 'actualAmountChange',
            ],
            [
                'label' => '实际保证金余额',
                'class' => MoneyDataColumn::class,
                'attribute' => 'actualAmount',
            ],
            [
                'label' => '实际保证金比例',
                'value' => function ($data) {
                    // 实际保证金余额
                    $actualAmount = ArrayHelper::getValue($data, 'actualAmount', 0);
                    // 期末实际存量余额
                    $loanBalance = ArrayHelper::getValue($data, 'loanBalance', 0);

                    if ($loanBalance == 0) {
                        return '-';
                    }

                    return number_format($actualAmount / $loanBalance * 100, 2, '.', '').'%';
                },
            ],
            [
                'label' => '应计保证金余额',
                'class' => MoneyDataColumn::class,
                'attribute' => 'depositAmount',
            ],
            [
                'label' => '保证金比例',
                'value' => function ($data) {
                    $rate = ArrayHelper::getValue($data, 'rate', '');

                    return $rate === '' ? 0 : ($rate * 100).'%';
                },
            ],
            [
                'label' => '放款计划',
                'class' => MoneyDataColumn::class,
                'attribute' => 'predictLoan',
            ],
            [
                'label' => '预计还款本金',
                'class' => MoneyDataColumn::class,
                'attribute' => 'predictRepay',
            ],
            [
                'label' => '预测存量余额',
                'class' => MoneyDataColumn::class,
                'attribute' => 'predictLoanBalance',
            ],
            [
                'label' => '预测保证金应计余额',
                'class' => MoneyDataColumn::class,
                'attribute' => 'predictDepositAmount',
            ],
            [
                'label' => '预计补充保证金差额',
                'class' => MoneyDataColumn::class,
                'attribute' => 'predictSupplementaryDepositAmount',
            ],
        ];
    }
}
