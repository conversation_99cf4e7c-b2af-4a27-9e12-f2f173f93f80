<?php

namespace dcs\controllers;

use Carbon\Carbon;
use dcs\approvals\OfflineFeeRuleApproval;
use dcs\models\OfflineFeeDatasource;
use dcs\models\OfflineFeeReBrush;
use dcs\models\OfflineFeeRule;
use dcs\models\OfflineFeeRuleSearch;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\ArrayDataProvider;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\helpers\Url;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

use function xlerr\adminlte\userFullName;

/**
 * OfflineFeeRuleController implements the CRUD actions for OfflineFeeRule model.
 */
class OfflineFeeRuleController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'change' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * 列表
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new OfflineFeeRuleSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        Url::remember();

        $auditing = (new OfflineFeeRuleApproval())->queryNotEndOfBusiness();

        $auditDataProvider = new ArrayDataProvider([
            'modelClass' => OfflineFeeRule::class,
            'key' => 'id',
            'allModels' => $auditing,
            'pagination' => false,
        ]);

        $dataProvider->query->andFilterWhere(['not in', 'id', array_column($auditing, 'id')]);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'auditDataProvider' => $auditDataProvider,
        ]);
    }

    public function actionPreview($assetLoanChannel, $subFeeType, $startDate): Response
    {
        return $this->actionAudit($assetLoanChannel, $subFeeType, $startDate);
    }

    public function actionAudit($assetLoanChannel, $subFeeType, $startDate): Response
    {
        $url = OfflineFeeRuleApproval::genAuditPreviewUrl($assetLoanChannel, $subFeeType, $startDate);

        return $this->redirect($url);
    }

    public function actionUndo($assetLoanChannel, $subFeeType, $startDate): Response
    {
        $approval = new OfflineFeeRuleApproval();
        $model = current(
            $approval->queryNotEndOfBusiness([
                'asset_loan_channel' => $assetLoanChannel,
                'sub_fee_type' => $subFeeType,
                'start_date' => $startDate,
            ])
        );
        try {
            if (empty($model)) {
                throw new UserException('数据已处理');
            }
            /** @var OfflineFeeRuleApproval $approval */
            $approval = $model->audit->getApproval();
            $approval->revoke('撤销');
        } catch (Throwable $e) {
            Yii::$app->getSession()->setFlash('warning', $e->getMessage());
        }

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * 复制已存在的模型并新建
     *
     * @param $id
     *
     * @return string|Response
     * @throws Throwable
     * @throws InvalidConfigException
     * @throws UserException
     * @throws NotFoundHttpException
     */
    public function actionCopy($id)
    {
        $model = $this->findModel($id);

        $model->isNewRecord = true;
        if ($model->data_source_id != null) {
            $offlineFeeDatasource = OfflineFeeDatasource::findOne($model->data_source_id);
            $model->data_source = $offlineFeeDatasource->data_source;
            $model->data_source_sql = $offlineFeeDatasource->data_source_sql;
        }
        return $this->actionCreate($model);
    }

    /**
     * 创建新规则配置
     *
     * @param OfflineFeeRule|null $model
     *
     * @return string|Response
     * @throws InvalidConfigException
     * @throws Throwable
     * @throws UserException
     */
    public function actionCreate(OfflineFeeRule $model = null)
    {
        $model = $model ?? new OfflineFeeRule();
        if ($model->load($this->request->post()) && $model->validate()) {
            $model->version = 0;
            $model->create_user = userFullName();
            $model->create_at = Carbon::now()->toDateTimeString();
            $attributes = $model->attributes;
            unset($attributes['id']);

            $approval = new OfflineFeeRuleApproval([
                'action' => 'create',
                'new' => $attributes,
            ], [
                'backUrl' => Url::to(['index']),
            ]);

            // 当前提交的数据不能存在于待审核列表中
            $auditing = $approval->queryNotEndOfBusiness([
                'asset_loan_channel' => $model->asset_loan_channel,
                'sub_fee_type' => $model->sub_fee_type,
                'start_date' => $model->start_date,
            ]);
            if (empty($auditing)) {
                return $this->redirect($approval->audit());
            }

            Yii::$app->getSession()->setFlash('error', '已提交审核, 请确认是否重复提交');
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }


    /**
     * 改变状态
     *
     * @param $id
     * @param $changeStatus
     *
     * @return Response
     * @throws StaleObjectException
     * @throws NotFoundHttpException
     */
    public function actionChange($id, $changeStatus): Response
    {
        $model = $this->findModel($id);

        $model->status = $changeStatus;

        $model->update();

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * 修改模型
     *
     * @param $id
     *
     * @return string|Response
     * @throws Throwable
     * @throws InvalidConfigException
     * @throws UserException
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load($this->request->post()) && $model->validate()) {
            $approval = new OfflineFeeRuleApproval([
                'action' => 'update',
                'new' => $model->attributes,
                'old' => $model->oldAttributes,
            ], [
                'backUrl' => Url::to([
                    'index',
                ]),
            ]);

            $auditing = $approval->queryNotEndOfBusiness([
                'asset_loan_channel' => $model->asset_loan_channel,
                'sub_fee_type' => $model->sub_fee_type,
                'start_date' => $model->start_date,
            ]);
            if (empty($auditing)) {
                return $this->redirect($approval->audit());
            }

            Yii::$app->getSession()->setFlash('error', '已提交审核, 请确认是否重复提交');
        }
        if ($model->data_source_id != null) {
            $offlineFeeDatasource = OfflineFeeDatasource::findOne($model->data_source_id);
            $model->data_source = $offlineFeeDatasource->data_source;
            $model->data_source_sql = $offlineFeeDatasource->data_source_sql;
        }
        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * 查看
     *
     * @param $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id): string
    {
        $model = $this->findModel($id);
        if ($model->data_source_id != null) {
            $offlineFeeDatasource = OfflineFeeDatasource::findOne($model->data_source_id);
            $model->data_source = $offlineFeeDatasource->data_source;
            $model->data_source_sql = $offlineFeeDatasource->data_source_sql;
        }
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    public function actionReBrush(OfflineFeeReBrush $model = null)
    {
        $model = $model ?? new OfflineFeeReBrush();
        if ($model->load($this->request->post()) && $model->validate()) {
            $model->createReBrushTask();
            Yii::$app->getSession()->setFlash('success', '提交成功!');
            return $this->redirect('index');
        }
        $model = new OfflineFeeReBrush();
        return $this->render('re-brush', [
            'model' => $model,
        ]);
    }

    /**
     * 获取OfflineFeeRule模型
     *
     * @param $id
     *
     * @return OfflineFeeRule
     * @throws NotFoundHttpException
     */
    protected function findModel($id): OfflineFeeRule
    {
        if (($model = OfflineFeeRule::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
