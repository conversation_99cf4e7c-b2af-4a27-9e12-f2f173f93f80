<?php

namespace dcs\controllers;

use dcs\models\IncomeVerification;
use dcs\models\IncomeVerificationCheck;
use dcs\models\IncomeVerificationSearch;
use kartik\depdrop\DepDropAction;
use Xlerr\SettlementFlow\Models\Rule;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * IncomeVerificationController implements the CRUD actions for IncomeVerification model.
 */
class IncomeVerificationController extends Controller
{
    public function actions(): array
    {
        return [
            'rule-list' => [
                'class' => DepDropAction::class,
                'outputCallback' => function ($channel): array {
                    $rules = Rule::find()
                        ->where([
                            'inc_type' => Rule::INC_TYPE_REVENUE,
                            'channel' => $channel
                        ])
                        ->select([
                            'id',
                            'name' => 'fee_type',
                        ])
                        ->asArray()
                        ->all();

                    $feeTypeList = Rule::feeTypeList();

                    return array_map(static function ($item) use ($feeTypeList) {
                        $item['name'] = $feeTypeList[$item['name']] ?? $item['name'];

                        return $item;
                    }, $rules);
                }
            ]
        ];
    }

    /**
     * Lists all IncomeVerification models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new IncomeVerificationSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionList(): Response
    {
        $searchModel = new IncomeVerificationSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);
        $data = $dataProvider->query
            ->andWhere([
                'status' => IncomeVerification::STATUS_INIT
            ])
            ->select([
                'id',
                'memo',
                'amount',
                'flow_trade_date'
            ])
            ->asArray()
            ->all();

        $data = array_map(static function ($row) {
            $row['id'] *= 1;
            $row['amount'] *= 1;

            return $row;
        }, $data);

        return $this->asJson([
            'code' => 0,
            'data' => $data,
            'msg' => 'ok'
        ]);
    }

    /**
     * Displays a single IncomeVerification model.
     *
     * @param int $id ID
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Updates an existing IncomeVerification model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id ID
     * @param string $scenario
     *
     * @return string
     * @throws NotFoundHttpException
     * @throws UserException
     * @throws InvalidConfigException
     */
    public function actionCheck(int $id, string $scenario = IncomeVerification::VERIFICATION_TYPE_OTHER): string
    {
        $model = IncomeVerificationCheck::findOne($id);
        if (!$model) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }

        if ($model->status !== IncomeVerification::STATUS_INIT) {
            throw new UserException('状态异常, 请刷新页面后重试');
        }
        $model->setScenario($scenario);
        $model->verification_type = $scenario;
        $model->bill_start_date = '';
        $model->bill_end_date = '';

        if ($this->request->isPost) {
            $model->load($this->request->post());
            $model->verification_user = Yii::$app->user->id;
            if ($model->check()) {
                return Html::script('window.top.reloadCurrentTab()');
            }
        }

        return $this->render('check', [
            'model' => $model,
        ]);
    }

    /**
     * Finds the IncomeVerification model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id ID
     *
     * @return IncomeVerification the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): IncomeVerification
    {
        if (($model = IncomeVerification::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
