<?php

namespace dcs\controllers;

use common\models\DepositBill;
use common\models\DepositBillSearch;
use kvmanager\KVException;
use system\tasks\AdvancedSendEmail;
use Throwable;
use Xlerr\SettlementFlow\Models\Rule;
use Yii;
use yii\base\UserException;
use yii\db\Exception;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

use function xlerr\desensitise\decrypt;

/**
 * DepositBillController implements the CRUD actions for DepositBill model.
 *
 * @deprecated
 */
class DepositBillController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all DepositBill models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new DepositBillSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @param $id
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws KVException
     */
    public function actionRepair($id)
    {
        $session = Yii::$app->getSession();
        $model = $this->findModel($id);
        if ($model->repair()) {
            $session->setFlash('success', '修复成功');
        } else {
            $session->setFlash('error', '操作失败，请重试');
        }

        return $this->redirect(Yii::$app->getRequest()->getReferrer());
    }

    /**
     * @param $id
     *
     * @return Response
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws Throwable
     */
    public function actionConfirmed($id)
    {
        $session = Yii::$app->getSession();
        $depositBill = $this->findModel($id);

        $mailConfig = DepositBill::config();
        $mailConfig = $mailConfig[$depositBill->channel][$depositBill->type]['mail'] ??
            $mailConfig[$depositBill->channel]['mail'] ?? null;
        if (empty($mailConfig)) {
            throw new UserException('缺少邮件配置');
        }

        $depositBill->status = 'success';

        /**
         * @var Rule $rule
         */
        $rule = Rule::find()->where([
            'channel' => $depositBill->channel,
            'fee_type' => 'deposit',
            'inc_type' => Rule::INC_TYPE_EXPENSES,
        ])->one();

        if (!$rule) {
            throw new UserException('暂无该资方线下费用规则!');
        }
        $transaction = DepositBill::getDb()->beginTransaction();
        try {
            if (!$depositBill->update()) {
                throw new UserException(json_encode($depositBill->getErrors()));
            }

            //            $mailConfig             = [
            //                'from'     => '<EMAIL>',
            //                'to'       => ['<EMAIL>'],
            //                'cc'       => ['<EMAIL>'],
            //                'subject'  => '保证金测试邮件',
            //            ];
            AdvancedSendEmail::make(array_merge($mailConfig, [
                'compose' => [
                    ['html' => 'deposit-bill'],
                    [
                        'date' => $depositBill->date,
                        'paymentCompany' => decrypt($rule->payment_company_encrypt, true),
                        'receiveCompany' => decrypt($rule->receive_company_encrypt, true),
                        'channel' => $depositBill->channel,
                        'balance' => $depositBill->balance,
                        'typeLabel' => $depositBill->type === DepositBill::TYPE_DEPOSIT ? '保证金' : '代偿金',
                    ],
                ],
            ]));

            $transaction->commit();

            $session->setFlash('success', '操作成功');

            return $this->redirect(Yii::$app->getRequest()->getReferrer());
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @return Response
     * @throws NotFoundHttpException
     * @throws StaleObjectException
     * @throws Throwable
     */
    public function actionSwitchStatus()
    {
        $depositBill = $this->findModel(Yii::$app->request->post('id'));

        $depositBill->status = Yii::$app->request->post('status');

        if ($depositBill->update()) {
            Yii::$app->session->setFlash('success', '操作成功');
        } else {
            Yii::$app->session->set('error', '操作失败');
        }

        return $this->redirect(['index']);
    }

    /**
     * Displays a single DepositBill model.
     *
     * @param int $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new DepositBill model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new DepositBill();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing DepositBill model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing DepositBill model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     * @throws StaleObjectException
     * @throws Throwable
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the DepositBill model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return DepositBill the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = DepositBill::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
