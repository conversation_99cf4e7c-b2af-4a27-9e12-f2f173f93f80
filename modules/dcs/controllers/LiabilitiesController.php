<?php

namespace dcs\controllers;

use Carbon\Carbon;
use common\models\Liabilities;
use common\models\LiabilitiesCostAllocatePlan;
use common\models\LiabilitiesSearch;
use common\models\LiabilitiesTrans;
use cpm\models\CapitalChannel;
use dcs\approvals\LiabilitiesApproval;
use Throwable;
use xlerr\common\helpers\MoneyHelper;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\Exception;
use yii\db\Transaction;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * LiabilitiesController implements the CRUD actions for Liabilities model.
 */
class LiabilitiesController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'submit' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Liabilities models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new LiabilitiesSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new Liabilities model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return string
     */
    public function actionCreate(): string
    {
        $model = new Liabilities();

        if ($this->request->isPost) {
            try {
                if ($model->create($this->request->post())) {
                    return Html::script('window.top.reloadCurrentTab()');
                }
            } catch (Throwable $e) {
                Yii::$app->session->addFlash('error', '操作失败: ' . $e->getMessage());
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * @param int $id ID
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws InvalidConfigException
     * @throws UserException
     */
    public function actionSubmit(int $id): Response
    {
        $referrer = $this->request->getReferrer();

        $model = $this->findModel($id);
        if ($model->status !== Liabilities::STATUS_NEW) {
            Yii::$app->session->setFlash('warning', '操作失败, 请重试...');

            return $this->redirect($referrer);
        }

        /** @var Transaction $trans */
        $trans = Liabilities::getDb()->beginTransaction();
        try {
            $affected = (int)Liabilities::updateAll([
                'status' => Liabilities::STATUS_AUDITING,
            ], [
                'id' => $id,
                'status' => Liabilities::STATUS_NEW,
            ]);

            if ($affected !== 1) {
                throw new UserException('修改状态失败');
            }

            $audit = new LiabilitiesApproval(array_merge($model->attributes, [
                'loan_channel_text' => CapitalChannel::name($model->loan_channel),
                'amount_human' => MoneyHelper::f2y($model->amount),
                'currency' => Liabilities::CURRENCY_LIST[$model->currency] ?? $model->currency,
                'rate_human' => $model->getYearRate() . '%',
                'company_id' => $model->settlementRuleConfig->company_id ?? null,
                'company_name' => $model->settlementRuleConfig->company_name ?? null,
            ]), [
                'operator' => $model->created_by,
            ]);

            $audit->audit();
            $trans->commit();

            return $this->redirect($this->request->referrer);
        } catch (Throwable $e) {
            $trans->rollBack();
            throw $e;
        }
    }

    /**
     * @param int $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * @param int $id
     *
     * @return Response|string
     * @throws NotFoundHttpException
     */
    public function actionSettled(int $id)
    {
        $model = $this->findModel($id);
        if ($model->load($this->request->post()) && $model->validate()) {
            $result = Liabilities::updateAll([
                'rate' => $model->rate,
                'period_days' => $model->period_days,
                'origin_due_at' => $model->getOldAttribute('due_at'),
                'due_at' => $model->due_at,
            ], 'id = :id and origin_due_at is null', [
                'id' => $model->id,
            ]);
            if ($result) {
                Yii::$app->getSession()->setFlash('success', '操作成功');

                return $this->redirect(['index']);
            }

            Yii::$app->getSession()->setFlash('error', '保存失败, 请重试');
        }

        return $this->render('settled', [
            'model' => $model,
        ]);
    }

    /**
     * @param int $id
     * @param string $version
     *
     * @return string|Response
     * @throws NotFoundHttpException|Throwable
     */
    public function actionCostAllocatePlan(int $id, string $version = '')
    {
        $model = $this->findModel($id);

        if ($this->request->isPost) {
            $plan = new LiabilitiesCostAllocatePlan([
                'liabilitiesId' => $model->id,
            ]);

            try {
                if ($plan->load($this->request->post()) && $plan->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            } catch (Throwable $e) {
                Yii::$app->session->setFlash('error', $e->getMessage());
            }
        } elseif ($version) {
            $plan = $model->getCostAllocatePlan($version);
        } else {
            $plan = $model->createCostAllocatePlan();
        }

        return $this->render('cost-allocate-plan', [
            'model' => $model,
            'costAllocatePlan' => $plan,
        ]);
    }

    /**
     * 根据在贷余额计算分摊比例
     *
     * @param string $date
     *
     * @return Response
     */
    public function actionLoanBalanceAllocateRate(string $date): Response
    {
        [$allocateRate, $sql] = Liabilities::allocateRate($date, false);

        return $this->asJson([
            'config' => $allocateRate,
            'sql' => $sql,
        ]);
    }

    /**
     * @param int $id
     *
     * @return string|Response
     * @throws NotFoundHttpException
     */
    public function actionRepayPlan(int $id)
    {
        $model = $this->findModel($id);
        $calculatedPlans = [];
        if ($this->request->isPost) {
            $repaymentPlans = $this->request->post('repaymentPlan');

            foreach ($repaymentPlans['period'] as $index => $period) {
                $calculatedPlans[] = [
                    'period' => $period,
                    'due_at' => $repaymentPlans['due_at'][$index],
                    'principal' => $repaymentPlans['principal'][$index],
                    'interest' => $repaymentPlans['interest'][$index],
                    'min_repayment' => $repaymentPlans['min_repayment'][$index],
                    'remaining_principal' => $repaymentPlans['remaining_principal'][$index],
                ];
            }

            $totalPrincipal = array_sum(array_column($calculatedPlans, 'principal'));

            if ($model->amount !== $totalPrincipal) {
                $model->addError('amount', vsprintf('应还本金总额: %s, 与借款金额: %s, 不一致!', [
                    MoneyHelper::f2y($totalPrincipal, true),
                    MoneyHelper::f2y($model->amount, true),
                ]));
            } else {
                /** @var Transaction $transaction */
                $transaction = Liabilities::getDb()->beginTransaction();
                $session = Yii::$app->getSession();
                try {
                    if ($model->load($this->request->post()) && $model->save(true, ['periods'])) {
                        $grantAt = $model->grant_at;
                        $userId = Yii::$app->user->id;
                        $data = [];
                        foreach ($calculatedPlans as $plan) {
                            array_push($data, [
                                'liabilities_id' => $model->id,
                                'period' => $plan['period'],
                                'type' => 'principal',
                                'amount' => $plan['principal'],
                                'grant_at' => $grantAt,
                                'due_at' => $plan['due_at'],
                                'created_by' => $userId,
                                'updated_by' => $userId,
                            ], [
                                'liabilities_id' => $model->id,
                                'period' => $plan['period'],
                                'type' => 'interest',
                                'amount' => $plan['interest'],
                                'grant_at' => $grantAt,
                                'due_at' => $plan['due_at'],
                                'created_by' => $userId,
                                'updated_by' => $userId,
                            ]);

                            $grantAt = Carbon::parse($plan['due_at'])->addDay()->toDateString();
                        }

                        LiabilitiesTrans::deleteAll(['liabilities_id' => $model->id]);

                        $affected = LiabilitiesTrans::getDb()
                            ->createCommand()
                            ->batchInsert(LiabilitiesTrans::tableName(), [
                                'liabilities_id',
                                'period',
                                'type',
                                'amount',
                                'grant_at',
                                'due_at',
                                'created_by',
                                'updated_by',
                            ], $data)
                            ->execute();

                        if ($affected !== count($data)) {
                            throw new UserException('保存还款计划失败');
                        }
                    }

                    $transaction->commit();

                    $session->setFlash('success', '保存成功!');

                    return $this->redirect(['view', 'id' => $model->id]);
                } catch (Throwable $e) {
                    $transaction->rollBack();

                    $session->setFlash('error', $e->getMessage());
                }
            }
        } else {
            $repaymentPlans = LiabilitiesTrans::find()
                ->where(['liabilities_id' => $id])
                ->orderBy(['period' => SORT_ASC, 'type' => SORT_ASC])
                ->all();

            $remainingPrincipal = $model->amount;
            $currentPeriod = 0;

            /** @var LiabilitiesTrans $plan */
            foreach ($repaymentPlans as $plan) {
                if ($plan->period !== $currentPeriod) {
                    $currentPeriod = $plan->period;
                }

                if ($plan->type === 'principal') {
                    $principalPayment = $plan->amount;
                    $remainingPrincipal -= $principalPayment;
                } elseif ($plan->type === 'interest') {
                    $interestPayment = $plan->amount;
                }

                if (isset($principalPayment, $interestPayment)) {
                    $minRepayment = $principalPayment + $interestPayment;

                    $calculatedPlans[] = [
                        'period' => $plan->period,
                        'due_at' => Carbon::parse($plan->due_at)->toDateString(),
                        'principal' => $principalPayment,
                        'interest' => $interestPayment,
                        'min_repayment' => $minRepayment,
                        'remaining_principal' => $remainingPrincipal
                    ];

                    unset($principalPayment, $interestPayment);
                }
            }
        }

        return $this->render('repay-plan', [
            'model' => $model,
            'calculatedPlans' => $calculatedPlans,
        ]);
    }

    /**
     * Finds the Liabilities model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id ID
     *
     * @return Liabilities the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): Liabilities
    {
        if (($model = Liabilities::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
