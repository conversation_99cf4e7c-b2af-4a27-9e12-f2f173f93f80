<?php

use dcs\models\OwnLoanRechargePlan;
use dcs\models\OwnLoanRechargePlanSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel OwnLoanRechargePlanSearch */

$this->title = '小贷充值计划';

$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{update} ',
        ],
        [
            'attribute' => 'channel',
            'format' => ['in', OwnLoanRechargePlan::channelList()],

        ],
        [
            'attribute' => 'type',
            'format' => ['in', OwnLoanRechargePlan::$typeList],
        ],
        [
            'attribute' => 'deposit',
            'format' => ['in', OwnLoanRechargePlan::depositList()],
        ],
        [
            'attribute' => 'amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'low_channel_balance',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'comment',
            'format' => ['truncate', 10],
        ],

        [
            'attribute' => 'start_date',
        ],

        [
            'attribute' => 'create_user',
        ],

        [
            'attribute' => 'create_at',
        ],
        [
            'attribute' => 'update_user',
        ],
        [
            'attribute' => 'update_at',
        ],
    ],
]);
