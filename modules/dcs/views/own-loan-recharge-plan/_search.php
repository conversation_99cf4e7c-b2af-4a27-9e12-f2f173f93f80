<?php

use dcs\models\OwnLoanRechargePlan;
use dcs\models\OwnLoanRechargePlanSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model OwnLoanRechargePlanSearch */
/* @var $form ActiveForm */
?>
<div class="box search">
    <div class="box-header with-border">
        <h3 class="box-title">搜索</h3>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= $form->field($model, 'type', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 150px',
            ],
        ])->widget(Select2::class, [
            'data' => OwnLoanRechargePlan::$typeList,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('type'),
            ],
        ]) ?>


        <?= $form->field($model, 'channel', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 180px',
            ],
        ])->widget(Select2::class, [
            'data' => OwnLoanRechargePlan::channelList(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('channel'),
            ],
        ]) ?>

        <?= $form->field($model, 'start_date')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '生效时间',
                'autocomplete' => 'off',
            ],
        ]) ?>


        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']); ?>
        <?= Html::a('新建', ['create'], [
            'class' => 'btn btn-success layer-dialog',
        ]) ?>
        <?php ActiveForm::end() ?>
    </div>
</div>

