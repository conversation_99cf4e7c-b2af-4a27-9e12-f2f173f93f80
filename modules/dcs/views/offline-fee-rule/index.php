<?php

use dcs\models\OfflineFeeRule;
use dcs\models\OfflineFeeRuleSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\data\ArrayDataProvider;
use yii\helpers\StringHelper;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $auditDataProvider ArrayDataProvider */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel OfflineFeeRuleSearch */

$this->title = '线下费用计算规则';

$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

if ($auditDataProvider->getTotalCount()) {
    echo GridView::widget([
        'dataProvider' => $auditDataProvider,
        'options' => [
            'class' => 'box box-warning',
        ],
        'layout' => '{items}',
        'rowOptions' => function (OfflineFeeRule $model) {
            return [
                'data' => [
                    'new' => $model->attributes,
                    'old' => $model->oldAttributes,
                ],
                'style' => 'background-color: ' . [
                        'create' => '#dff0d8',
                        'update' => '#fcf8e3',
                    ][$model->audit->approval->getData('action')],
            ];
        },
        'columns' => [
            [
                'header' => '待审核数据',
                'class' => ActionColumn::class,
                'template' => '{preview} {audit} {undo}',
                'urlCreator' => function ($action, OfflineFeeRule $model) {
                    return Url::to([
                        $action,
                        'assetLoanChannel' => $model->asset_loan_channel,
                        'subFeeType' => $model->sub_fee_type,
                        'startDate' => $model->start_date,
                    ]);
                },
                'buttons' => [
                    'preview' => function ($url) {
                        return ActionColumn::newButton('查看', $url, [
                            'class' => 'btn-info',
                            'target' => '_blank',
                        ]);
                    },
                    'audit' => function ($url) {
                        return ActionColumn::newButton('审核', $url, [
                            'class' => 'btn-facebook',
                            'target' => '_blank',
                            'data' => [
                                'method' => 'post',
                            ],
                        ]);
                    },
                    'undo' => function ($url) {
                        return ActionColumn::newButton('撤销', $url, [
                            'class' => 'btn-danger',
                            'data' => [
                                'confirm' => '您确定要撤销该审核吗',
                                'method' => 'post',
                            ],
                        ]);
                    },
                ],
                'visibleButtons' => [
                    'audit' => function (OfflineFeeRule $model) {
                        return $model->audit->approvalEntries->auditable();
                    },
                    'undo' => function (OfflineFeeRule $model) {
                        return $model->audit->audit_creator_id === Yii::$app->getUser()->getId();
                    },
                ],
            ],
            'asset_loan_channel',
            'fee_type',
            'sub_fee_type',
            'start_date',
            'end_date',
        ],
    ]);
}

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{view} {update} {copy} {change}',
            'buttons' => [
                'view' => function ($url) {
                    return ActionColumn::newButton('查看', $url, [
                        'class' => 'btn-info',
                        'target' => '_blank',
                    ]);
                },
                'copy' => function ($url) {
                    return ActionColumn::newButton('复制', $url, [
                        'class' => 'btn-twitter',
                    ]);
                },
                'change' => function ($url, OfflineFeeRule $model) {
                    $statusName = $model->status === OfflineFeeRule::STATUS_ACTIVE ? '置为无效' : '置为有效';

                    return ActionColumn::newButton($statusName, [
                        'change',
                        'id' => $model->id,
                        'changeStatus' => $model->status === OfflineFeeRule::STATUS_ACTIVE
                            ? OfflineFeeRule::STATUS_NEGATIVE
                            : OfflineFeeRule::STATUS_ACTIVE,
                    ], [
                        'class' => $model->status === OfflineFeeRule::STATUS_ACTIVE ? 'btn-default' : 'btn-danger',
                        'data' => [
                            'confirm' => "确定{$statusName}吗?",
                            'method' => 'post',
                        ],
                    ]);
                },
            ],
        ],
        [
            'attribute' => 'asset_loan_channel',
        ],
        [
            'attribute' => 'fee_type',
        ],
        [
            'attribute' => 'fee_type_desc',
            'format' => ['truncate', 10],
        ],
        [
            'attribute' => 'sub_fee_type',
        ],
        [
            'attribute' => 'income_expense_type',
            'format' => ['in', OfflineFeeRule::incomeExpenseTypeList()],
        ],
        [
            'attribute' => 'rate',
        ],
        [
            'attribute' => 'calculation_type',
        ],
        [
            'attribute' => 'payment_mode',
        ],
        [
            'attribute' => 'offlineFeeDatasource.data_source_name',
        ],
        [
            'label' => '数据源类型',
            'value' => function ($model) {
                if ($model->data_source_id==null){
                    return $model->data_source;
                }
                return $model->offlineFeeDatasource->data_source;
            },
        ],
        [
            'label' => '数据源sql',
            'value' => function ($model) {
                $data_source_sql = null;
                if ($model->data_source_id==null){
                    $data_source_sql =  $model->data_source_sql;
                }else{
                    $data_source_sql= $model->offlineFeeDatasource->data_source_sql;
                }
                return StringHelper::truncate($data_source_sql, 10);
            },
        ],
        [
            'attribute' => 'start_date',
        ],
        [
            'attribute' => 'end_date',
        ],
        [
            'attribute' => 'status',
        ],
        [
            'attribute' => 'need_up',
            'format' => ['in', OfflineFeeRule::needUpList()],
        ],
    ],
]);
