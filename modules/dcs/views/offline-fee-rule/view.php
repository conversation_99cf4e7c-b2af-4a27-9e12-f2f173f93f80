<?php

use xlerr\CodeEditor\CodeEditor;
use yii\helpers\Html;
use yii\widgets\DetailView;
use dcs\models\OfflineFeeRule;

/* @var $this yii\web\View */
/* @var $model OfflineFeeRule */

$this->title                   = '规则编号:' . $model->id;
$this->params['breadcrumbs'][] = ['label' => '线下费用计算规则', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <a class="btn btn-default" href="<?= Yii::$app->getRequest()->getReferrer() ?>">返回列表</a>
</p>
<div class="box">
    <div class="box-header with-border">
        <h3 class="box-title">详情</h3>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model'      => $model,
            'attributes' => [
                'id',
                'asset_loan_channel',
                'fee_type',
                'fee_type_desc',
                'sub_fee_type',
                [
                    'attribute' => 'income_expense_type',
                    'format' => ['in', OfflineFeeRule::incomeExpenseTypeList()],
                ],
                'rate',
                'calculation_type',
                'calculation_desc',
                'payment_mode',
                'data_source',
                [
                    'attribute'      => 'data_source_sql',
                    'captionOptions' => [
                        'style' => 'width: 10%',
                    ],
                    'format'         => 'raw',
                    'value'          => CodeEditor::widget([
                        'name'          => 'value_show',
                        'value'         => $model->data_source_sql,
                        'clientOptions' => [
                            'readOnly' => true,
                            'mode'     => CodeEditor::MODE_SQL,
                            'maxLines' => 40,
                        ],
                    ]),
                ],
                'start_date',
                'end_date',
                'status',
                'comment',
                [
                    'attribute' => 'need_up',
                    'format' => ['in', OfflineFeeRule::needUpList()],
                ],
                'create_user',
                'create_at',
                'update_at',
            ],
        ]) ?>
    </div>
</div>
