<?php

use dcs\models\DepbankOrder;
use dcs\models\DepbankOrderSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;

/** @var yii\web\View $this */
/** @var DepbankOrderSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = '存管出入金聚合订单';
$this->params['breadcrumbs'][] = $this->title;
echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{record}',
            'buttons' => [
                'record' => static function ($url, DepbankOrder $model) {
                    return DialogActionColumn::newButton('查询明细', [
                        'record',
                        'orderNo' => $model->order_no,
                    ], [
                        'class' => 'btn-info layer-dialog',
                    ]);
                },
            ],
        ],
        'order_no',
        [
            'attribute' => 'order_type',
            'format' => ['in', DepbankOrder::TYPE_LIST],
        ],
        [
            'attribute' => 'amount',
            'class' => MoneyDataColumn::class
        ],
        [
            'attribute' => 'status',
            'format' => ['in', DepbankOrder::STATUS_LIST],
        ],
        'finish_at',
        'out_acct_bank',
        'out_acct_no',
        'out_acct_name',
        'out_acct_cer_no',
        'in_acct_bank',
        'in_acct_no',
        'in_acct_name',
        'in_acct_cer_no',
        'memo',
//        'comment',
        'label',
        'from_system',
        'create_at',
        'update_at',
    ],
]);


