<?php

use cpm\models\CapitalChannel;
use dcs\models\CapitalSettlementRuleConfig;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use Xlerr\SettlementFlow\Models\Rule;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\web\View;

/**
 * @var View $this
 * @var CapitalSettlementRuleConfig $model
 */

$form = ActiveForm::begin();

$bizRegions = array_values(Rule::bizRegions());

$showData = [];
if ($model->company_id && $model->company_name) {
    $showData = [
        $model->company_id => $model->company_name
    ];
}

if ((int)$model->funding_nature !== CapitalSettlementRuleConfig::NATURE_BAOLI || empty($model->company_id)) {
    $model->company_id = null;
    $model->company_name = null;
}

$fundingNatureId = Html::getInputId($model, 'funding_nature');
$isAllocationId = Html::getInputId($model, 'is_allocation');
$companyIdId = Html::getInputId($model, 'company_id');
$companyNameId = Html::getInputId($model, 'company_name');
$natureBaoli = CapitalSettlementRuleConfig::NATURE_BAOLI;
$companyInfo = <<<JS
$('#{$fundingNatureId}').change('change', function(e) {
    var is_allocation = $('#{$isAllocationId}');
    var company_id = $('#{$companyIdId}');
    var company_name = $('#{$companyNameId}');
    if (this.value == {$natureBaoli}) {
        $('#company_info').show();
    } else {
        $('#company_info').hide();
        is_allocation.val('0');
        company_id.val('0');
        company_name.val('');
    }
});
JS;
$this->registerJs($companyInfo);
?>

<div class="box-body">
    <div class="row">
        <div class="col-md-4">
            <?= $form->field($model, 'asset_loan_channel')->widget(Select2::class, [
                'data' => CapitalChannel::list(),
                'options' => [
                    'prompt' => $model->getAttributeLabel('asset_loan_channel'),
                ],
            ]) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'short_code') ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'admin_channel')->widget(Select2::class, [
                'data' => CapitalChannel::list(),
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => vsprintf('%s(默认为当前%s)', [
                        $model->getAttributeLabel('admin_channel'),
                        $model->getAttributeLabel('asset_loan_channel'),
                    ]),
                ],
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-4">
            <?= $form->field($model, 'channel_status')->widget(Select2::class, [
                'data' => CapitalSettlementRuleConfig::CHANNEL_STATUS_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('channel_status'),
                ],
            ]) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'settlement_mode')->widget(Select2::class, [
                'data' => CapitalSettlementRuleConfig::getSettlementMode(),
                'hideSearch' => true,
                'options' => [
                    'prompt' => '请选择结算规则',
                ],
            ]) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'remit_rule')->widget(Select2::class, [
                'data' => CapitalSettlementRuleConfig::REMIT_RULE_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('remit_rule'),
                ],
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <?= $form->field($model, 'grant_start_at')->widget(DatePicker::class) ?>
        </div>
        <div class="col-md-3">
            <?= $form->field($model, 'min_due_at')->widget(DatePicker::class) ?>
        </div>
        <div class="col-md-3">
            <?= $form->field($model, 'max_due_at')->widget(DatePicker::class) ?>
        </div>
        <div class="col-md-3">
            <?= $form->field($model, 'min_compensate_at')->widget(DatePicker::class) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-4">
            <?= $form->field($model, 'compensate_settlement_rule') ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'compensate_remit_rule') ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'compensate_settlement_time') ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-2">
            <?= $form->field($model, 'buyback_rule_day') ?>
        </div>
        <div class="col-md-2">
            <?= $form->field($model, 'buyback_rule_period') ?>
        </div>
        <div class="col-md-2">
            <?= $form->field($model, 'buyback_rule_period_total') ?>
        </div>
        <div class="col-md-2">
            <?= $form->field($model, 'buyback_settlement_rule') ?>
        </div>
        <div class="col-md-2">
            <?= $form->field($model, 'buyback_remit_rule') ?>
        </div>
        <div class="col-md-2">
            <?= $form->field($model, 'buyback_rule_period_adv') ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <?= $form->field($model, 'switch_us_day') ?>
        </div>
        <div class="col-md-3">
            <?= $form->field($model, 'switch_us_time') ?>
        </div>
        <div class="col-md-3">
            <?= $form->field($model, 'offline_repay_method')->widget(Select2::class, [
                'data' => CapitalSettlementRuleConfig::OFFLINE_REPAY_METHOD_LIST,
                'options' => [
                    'prompt' => $model->getAttributeLabel('offline_repay_method'),
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                ],
            ]) ?>
        </div>
        <div class="col-md-3">
            <?= $form->field($model, 'offline_repay_remit_rule') ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-4">
            <?= $form->field($model, 'funding_nature')->widget(Select2::class, [
                'data' => CapitalSettlementRuleConfig::NATURE_LIST,
                'options' => [
                    'prompt' => '请选择机构性质',
                ],
            ]) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'weidu_income_refund')->widget(Select2::class, [
                'data' => CapitalSettlementRuleConfig::WD_INCOME_REFUND_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => '请选择唯渡收入回款方式',
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                ],
            ]) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'fees')->widget(Select2::class, [
                'data' => CapitalSettlementRuleConfig::getBdFeesList(),
                'options' => [
                    'prompt' => '请选择BD费用类型',
                ],
                'pluginOptions' => [
                    'multiple' => true,
                ],
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-8">
            <div class="row" id="company_info"
                 style="display: <?= (int)$model->funding_nature !== CapitalSettlementRuleConfig::NATURE_BAOLI ? 'none' : 'block' ?>">
                <div class="col-md-6">
                    <?= $form->field($model, 'is_allocation')->widget(Select2::class, [
                        'data' => CapitalSettlementRuleConfig::ALLOCATION_MAP,
                        'hideSearch' => true,
                        'options' => [
                            'prompt' => '请选择是否分摊',
                        ],
                    ]) ?>
                </div>
                <div class="col-md-6">
                    <div style="display: none">
                        <?= $form->field($model, 'company_name')->hiddenInput()->label(false) ?>
                    </div>
                    <?= $form->field($model, 'company_id', [
                        'labelOptions' => ['label' => '公司名称']
                    ])->widget(Select2::class, [
                        'value' => $model->company_name,
                        'data' => $showData, // 初始选项数组 ['id1' => 'value1', ...]
                        //'initValueText' => empty($model->category_id) ? '未来设置' : $model->company_name,
                        'options' => [
                            'placeholder' => '请输入关键字进行搜索...',
                        ],
                        'pluginOptions' => [
                            'tags' => false, // 允许输入新值
                            'allowClear' => true,
                            'minimumInputLength' => 2, // 输入多少字符后开始搜索
                            'ajax' => [
                                'url' => '/cpop-settlement/capital/fund-company',
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {keyword:params.term}; }'),
                                'processResults' => new JsExpression(
                                    'function(data, params) {
                            let result = [];
                            if (data.code != 0) {
                                layer.msg(data.message + " 查询失败，请稍后再试！", {icon: 2});
                                return {results: result};
                            }
                            data.data.forEach(function(item, index) {
                                let itemObj = {
                                    id: item.id,
                                    text: item.name
                                }
                                result.push(itemObj);
                            });
                            return {results: result};
                        }'
                                )
                            ],
                        ],
                        'pluginEvents' => [
                            // 当值改变时触发
                            'change' => "function(e) {
                         var selectedText = $(this).find('option:selected').text();
                        $('#{$companyNameId}').val(selectedText);
                    }"
                        ]
                    ]) ?>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'biz_region')->widget(Select2::class, [
                'data' => array_combine($bizRegions, $bizRegions),
                'hideSearch' => true,
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'grant_date_onloan_report')->widget(Select2::class, [
                'data' => ['不开启', '开启'],
                'hideSearch' => true,
                'options' => [
                    'prompt' => '是否开启按放款日在贷报表',
                ],
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'capital_withhold_report')->widget(Select2::class, [
                'data' => ['不开启', '开启'],
                'hideSearch' => true,
                'options' => [
                    'prompt' => '是否开启资方扣款报表',
                ]
            ]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <?= $form->field($model, 'enable_ab_prin_via_abp_report')->widget(Select2::class, [
                'data' => ['不开启', '开启'],
                'hideSearch' => true,
                'options' => [
                    'prompt' => '是否开启按权责期次每日权责本金报表',
                ],
            ]) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'granted_with_rateno_report')->widget(Select2::class, [
                'data' => ['不开启', '开启'],
                'hideSearch' => true,
                'options' => [
                    'prompt' => '是否开启每日放款(按费率编号分类)报表',
                ]
            ]) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'ab_period_amount_with_gd_report')->widget(Select2::class, [
                'data' => ['不开启', '开启'],
                'hideSearch' => true,
                'options' => [
                    'prompt' => '是否开启每日权责期次本金利息(按放款日分类)报表',
                ]
            ]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <?= $form->field($model, 'desc')->widget(CodeEditor::class, [
                'clientOptions' => [
                    'mode' => CodeEditor::MODE_Text,
                    'minLines' => 3,
                    'maxLines' => 20,
                ],
            ]) ?>
        </div>
    </div>
</div>
<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
