<?php

use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\grid\SerialColumn;
use yii\helpers\Html;
use yii\web\View;
use Carbon\Carbon;


/* @var $this View */
/* @var $dataProvider ActiveDataProvider */

$this->title                   = '创新收费收入配置';
$this->params['breadcrumbs'][] = $this->title;
?>

<p>
    <?= Html::a('分配当月方案维护', ['create'], [
        'class' => 'btn btn-success',
    ]) ?>
    <?= Html::a('分配次月方案维护', ['create-next-month'], [
        'class' => 'btn btn-success',
    ]) ?>
</p>
<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'class'  => SerialColumn::class,
            'header' => '序号',
        ],
        [
            'label'     => '生效日期',
            'attribute' => 'asset_allocation_tech_date',
            'format'    => function ($v) {
                $carbon = new Carbon($v);
                if ($carbon->isNextMonth()) {
                    return Html::a($v, ['create-next-month', 'date' => $v]);
                } else {
                    return Html::a($v, ['create', 'date' => $v]);
                }
            },
        ],
    ],
]) ?>
