<?php

use common\models\Asset;
use xlerr\CodeEditor\CodeEditor;
use yii\widgets\DetailView;
use dcs\models\CapitalAutoSettlementRule;

/* @var $this yii\web\View */
/* @var $model CapitalAutoSettlementRule */

$this->title                   = '规则编号:' . $model->id;
$this->params['breadcrumbs'][] = ['label' => '资方结算规则', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <a class="btn btn-default" href="<?= Yii::$app->getRequest()->getReferrer() ?>">返回列表</a>
</p>
<div class="box">
    <div class="box-header with-border">
        <h3 class="box-title">详情</h3>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model'      => $model,
            'attributes' => [
                'id',
                [
                    'attribute' => 'asset_loan_channel',
                    'value'     => static fn(CapitalAutoSettlementRule $model
                    ) => Asset::channelList()[$model->asset_loan_channel],
                ],
                [
                    'attribute' => 'support_operation_type',
                    'format'    => 'raw',
                    'value'     => static fn(CapitalAutoSettlementRule $model) => implode(',',
                        array_map(static fn($index) => CapitalAutoSettlementRule::OPERATION_TYPE[$index],
                            explode(';', $model->support_operation_type))),
                ],
                [
                    'attribute' => 'auto_day_type',
                    'format'    => 'raw',
                    'value'     => static fn(CapitalAutoSettlementRule $model
                    ) => $model::DAY_TEXT[$model->auto_day_type],
                ],
                'auto_day_value',
                'start_date',
                'end_date',
                [
                    'attribute'      => 'notify_email',
                    'captionOptions' => [
                        'style' => 'width: 10%',
                    ],
                    'format'         => 'raw',
                    'value'          => CodeEditor::widget([
                        'name'          => 'value_show',
                        'value'         => $model->notify_email,
                        'clientOptions' => [
                            'readOnly' => true,
                            'mode'     => CodeEditor::MODE_Text,
                            'maxLines' => 5,
                        ],
                    ]),
                ],
                [
                    'attribute'      => 'settlement_comment',
                    'captionOptions' => [
                        'style' => 'width: 10%',
                    ],
                    'format'         => 'raw',
                    'value'          => CodeEditor::widget([
                        'name'          => 'value_show',
                        'value'         => $model->settlement_comment,
                        'clientOptions' => [
                            'readOnly' => true,
                            'mode'     => CodeEditor::MODE_Text,
                            'maxLines' => 5,
                        ],
                    ]),
                ],
                [
                    'attribute' => 'status',
                    'value'     => static fn(CapitalAutoSettlementRule $model
                    ) => CapitalAutoSettlementRule::STATUS_TEXT[$model->status],
                ],
                'comment',
                'create_user',
                'create_at',
                'update_at',
            ],
        ]) ?>
    </div>
</div>
