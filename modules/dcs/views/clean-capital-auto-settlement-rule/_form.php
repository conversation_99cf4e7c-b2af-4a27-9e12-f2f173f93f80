<?php

use cpm\models\CapitalChannel;
use dcs\models\CapitalAutoSettlementRule;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;
use xlerr\common\widgets\DatePicker;

/* @var $this View */
/* @var $model CapitalAutoSettlementRule */
/* @var $actionName */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">


    <?= $form->field($model, 'asset_loan_channel')->widget(Select2::class, [
        'data'       => CapitalChannel::list(true),
        'options'    => ['placeholder' => '请选择...'],
    ]) ?>
    <?= $form->field($model, 'support_operation_type')->widget(Select2::class, [
        'data'       => CapitalAutoSettlementRule::OPERATION_TYPE,
        'hideSearch' => true,
        'options'    => ['multiple' => true, 'placeholder' => '请选择...'],
    ]) ?>
    <?= $form->field($model, 'auto_day_type')->widget(Select2::class, [
        'data'       => CapitalAutoSettlementRule::DAY_TEXT,
        'hideSearch' => true,
        'options'    => ['placeholder' => '请选择...'],
    ]) ?>
    <?= $form->field($model, 'auto_day_value')->textInput([
        'maxlength' => true,
    ]) ?>
    <?= $form->field($model, 'start_date')->widget(DatePicker::class, [
        'options' => [
            'placeholder'  => '规则开始生效日期',
            'autocomplete' => 'off',
        ],
    ]) ?>
    <?= $form->field($model, 'end_date')->widget(DatePicker::class, [
        'options' => [
            'placeholder'  => '规则开始结束日期',
            'value'        => '2099-12-31',
            'autocomplete' => 'off',
        ],
    ]) ?>
    <?= $form->field($model, 'notify_email')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode'     => CodeEditor::MODE_Text,
            'minLines' => 5,
            'maxLines' => 10,
        ],
    ]) ?>
    <?= $form->field($model, 'status')->widget(Select2::class, [
        'data'       => CapitalAutoSettlementRule::STATUS_TEXT,
        'hideSearch' => true,
    ]) ?>
    <?= $form->field($model, 'settlement_comment')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode'     => CodeEditor::MODE_Text,
            'minLines' => 5,
            'maxLines' => 10,
        ],
    ]) ?>
    <?= $form->field($model, 'comment')->textInput([
        'maxlength' => true,
    ]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
        'data'  => [
            'confirm' => '确定保存吗?',
            'method'  => 'post',
        ],
    ]) ?>
</div>

<?php ActiveForm::end(); ?>

