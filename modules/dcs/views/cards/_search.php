<?php

use dcs\models\CardSearch;
use xlerr\common\widgets\ActiveForm;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model CardSearch */
/* @var $form ActiveForm */

$form = ActiveForm::begin([
    'action'        => ['index'],
    'method'        => 'get',
    'type'          => ActiveForm::TYPE_INLINE,
    'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
]);

echo $form->field($model, 'card_user_name_encrypt');

echo $form->field($model, 'card_num_encrypt');

echo Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']);

echo Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']);

ActiveForm::end();
