<?php

use Carbon\Carbon;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\helpers\MoneyHelper;
use yii\helpers\Url;

/** @var string $fq_title */
/** @var string $relate_num */
/** @var array $fq_cost_info */
/** @var bool $splitTable */
/** @var string $assetId */
$splitTable = $splitTable ?? false;
?>
<style>
    .split-table-style th[colspan],
    .split-table-style th:nth-of-type(1),
    .split-table-style th:nth-of-type(4),
    .split-table-style th:nth-of-type(8),
    .split-table-style th:nth-of-type(12),
    .split-table-style th:nth-of-type(16),
    .split-table-style td[colspan],
    .split-table-style th:nth-of-type(1),
    .split-table-style td:nth-of-type(1),
    .split-table-style td:nth-of-type(4),
    .split-table-style td:nth-of-type(8),
    .split-table-style td:nth-of-type(12),
    .split-table-style td:nth-of-type(16) {
        border-right: 2px solid #f4f4f4;
    }
</style>
<div class="box box-default">
    <div class="box-header with-border">
        <div class="box-title"><?= $fq_title . ' ' . $relate_num ?></div>
    </div>

    <div class="box-body no-padding">
        <table class="table table-striped table-hover <?= $splitTable ? 'split-table-style' : '' ?>">
            <thead>
            <tr>
                <th colspan="1"></th>
                <th colspan="3"></th>
                <th colspan="4">本金</th>
                <th colspan="4">利息</th>
                <th colspan="4">费用</th>
                <th colspan="4">罚息</th>
            </tr>
            <tr>
                <th>操作</th>
                <th>期次</th>
                <th>到期日</th>
                <th>总待还</th>
                <th>应还</th>
                <th>已还</th>
                <th>减免</th>
                <th>待还</th>
                <th>应还</th>
                <th>已还</th>
                <th>减免</th>
                <th>待还</th>
                <th>应还</th>
                <th>已还</th>
                <th>减免</th>
                <th>待还</th>
                <th>应还</th>
                <th>已还</th>
                <th>减免</th>
                <th>待还</th>
            </thead>
            <tbody>
            <?php
            $pendingRepayment = 0;
            $repayPrincipalNeedAll = 0;
            $repayInterestNeedAll = 0;
            $serviceNeedAll = 0;
            $lateInterestNeedAll = 0;
            if (!empty($fq_cost_info)) {
                foreach ($fq_cost_info as $fee) {
                    if (is_array($fee)) {
                        $repayPrincipalShould = intval($fee['repayprincipal_should'] ?? 0);
                        $repayPrincipalPaid = intval($fee['repayprincipal_paid'] ?? 0);
                        $repayPrincipalDec = intval($fee['repayprincipal_dec'] ?? 0);

                        $repayInterestShould = intval($fee['repayinterest_should'] ?? 0);
                        $repayInterestPaid = intval($fee['repayinterest_paid'] ?? 0);
                        $repayInterestDec = intval($fee['repayinterest_dec'] ?? 0);

                        $serviceShould = intval($fee['service_should'] ?? 0);
                        $servicePaid = intval($fee['service_paid'] ?? 0);
                        $serviceDec = intval($fee['service_dec'] ?? 0);

                        $lateInterestShould = intval($fee['lateinterest_should'] ?? 0);
                        $lateInterestPaid = intval($fee['lateinterest_paid'] ?? 0);
                        $lateInterestDec = intval($fee['lateinterest_dec'] ?? 0);

                        //本金待还
                        $repayPrincipalNeed = $repayPrincipalShould - $repayPrincipalPaid;
                        $repayPrincipalNeed = ($repayPrincipalNeed > 0) ? $repayPrincipalNeed : 0.00;
                        $repayPrincipalNeedAll += $repayPrincipalNeed;

                        //利息待还
                        $repayInterestNeed = $repayInterestShould - $repayInterestPaid;
                        $repayInterestNeed = ($repayInterestNeed > 0) ? $repayInterestNeed : 0.00;
                        $repayInterestNeedAll += $repayInterestNeed;

                        //费用待还
                        $serviceNeed = $serviceShould - $servicePaid;
                        $serviceNeed = ($serviceNeed > 0) ? $serviceNeed : 0.00;
                        $serviceNeedAll += $serviceNeed;

                        //罚息
                        $lateInterestNeed = $lateInterestShould - $lateInterestPaid;
                        $lateInterestNeed = ($lateInterestNeed > 0) ? $lateInterestNeed : 0.00;
                        $lateInterestNeedAll += $lateInterestNeed;

                        $curPendingRepayment =
                            $repayPrincipalNeed + $repayInterestNeed + $serviceNeed + $lateInterestNeed;
                        $pendingRepayment += $curPendingRepayment;
                        echo vsprintf(
                            '<tr><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td></td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td></tr>',
                            [
                                Carbon::now()->lt($fee['dtransaction_expect_finish_time']) && ($repayPrincipalShould == $repayPrincipalPaid) ?
                                    DialogActionColumn::newButton('还款取消', Url::to(['repayment-cancel', 'assetId' => $assetId, 'period' => $fee['dtransaction_period']]), [
                                        'class' => 'btn-primary layer-dialog',
                                    ]) : '',
                                $fee['dtransaction_period'],
                                Carbon::parse($fee['dtransaction_expect_finish_time'])->toDateString(),
                                Carbon::parse($fee['dtransaction_expect_finish_time'])->toDateString() < Carbon::now()
                                    ->toDateString()
                                && $curPendingRepayment > 0 ? vsprintf('<span style="color: red">%s</span>', [
                                    MoneyHelper::f2y($curPendingRepayment, true),
                                ]) : MoneyHelper::f2y($curPendingRepayment, true),
                                MoneyHelper::f2y($repayPrincipalShould, true),
                                MoneyHelper::f2y($repayPrincipalPaid, true),
                                MoneyHelper::f2y($repayPrincipalDec, true),
                                MoneyHelper::f2y($repayPrincipalNeed, true),

                                MoneyHelper::f2y($repayInterestShould, true),
                                MoneyHelper::f2y($repayInterestPaid, true),
                                MoneyHelper::f2y($repayInterestDec, true),
                                MoneyHelper::f2y($repayInterestNeed, true),

                                MoneyHelper::f2y($serviceShould, true),
                                MoneyHelper::f2y($servicePaid, true),
                                MoneyHelper::f2y($serviceDec, true),
                                MoneyHelper::f2y($serviceNeed, true),

                                MoneyHelper::f2y($lateInterestShould, true),
                                MoneyHelper::f2y($lateInterestPaid, true),
                                MoneyHelper::f2y($lateInterestDec, true),
                                MoneyHelper::f2y($lateInterestNeed, true),
                            ]
                        );
                    }
                }
            }
            ?>
            <tr>
                <td colspan="1"></td>
                <td>总计</td>
                <td></td>
                <td><?= MoneyHelper::f2y($pendingRepayment, true) ?></td>
                <td colspan="3"><?= MoneyHelper::f2y($fq_cost_info['repayprincipal_should_all'] ?? 0, true) ?></td>
                <td><?= MoneyHelper::f2y($repayPrincipalNeedAll, true) ?></td>
                <td colspan="3"><?= MoneyHelper::f2y($fq_cost_info['repayinterest_should_all'] ?? 0, true) ?></td>
                <td><?= MoneyHelper::f2y($repayInterestNeedAll, true) ?></td>
                <td colspan="3"><?= MoneyHelper::f2y($fq_cost_info['service_should_all'] ?? 0, true) ?></td>
                <td><?= MoneyHelper::f2y($serviceNeedAll, true) ?></td>
                <td colspan="3"><?= MoneyHelper::f2y($fq_cost_info['lateinterest_should_all'] ?? 0, true) ?></td>
                <td><?= MoneyHelper::f2y($lateInterestNeedAll, true) ?></td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

