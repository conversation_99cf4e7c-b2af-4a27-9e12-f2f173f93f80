<?php

use common\business\AssetHelper;
use common\consts\Consts;
use common\models\Asset;
use common\models\AssetAttachment;
use common\models\Dtransaction;
use common\models\Provision;
use common\models\Withhold;
use contract\models\Contract;
use xlerr\common\helpers\MoneyHelper;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\JsExpression;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model Asset */

$this->title = $model->asset_item_no;
$this->params['breadcrumbs'][] = ['label' => '资产管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

$withholdSerialNos = Withhold::find()
    ->alias('w')
    ->innerJoinWith([
        'withholdOrder' => static function (ActiveQuery $query) use ($model) {
            $query->onCondition(['withhold_order_reference_no' => $model->asset_item_no]);
        },
    ])
    ->select('w.withhold_serial_no')
    ->column();
?>

<p>
    <?= Html::a('查看代扣记录', [
        'withhold-result/index',
        'item_no' => $model->asset_item_no,
        'withhold_status' => Withhold::STATUS_SUCCESS,
    ], [
        'class' => 'btn btn-info',
        'target' => '_blank',
    ]) ?>

    <?php
    if ($withholdSerialNos) {
        echo Html::a('查看绑卡记录', [
            '/repay/card-bind/index',
            'card_bind_serial_no' => $withholdSerialNos,
            //            'card_bind_status' => 'success',
        ], [
            'class' => 'btn btn-info',
            'target' => '_blank',
        ]);
    } else {
        echo Html::button('查看绑卡记录', [
            'class' => 'btn btn-info',
            'onClick' => new JsExpression('javascript:window.alert("未查询到代扣序列号");'),
        ]);
    }
    ?>

    <?php
    if (
        $model->asset_type === Consts::ASSET_TYPE_PAY_DAY_LOAN && $model->asset_status === Asset::STATUS_REPAY
        && Yii::$app->user->can('ManualWithhold')
    ) {
        echo Html::a('代扣', [
            'withhold-result/create',
            'id' => $model->asset_id,
        ], [
            'class' => 'btn btn-success',
        ]);
    }
    ?>

    <?php
    if (
        $model->asset_loan_channel != Consts::ASSET_LOAN_CHANNEL_NOLOAN
        && in_array($model->asset_status, [Asset::STATUS_SIGN, Asset::STATUS_SALE])
        && Yii::$app->getUser()->can(Url::to(['cancel']))
    ) {
        echo Html::a('终止放款', ['cancel', 'id' => $model->asset_id], [
            'class' => 'btn btn-twitter',
            'data' => [
                'method' => 'post',
                'confirm' => '确定要终止放款吗?',
            ],
        ]);
    }
    ?>

    <?php
    if (
        $model->asset_loan_channel != Consts::ASSET_LOAN_CHANNEL_NOLOAN
        && $model->asset_status == Asset::STATUS_REPAY
        && Yii::$app->getUser()->can('AssetVoid')
    ) {
        echo Html::a('取消借款(12h)', ['/repay/operate/asset-void-withhold', 'refNo' => $model->asset_item_no], [
            'class' => 'btn btn-twitter',
            'target' => '_blank',
        ]);
    }
    ?>
</p>

<div class="row">
    <div class="col-sm-6">
        <div class="box">
            <div class="box-header with-border">
                <h3 class="box-title">资产基本信息</h3>
            </div>

            <div class="box-body no-padding">
                <?= DetailView::widget([
                    'model' => $model,
                    'attributes' => [
                        'asset_item_no',
                        [
                            'attribute' => 'asset_period_type',
                            'format' => ['in', Asset::periodTypeList()],
                        ],
                        'asset_period_count',
                        'asset_grant_at',
                        'asset_actual_grant_at',
                        'asset_due_at',
                        'asset_first_payat',
                        'asset_payoff_at',
                        [
                            'attribute' => 'asset_repayment_type',
                            'format' => ['in', Consts::$repaymentTypeDisplay],
                        ],
                        //                    'asset_fee_rate',
                        //                    'asset_interest_rate',
                        [
                            'attribute' => 'asset_type',
                            'format' => ['in', Asset::assetTypeList()],
                        ],
                        [
                            'attribute' => 'asset_sub_type',
                            'format' => ['in', Asset::subTypeList()],
                        ],
                        [
                            'attribute' => 'asset_loan_channel',
                            'format' => ['in', Asset::channelList(), '未知'],
                        ],
                        [
                            'attribute' => 'asset_from_system',
                            'format' => ['in', Consts::$assetFromSystemList],
                        ],
                    ],
                ]) ?>
            </div>
        </div>
    </div>
    <div class="col-sm-6">
        <div class="box">
            <div class="box-header with-border">
                <div class="box-title">资产资金信息</div>
            </div>

            <div class="box-body no-padding">
                <?= DetailView::widget([
                    'model' => $model,
                    'attributes' => [
                        [
                            "attribute" => 'asset_principal_amount_f',
                            'value' => MoneyHelper::f2y($model->asset_principal_amount_f, true),
                        ],
                        [
                            "attribute" => 'asset_granted_principal_amount_f',
                            'value' => MoneyHelper::f2y($model->asset_granted_principal_amount_f, true),
                        ],
                        [
                            "attribute" => 'asset_repaid_principal_amount_f',
                            'value' => MoneyHelper::f2y($model->asset_repaid_principal_amount_f, true),
                        ],
                        [
                            "attribute" => 'asset_interest_amount_f',
                            'value' => MoneyHelper::f2y($model->asset_interest_amount_f, true),
                        ],
                        [
                            "attribute" => 'asset_repaid_interest_amount_f',
                            'value' => function ($model) {
                                $repaidAmt = $model->asset_repaid_interest_amount_f;
                                $provisonDatas = Provision::findAll([
                                    "provision_item_no" => $model->asset_item_no,
                                    "provision_tran_type" => Dtransaction::DTRANSACTION_TYPE_REPAYINTEREST,
                                ]);
                                if (!empty($provisonDatas)) {
                                    foreach ($provisonDatas as $provisonData) {
                                        $repaidAmt -= $provisonData->provision_amount;
                                    }
                                }
                                $repaidAmt = $repaidAmt < 0 ? 0 : $repaidAmt;

                                return MoneyHelper::f2y($repaidAmt, true);
                            },
                        ],
                        [
                            "attribute" => 'asset_status',
                            'value' => $model->assetStatusText,
                        ],
                    ],
                ]) ?>
            </div>
        </div>

        <div class="box">
            <div class="box-header with-border">
                <div class="box-title">渠道信息</div>
            </div>
            <div class="box-body no-padding">
                <table class="table table-striped table-bordered detail-view">
                    <tbody>
                    <tr>
                        <th>渠道名称</th>
                        <td>
                            <?php
                            if (isset($model->channel)) {
                                echo $model->channel->channel_name == 'Paydayloan' ? 'PDL'
                                    : $model->channel->channel_name;
                            } else {
                                echo '未设置';
                            }
                            ?>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="box">
            <div class="box-header with-border">
                <div class="box-title">区域信息</div>
            </div>
            <div class="box-body no-padding">
                <table class="table table-striped table-bordered detail-view">
                    <tbody>
                    <tr>
                        <th>省份</th>
                        <td>
                            <?php
                            if (isset($model->borrower->asset_individual_province_name)) {
                                echo $model->borrower->asset_individual_province_name;
                            } else {
                                echo "未设置";
                            }

                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th>城市</th>
                        <td>
                            <?php
                            if (isset($model->borrower->asset_individual_city_name)) {
                                echo $model->borrower->asset_individual_city_name;
                            } else {
                                echo "未设置";
                            }

                            ?>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <?php
        echo $this->render('_signFqCostAssetInfo', [
            'fq_cost_info' => AssetHelper::getAssetFqInfoData($model->asset_id),
            'fq_title' => '费用信息',
            'relate_num' => '',
            'assetId' => $model->asset_id,
            'splitTable' => true,
            'showAction' => true
        ]);

        foreach ($model->noloanAsset as $noloanAsset) {
            echo $this->render('_signFqCostAssetInfoRelation', [
                'fq_cost_info' => AssetHelper::getAssetFqInfoData($noloanAsset->asset_id),
                'fq_title' => '关联资产费用信息: ' . $noloanAsset->assetExtend->asset_extend_ref_order_type,
                'relate_num' => Html::a($noloanAsset->asset_item_no, [
                    'view-item',
                    'item_no' => $noloanAsset->asset_item_no,
                ], [
                    'title' => '查看',
                    'target' => '_blank',
                ]),
                'assetId' => $model->asset_id,
                'splitTable' => true,
            ]);
        }

        echo $this->render('_signAssetStatusInfo', [
            'model' => $model,
            'title' => '放款记录',
        ]);
        ?>
    </div>

    <div class="col-md-6">
        <?php
        if (!YII_ENV_PROD || Yii::$app->user->can('IndividualInfoManage')) {
            echo GridView::widget([
                'layout' => '<div class="box-header with-border"><div class="box-title">资产附件</div></div><div class="box-body table-responsive no-padding">{items}</div>',
                'dataProvider' => new ActiveDataProvider([
                    'query' => $model->getAssetAttachment()
                        ->andWhere([
                            'and',
                            ['!=', 'asset_attachment_type', AssetAttachment::ATTACHMENT_TYPE_OFFLINE_RECHARGE],
                            ['!=', 'asset_attachment_status', AssetAttachment::ATTACHMENT_STATUS_VOID],
                        ]),
                    'pagination' => false,
                    'sort' => false,
                ]),
                'columns' => [
                    [
                        'label' => '附件类型',
                        'attribute' => 'asset_attachment_type_text',
                    ],
                    [
                        'label' => '附件状态',
                        'attribute' => 'asset_attachment_status',
                        'format' => [
                            'in',
                            [
                                AssetAttachment::ATTACHMENT_STATUS_OPEN => '有效',
                            ],
                            '无效',
                        ],
                    ],
                    [
                        'label' => '操作',
                        'format' => 'raw',
                        'value' => function (AssetAttachment $attachment) {
                            $link = Html::a('浏览', [
                                'attachment-signature',
                                'id' => $attachment->asset_attachment_id,
                                'url' => preg_replace('/^http:/', 'https:', $attachment->asset_attachment_url),
                            ], [
                                'target' => '_blank',
                                'target_browser' => true,
                                'class' => 'btn btn-sm btn-primary',
                            ]);
                            if ($attachment->asset_attachment_contract_sign_at && Yii::$app->getUser()->can('EvidenceReport')) {
                                if (Contract::findApplyId($attachment->asset_attachment_asset_item_no, $attachment->asset_attachment_type, $attachment->asset_attachment_contract_code)) {
                                    $link .= ' ' . Html::a('电签证明', [
                                            'evidence-report',
                                            'item_no' => $attachment->asset_attachment_asset_item_no,
                                            'type' => $attachment->asset_attachment_type,
                                            'contract_code' => $attachment->asset_attachment_contract_code,
                                        ], [
                                            'class' => 'btn btn-sm btn-info evidence-report-btn',
                                            'data-url' => Url::to([
                                                'evidence-report',
                                                'item_no' => $attachment->asset_attachment_asset_item_no,
                                                'type' => $attachment->asset_attachment_type,
                                                'contract_code' => $attachment->asset_attachment_contract_code,
                                            ]),
                                        ]);
                                }
                            }
                            return $link;
                        },
                    ],
                ],
            ]);
        } ?>
    </div>
</div>
<?php
$js = <<<JS
$(document).on('click', '.evidence-report-btn', function(e) {
    e.preventDefault();
    const url = $(this).data('url');
    
    layer.confirm('确定要查看电签证明吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        window.open(url, '_blank');
    });
});
JS;
$this->registerJs($js);
?>
