<?php

use dcs\models\IncStatementSearch;
use xlerr\common\widgets\GridView;
use yii\data\DataProviderInterface;
use yii\web\View;

/**
 * @var View $this
 * @var DataProviderInterface $dataProvider
 * @var IncStatementSearch $searchModel
 * @var array $columns
 * @var array $channels
 */

echo $this->render('_search', [
    'model' => $searchModel,
    'channels' => $channels,
]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => $columns,
]);
