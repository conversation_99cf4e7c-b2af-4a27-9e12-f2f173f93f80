<?php

use common\models\Liabilities;
use cpm\models\CapitalChannel;
use kartik\detail\DetailView;
use xlerr\common\helpers\MoneyHelper;

/** @var Liabilities $model */

?>

<div class="box box-info">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'krajeeDialogSettings' => [
                'overrideYiiConfirm' => false,
            ],
//            'hAlign' => DetailView::ALIGN_LEFT,
            'enableEditMode' => false,
            'labelColOptions' => [
                'style' => 'width:10%',
            ],
            'valueColOptions' => [
                'style' => 'width:20%',
            ],
            'attributes' => [
                [
                    'columns' => [
                        [
                            'attribute' => 'loan_channel',
                            'format' => ['in', CapitalChannel::list()],
                        ],
                        [
                            'attribute' => 'amount',
                            'value' => vsprintf('%s %s', [
                                MoneyHelper::f2y($model->amount, true),
                                Liabilities::CURRENCY_LIST[$model->currency] ?? $model->currency
                            ]),
                        ],
                        [
                            'attribute' => 'rate',
                            'value' => $model->getYearRate(),
                            'format' => 'percentage',
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'grant_at',
                        ],
                        [
                            'attribute' => 'due_at',
                        ],
                        [
                            'attribute' => 'period_days',
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'status',
                            'format' => ['in', Liabilities::STATUS_LIST],
                        ],
                        [
                            'attribute' => 'receipt_date',
                        ],
                        [
                            'attribute' => 'exchange_rate',
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [

                            'label' => '创建人',
                            'attribute' => 'created_by',
                            'value' => $model->operator->username ?? '-',
                        ],
                        [
                            'attribute' => 'created_at',
                        ],
                        [
                            'attribute' => 'updated_at',
                        ],
                    ],
                ],
            ]
        ]) ?>
    </div>
</div>
