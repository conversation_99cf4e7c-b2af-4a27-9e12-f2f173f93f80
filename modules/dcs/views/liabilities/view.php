<?php

use common\models\Liabilities;
use common\models\LiabilitiesCostAllocatePlan;
use common\models\LiabilitiesTrans;
use dashboard\grid\MoneyTotalDataColumn;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use Xlerr\SettlementFlow\Models\Rule;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

/**@var Liabilities $model */
/**@var LiabilitiesTrans[] $repaymentPlans */

$this->title = '详情';
$this->params['breadcrumbs'][] = ['label' => '保理借款管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
    <p>
        <?= Html::a('修改还款计划', ['repay-plan', 'id' => $model->id], [
            'class' => 'btn btn-primary',
        ]) ?>
    </p>

<?php
echo $this->render('_info', [
    'model' => $model,
]);

$bizRegions = Rule::bizRegions();

$totalPrincipal = $model->amount;
echo GridView::widget([
    'options' => [
        'class' => 'box box-warning'
    ],
    'layout' => '<div class="box-header with-border"><div class="box-title">还款计划</div></div><div class="box-body table-responsive no-padding">{items}</div>',
    'dataProvider' => new ArrayDataProvider([
        'allModels' => $model->getTrans()
            ->select([
                'period',
                'total' => 'SUM(amount)',
                'principal' => 'sum(if(type = \'principal\', amount, 0))',
                'interest' => 'sum(if(type = \'interest\', amount, 0))',
                'grant_at',
                'due_at',
            ])
            ->groupBy('liabilities_id, period')
            ->orderBy(['period' => SORT_ASC])
            ->asArray()
            ->all(),
        'pagination' => false,
        'sort' => false,
    ]),
    'columns' => [
        [
            'label' => '期次',
            'attribute' => 'period',
            'footer' => '合计'
        ],
        [
            'label' => '起息日',
            'attribute' => 'grant_at',
            'footer' => '-'
        ],
        [
            'label' => '还款日',
            'attribute' => 'due_at',
            'footer' => '-'
        ],
        [
            'label' => '应还总额',
            'attribute' => 'total',
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            'label' => '应还本金',
            'attribute' => 'principal',
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            'label' => '应还利息',
            'attribute' => 'interest',
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            'label' => '剩余本金',
            'class' => MoneyDataColumn::class,
            'value' => static function ($data) use (&$totalPrincipal) {
                return $totalPrincipal -= $data['principal'];
            },
            'footer' => '',
        ]
    ]
]);

$canLiabilitiesCostAllocate = Yii::$app->user->can('liabilities_cost_allocate');
echo GridView::widget([
    'layout' => '<div class="box-header with-border"><div class="box-title">成本分摊计划</div></div><div class="box-body table-responsive no-padding">{items}</div>',
    'dataProvider' => new ArrayDataProvider([
        'allModels' => $model->getCostAllocatePlans(),
        'pagination' => false,
        'sort' => false,
    ]),
    'columns' => [
        ...($canLiabilitiesCostAllocate ? [
            [
                'class' => DialogActionColumn::class,
                'template' => '{cost-allocate-plan}',
                'urlCreator' => function ($action, LiabilitiesCostAllocatePlan $plan) {
                    return Url::to([$action, 'id' => $plan->liabilitiesId, 'version' => $plan->version]);
                },
                'header' => Html::a('添加', ['cost-allocate-plan', 'id' => $model->id], [
                    'class' => 'btn btn-xs btn-success',
                ]),
                'buttons' => [
                    'cost-allocate-plan' => function ($url) {
                        return DialogActionColumn::newButton('编辑', $url, [
                            'class' => 'btn-primary',
                        ]);
                    }
                ],
            ]
        ] : []),
        [
            'label' => '版本号',
            'attribute' => 'version',
        ],
        [
            'label' => '生效时间',
            'value' => function (LiabilitiesCostAllocatePlan $plan) {
                return vsprintf('%s - %s', [
                    $plan->beginAt,
                    $plan->endAt,
                ]);
            }
        ],
        [
            'label' => '分摊计划',
            'attribute' => 'plan',
            'format' => function ($rawPlans) use ($bizRegions) {
                $plans = Json::decode($rawPlans);
                $content = [];
                foreach ($plans as $country => $rate) {
                    $content[] = vsprintf('%s: %.2f%%', [
                        $bizRegions[$country] ?? $country,
                        $rate * 100
                    ]);
                }

                return implode(', ', $content);
            }
        ],
    ]
]);
