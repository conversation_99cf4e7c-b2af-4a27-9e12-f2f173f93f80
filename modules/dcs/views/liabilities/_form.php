<?php

use common\models\Liabilities;
use xlerr\common\assets\MomentAsset;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model Liabilities */

MomentAsset::register($this);
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'loan_channel')->widget(Select2::class, [
        'data' => Liabilities::channelList(),
        'hideSearch' => true,
        'disabled' => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'amount', [
        'addon' => [
            'append' => [
                'content' => '元',
            ],
        ],
    ])->widget(MoneyInput::class, [
        'options' => [
            'disabled' => !$model->isNewRecord,
        ],
    ]) ?>

    <?= $form->field($model, 'yearRate', [
        'addon' => [
            'append' => [
                'content' => '%',
            ],
        ],
    ])->label('年利率<span class="text-danger">（最多两位小数, 例: 10.42%）</span>') ?>

    <?= $form->field($model, 'grant_at')->widget(DatePicker::class, [
        'options' => [
            'disabled' => !$model->isNewRecord,
        ],
    ]) ?>

    <?= $form->field($model, 'period_days')->textInput() ?>

    <?= $form->field($model, 'due_at')->textInput([
        'disabled' => true,
    ]) ?>

    <?= $form->field($model, 'receipt_date')->widget(DatePicker::class, [
        'options' => [
            'disabled' => !$model->isNewRecord,
        ],
    ]) ?>

    <?= $form->field($model, 'currency')->widget(Select2::class, [
        'data' => Liabilities::CURRENCY_LIST,
        'hideSearch' => true,
        'pluginOptions' => [
            'allowClear' => true,
        ],
        'options' => [
            'prompt' => $model->getAttributeLabel('currency'),
        ],
        'disabled' => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'exchange_rate')->textInput() ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
        'data' => [
            'confirm' => '确定保存吗?',
        ],
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
<script>
    <?php $this->beginBlock('dueAt') ?>
    const grantAtEl = $('#<?= Html::getInputId($model, 'grant_at') ?>'),
        periodDaysEl = $('#<?= Html::getInputId($model, 'period_days') ?>'),
        dueAtEl = $('#<?= Html::getInputId($model, 'due_at') ?>');

    [grantAtEl, periodDaysEl].forEach(el => {
        el.on('change keyup', function () {
            const grantAt = grantAtEl.val(), periodDays = periodDaysEl.val()
            if (grantAt && periodDays) {
                dueAtEl.val(moment(grantAt).add(periodDays, 'days').format('YYYY-MM-DD'))
            }
        })
    })

    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['dueAt']) ?>
</script>
