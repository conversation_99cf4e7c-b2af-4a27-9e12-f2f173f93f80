<?php

use common\models\Liabilities;
use common\models\LiabilitiesSearch;
use cpm\models\CapitalChannel;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel LiabilitiesSearch */

$this->title = '保理借款管理';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{view} {submit} {settled}',
            'buttons' => [
                'view' => static fn($url) => ActionColumn::newButton('查看', $url, [
                    'class' => 'btn-info layer-dialog',
                ]),
                'submit' => static function ($url) {
                    return ActionColumn::newButton('提交', $url, [
                        'data' => [
                            'method' => 'post',
                            'confirm' => '确定要提交审核?',
                        ],
//                        'target' => '_blank',
//                        'target_browser' => true,
                        'class' => 'btn-primary',
                    ]);
                },
                'settled' => static function ($url) {
                    return ActionColumn::newButton('结清', $url, [
                        'class' => 'btn-success',
                    ]);
                },
            ],
            'visibleButtons' => [
                'submit' => static function (Liabilities $model) {
                    return $model->status === Liabilities::STATUS_NEW;
                },
                'settled' => static function (Liabilities $model) {
                    return $model->status === Liabilities::STATUS_SUCCESS && empty($model->origin_due_at);
                },
            ],
        ],
        [
            'attribute' => 'loan_channel',
            'format' => ['in', CapitalChannel::list()],
        ],
        'grant_at',
        'due_at',
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'amount',
        ],
        [
            'attribute' => 'rate',
            'format' => static function ($rate) {
                return number_format($rate * 100, 2) . '%';
            },
        ],
        'period_days',
        [
            'attribute' => 'status',
            'format' => ['in', Liabilities::STATUS_LIST],
        ],
        'receipt_date',
        [
            'attribute' => 'currency',
            'format' => ['in', Liabilities::CURRENCY_LIST],
        ],
        'exchange_rate',
        [
            'label' => '创建人',
            'attribute' => 'created_by',
            'value' => 'operator.username',
        ],
        'created_at',
        'updated_at',
    ],
]);
