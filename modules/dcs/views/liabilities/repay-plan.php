<?php

use common\models\Liabilities;
use xlerr\common\assets\MomentAsset;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\MoneyInput;
use yii\helpers\Html;

/** @var Liabilities $model */
/** @var array $calculatedPlans */

MomentAsset::register($this);

$this->title = '修改还款计划';
$this->params['breadcrumbs'][] = ['label' => '保理借款管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">修改还款计划</div>
    </div>
    <?php $form = ActiveForm::begin(['id' => 'repaymentForm']); ?>
    <div class="box-body">

        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'amount')->widget(MoneyInput::class, [
                    'options' => [
                        'class' => 'form-control',
                        'readonly' => true,
                    ]
                ]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'grant_at')->widget(DatePicker::class, [
                    'options' => [
                        'readonly' => true,
                    ]
                ]) ?>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'yearRate')->textInput([
                    'class' => 'form-control',
                    'readonly' => true,
                ]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'periods', [
                    'addon' => [
                        'append' => [
                            'asButton' => true,
                            'content' => Html::button('生成还款计划', [
                                'id' => 'generatePlan',
                                'class' => 'btn btn-flat btn-success'
                            ])
                        ]
                    ]
                ])->textInput([
                    'class' => 'form-control',
                    'placeholder' => '请输入借款期数'
                ]) ?>
            </div>
        </div>
    </div>

    <div class="box-body no-padding">
        <table class="table table-striped table-bordered">
            <thead class="thead-dark">
            <tr>
                <th style="display: none;">期次</th>
                <th>还款日期</th>
                <th>最低应还总金额</th>
                <th>应还本金</th>
                <th>应还利息</th>
                <th>剩余本金</th>
            </tr>
            </thead>
            <tbody id="repaymentTableBody">
            <?php foreach ($calculatedPlans as $index => $plan) : ?>
                <tr>
                    <td style="display: none;">
                        <?= Html::input('text', "repaymentPlan[period][$index]", $plan['period']) ?>
                    </td>
                    <td>
                        <?= Html::input('date', "repaymentPlan[due_at][$index]", $plan['due_at'], [
                            'class' => 'form-control repayment-date',
                        ]) ?>
                    </td>
                    <td>
                        <?= Html::input('text', "repaymentPlan[min_repayment][$index]", $plan['min_repayment'], [
                            'class' => 'form-control min-repayment',
                            'readonly' => true,
                            'data' => [
                                'index' => $index,
                            ]
                        ]) ?>
                    </td>
                    <td>
                        <?= Html::input('text', "repaymentPlan[principal][$index]", $plan['principal'], [
                            'class' => 'form-control principal',
                            'style' => 'color: blue',
                            'data' => [
                                'index' => $index,
                            ]
                        ]) ?>
                    </td>
                    <td>
                        <?= Html::input('text', "repaymentPlan[interest][$index]", $plan['interest'], [
                            'class' => 'form-control interest',
                            'style' => 'color: green',
                            'data' => [
                                'index' => $index,
                            ]
                        ]) ?>
                    </td>
                    <td>
                        <?= Html::input(
                            'text',
                            "repaymentPlan[remaining_principal][$index]",
                            $plan['remaining_principal'],
                            [
                                'class' => 'form-control remaining-principal',
                                'style' => 'color: red',
                                'readonly' => true,
                                'data' => [
                                    'index' => $index,
                                ]
                            ]
                        ) ?>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <div class="box-footer">
        <?= Html::submitButton('保存', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('取消', ['view', 'id' => $model->id], ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<script>
    <?php $this->beginBlock('plan') ?>
    $(document).ready(function () {
        const grantAtEl = $('#<?= Html::getInputId($model, 'grant_at') ?>'),
            periodEl = $('#<?= Html::getInputId($model, 'periods') ?>'),
            periodDays = <?= $model->period_days ?>,
            tableBody = $('#repaymentTableBody');

        function moneyInputMask() {
            const minRepaymentInput = tableBody.find('input.min-repayment'),
                principalInput = tableBody.find('input.principal'),
                interestInput = tableBody.find('input.interest'),
                remainingPrincipalInput = tableBody.find('input.remaining-principal'),
                calcMinRepayment = function (e) {
                    const tr = $(this).parents('tr')
                    tr.find('input.min-repayment')
                        .val(parseInt(tr.find('input.principal').val()) + parseInt(tr.find('input.interest').val()))
                },
                totalPrincipal = <?= $model->amount ?>,
                calcRemainingPrincipal = function (e) {
                    let total = totalPrincipal;
                    remainingPrincipalInput.each(function (i, el) {
                        const input = $(el);
                        total -= parseInt(input.parents('tr').find('input.principal').val())
                        input.val(total)
                    })
                };

            minRepaymentInput.inputmask({'alias': 'iY2F'})
            principalInput.inputmask({'alias': 'iY2F'})
            interestInput.inputmask({'alias': 'iY2F'})
            remainingPrincipalInput.inputmask({'alias': 'iY2F'})

            interestInput.on('input', calcMinRepayment)
            principalInput.on('input', calcMinRepayment)
            principalInput.on('input', calcRemainingPrincipal)
        }


        $('#generatePlan').on('click', function (event) {
            event.preventDefault();

            const period = parseInt(periodEl.val()) || 0;
            if (period === 0) {
                alert('期次不能为零!');
                return;
            }

            const repaymentPlan = [], dayCount = Math.ceil(periodDays / period);
            let dueAt = grantAtEl.val();
            for (let i = 1; i <= period; i++) {
                if (i < period) {
                    dueAt = moment(dueAt).add(dayCount, 'days').format('YYYY-MM-DD')
                } else {
                    dueAt = '<?= $model->due_at ?>'
                }
                repaymentPlan.push({
                    period: i,
                    due_at: dueAt,
                    principal: 0,
                    interest: 0,
                    min_repayment: 0,
                    remaining_principal: 0
                });
            }

            tableBody.empty();
            repaymentPlan.forEach(function (repayment, index) {
                const row = `<tr>
    <td style="display: none;">
        <input type="text" name="repaymentPlan[period][${index}]" value="${repayment.period}">
    </td>
    <td>
        <input type="date" name="repaymentPlan[due_at][${index}]" value="${repayment.due_at}"
               class="form-control repayment-date">
    </td>
    <td>
        <input type="text" readonly name="repaymentPlan[min_repayment][${index}]" value="${repayment.min_repayment}"
               class="form-control min-repayment">
    </td>
    <td>
        <input type="text" name="repaymentPlan[principal][${index}]" class="form-control principal"
               value="${repayment.principal}" style="color: blue;">
    </td>
    <td>
        <input type="text" name="repaymentPlan[interest][${index}]" class="form-control interest"
               value="${repayment.interest}" style="color: green;">
    </td>
    <td>
        <input type="text" readonly name="repaymentPlan[remaining_principal][${index}]"
               class="form-control remaining-principal" value="${repayment.remaining_principal}" style="color: red;">
    </td>
</tr>`;
                tableBody.append(row);
            });

            moneyInputMask()
        });

        moneyInputMask()
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['plan']) ?>
</script>
