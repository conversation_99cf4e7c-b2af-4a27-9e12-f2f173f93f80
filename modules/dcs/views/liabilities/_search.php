<?php

use common\models\Liabilities;
use common\models\LiabilitiesSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model LiabilitiesSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <i class="fa fa-search"></i>
        <h3 class="box-title">搜索</h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'loan_channel', [
            'options' => [
                'style' => 'min-width: 120px',
            ],
        ])->widget(Select2::class, [
            'data' => Liabilities::channelList(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('loan_channel'),
            ],
        ]) ?>

        <?= $form->field($model, 'startDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'status', [
            'options' => [
                'style' => 'min-width: 120px',
            ],
        ])->widget(Select2::class, [
            'data' => Liabilities::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('status'),
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>
        <?= Html::a('补录记录', ['create'], ['class' => 'btn btn-success layer-dialog']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>