<?php

use common\models\Liabilities;
use common\models\LiabilitiesCostAllocatePlan;
use xlerr\common\assets\VueAsset;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use yii\helpers\Html;

/** @var Liabilities $model */
/** @var LiabilitiesCostAllocatePlan $costAllocatePlan */

VueAsset::register($this);

$this->title = '修改成本分摊计划';
$this->params['breadcrumbs'][] = ['label' => '保理借款管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_info', [
    'model' => $model,
]);
?>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">成本分摊配置</div>
    </div>
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body">
        <div class="row">
            <div class="col-md-4">
                <?= $form->field($costAllocatePlan, 'version')->textInput([
                    'readonly' => !empty($costAllocatePlan->version), // 添加分摊配置时，版本好为空，允许修改版本号。否则禁止修改
                ]) ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($costAllocatePlan, 'beginAt')->widget(DatePicker::class) ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($costAllocatePlan, 'endAt')->widget(DatePicker::class) ?>
            </div>
        </div>

    </div>
    <div class="box-header with-border">
        <div class="box-title">比例配置</div>
    </div>

    <div class="box-body" id="app">
        <div style="display: flex; flex-flow: row nowrap; gap: 30px;">
            <div style="display: flex; flex-flow: column nowrap; gap: 15px; flex: 1">
                <input type="hidden" :value="JSON.stringify(data)"
                       name="<?= Html::getinputname($costAllocatePlan, 'plan') ?>"/>
                <template v-for="(val, country) in data">
                    <div class="input-group">
                        <span class="input-group-addon" style="width: 60px"><strong>{{country}}</strong></span>
                        <input-percent v-model="data[country]" class="form-control"></input-percent>
                        <span class="input-group-addon">%</span>
                    </div>
                </template>
                <div>
                    当前总比例为: {{(totalRate * 100).toFixed(2)}}%.
                    <span class="text-danger" v-if="totalRate !== 1.0"><br/>分摊比例异常: 总比例必须为100%.</span>
                </div>
            </div>
            <div style="padding-top: 100px;">
                <div @click="applyLoanRate" class="btn btn-flat btn-success">
                    <i class="fa fa-angle-double-left"></i>
                </div>
            </div>
            <div style="display: flex; flex-flow: column nowrap; gap: 15px; flex: 1">
                <div class="input-group">
                    <span class="input-group-addon"><b>日期</b></span>
                    <input type="date" v-model="date" class="form-control"/>
                    <span class="input-group-btn">
                    <button :disabled="loading" @click.prevent.stop="fetchLoanRate" class="btn btn-flat btn-primary">
                        <i v-show="loading" class="fa fa-spinner fa-spin"></i>
                        获取在贷占比
                    </button>
                </span>
                </div>
                <pre style="min-height:180px">{{JSON.stringify(loanRate, null, '  ')}}
<a v-if="sql" href="/" @click.prevent.stop="showSql">查看计算逻辑</a>
</pre>
            </div>
        </div>
    </div>

    <div class="box-footer">
        <?= Html::submitButton('保存', [
            'id' => 'submit-button',
            'class' => 'btn btn-primary'
        ]) ?>

        <?= Html::a('取消', ['view', 'id' => $model->id], ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<script>
    <?php $this->beginBlock('plan') ?>
    const {createApp, ref, reactive, computed, watch} = Vue,
        submitButton = document.getElementById('submit-button')
    createApp({
        setup() {
            const rawData = <?= $costAllocatePlan->plan ?>,
                data = reactive(_.merge({tha: 0, mex: 0, phl: 0, pak: 0, idn: 0}, rawData)),
                totalRate = computed(() => Object.values(data).reduce((a, b) => a + parseFloat(b), 0)),
                date = ref('<?= $model->grant_at ?>'),
                loanRate = ref({}),
                sql = ref(''),
                showSql = () => {
                    layer.alert(`<pre>${sql.value}</pre>`)
                },
                loading = ref(false),
                fetchLoanRate = () => {
                    if (!date.value) {
                        alert('请选择日期')
                        return
                    }
                    loading.value = true;
                    fetch('loan-balance-allocate-rate?date=' + date.value).then(res => res.json()).then(res => {
                        loanRate.value = res.config
                        sql.value = res.sql
                    }).finally(() => loading.value = false)
                },
                applyLoanRate = () => {
                    Object.keys(data).forEach(country => {
                        data[country] = loanRate.value[country] ?? 0
                    })
                }

            watch(totalRate, (val) => {
                submitButton.disabled = val !== 1.0;
            }, {immediate: true})

            return {data, totalRate, date, loanRate, sql, showSql, loading, fetchLoanRate, applyLoanRate}
        }
    }).component('input-percent', {
        props: ['modelValue'],
        emit: ['update:modelValue'],
        setup(props, {emit}) {
            const val = computed(() => (props.modelValue * 100).toFixed(2)),
                onInput = (e) => {
                    emit('update:modelValue', Math.round(e.target.value * 100) / 10000)
                }

            return {val, onInput}
        },
        template: `<input type="number" :value="val" @input="onInput" />`
    }).mount('#app');
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['plan']) ?>
</script>
