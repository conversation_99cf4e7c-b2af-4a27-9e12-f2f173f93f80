<?php

use common\models\Asset;
use dcs\models\AssetReverse;
use dcs\models\AssetReverseSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model AssetReverseSearch */
/* @var $form ActiveForm */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-search"></i> 搜索</h3>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'loan_channel', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 140px',
            ],
        ])->widget(Select2::class, [
            'data' => Asset::channelList(true),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('loan_channel'),
            ],
        ]) ?>

        <?= $form->field($model, 'status', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 120px',
            ],
        ])->widget(Select2::class, [
            'data' => AssetReverse::statusList(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('status'),
            ],
        ]) ?>

        <?= $form->field($model, 'startDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '结束时间',
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']) ?>

        <?= Html::a('添加', ['create'], [
            'class' => 'btn btn-success layer-dialog',
        ]) ?>

        <?= Html::a('确认结清', 'javascript:;', ['class' => 'btn btn-twitter confirm-btn']) ?>

        <?php ActiveForm::end(); ?>

    </div>

</div>
