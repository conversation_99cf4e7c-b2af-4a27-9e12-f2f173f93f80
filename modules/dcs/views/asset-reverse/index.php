<?php

use common\consts\Consts;
use common\models\Asset;
use dcs\models\AssetReverse;
use dcs\models\AssetReverseSearch;
use dcs\models\AssetReverseTran;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\grid\CheckboxColumn;
use yii\web\View;
use yii\widgets\ActiveForm;

/* @var $this View */
/* @var $searchModel AssetReverseSearch */
/* @var $dataProvider ActiveDataProvider */

$this->title = '异常结清资产';

$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', [
    'model' => $searchModel,
]);

$form = ActiveForm::begin([
    'id'     => 'confirmForm',
    'method' => 'post',
    'action' => ['confirm'],
]);

echo GridView::widget([
    'id'           => 'confirmGrid',
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'class'           => CheckboxColumn::class,
            'checkboxOptions' => static function (AssetReverse $model) {
                $flag = $model->status !== AssetReverse::STATUS_NEED_CONFIRM;

                return [
                    'disabled' => $flag,
                    'style'    => 'display:' . ($flag ? 'none' : 'block'),
                ];
            },
        ],
        [
            'attribute' => 'loan_channel',
            'format'    => ['in', Asset::channelList()],
        ],
        'asset_item_no',
        [
            'label'     => '合同本金',
            'class'     => MoneyDataColumn::class,
            'attribute' => 'capitalAsset.capital_asset_origin_amount',
        ],
        [
            'label'     => '放款日期',
            'attribute' => 'capitalAsset.capital_asset_granted_at',
        ],
        [
            'label' => '贴息金额',
            'class' => MoneyDataColumn::class,
            'value' => static function (AssetReverse $model) {
                return (int)$model
                    ->getTrans()
                    ->andWhere(['<>', 'type', AssetReverseTran::TYPE_PRINCIPAL])
                    ->sum('amount');
            },
        ],
        [
            'label'     => '异常结清日期',
            'attribute' => 'callback_at',
        ],
        [
            'attribute' => 'status',
            'format'    => ['in', AssetReverse::statusList()],
        ],
    ],
]);

ActiveForm::end();
?>
<script>
    <?php $this->beginBlock('js') ?>
    $('a.confirm-btn').click(function () {
        let idList = $('#confirmGrid').yiiGridView("getSelectedRows");
        if (idList.length <= 0) {
            window.alert('请选择确认结清的数据');
        } else if (window.confirm('确定要将选中数据设为已结清吗？')) {
            $('#confirmForm').submit();
        }
        return false;
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>
