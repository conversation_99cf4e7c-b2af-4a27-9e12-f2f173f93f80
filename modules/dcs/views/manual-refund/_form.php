<?php

use dcs\models\ManualRefund;
use kvmanager\models\KeyValue;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model ManualRefund */
?>

<?php
$form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'asset_item_no')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'refund_limit')->widget(Select2::class, [
        'data' => (array)(KeyValue::takeAsArray('manual_refund_config')['reason'] ?? []),
        'hideSearch' => true,
        'pluginOptions' => [
            'allowClear' => true,
        ],
        'options' => [
            'prompt' => $model->getAttributeLabel('refund_limit'),
        ],
    ]) ?>

    <?= $form->field($model, 'refund_amount')->widget(MoneyInput::class)->label('金额(元)') ?>


    <?= $form->field($model, 'source_system')->widget(Select2::class, [
        'data' => ManualRefund::SOURCE_SYSTEM_LIST,
        'hideSearch' => true,
        'pluginOptions' => [
            'allowClear' => true,
        ],
        'options' => [
            'prompt' => $model->getAttributeLabel('source_system'),
        ],
    ]) ?>

    <?= $form->field($model, 'cardSource')->widget(Select2::class, [
        'data' => ManualRefund::CARD_SOURCE_LIST,
        'hideSearch' => true,
        'pluginOptions' => [
            'allowClear' => true,
        ],
        'options' => [
            'prompt' => $model->getAttributeLabel('cardSource'),
        ],
    ]) ?>


    <div id="showCardInfo">
        <?= $form->field($model, 'receiver_name')->textInput(['readonly' => true]) ?>

        <?= $form->field($model, 'receiver_id_num')->textInput(['readonly' => true]) ?>

        <?= $form->field($model, 'receiver_card_num')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'bank_name')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'receiver_phone')->textInput(['maxlength' => true]) ?>
    </div>


    <div id="showSerialNo">
        <?= $form->field($model, 'refund_withhold_serial_no')->textInput(['maxlength' => true]) ?>
    </div>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php
ActiveForm::end(); ?>

<script>
    <?php  $this->beginBlock('assetInfo') ?>

    const nameEl = $('#<?= Html::getInputId($model, 'receiver_name') ?>'),
        identityEl = $('#<?= Html::getInputId($model, 'receiver_id_num') ?>'),
        cardNoEl = $('#<?= Html::getInputId($model, 'receiver_card_num') ?>'),
        phoneEl = $('#<?= Html::getInputId($model, 'receiver_phone') ?>'),
        branchNameEl = $('#<?= Html::getInputId($model, 'bank_name') ?>'),
        cardSourceEl = $('#<?= Html::getInputId($model, 'cardSource') ?>'),
        itemNoEl = $('#<?= Html::getInputId($model, 'asset_item_no') ?>'),
        serialNoEl = $('#<?= Html::getInputId($model, 'refund_withhold_serial_no') ?>')
    refundLimitEl = $('#<?= Html::getInputId($model, 'refund_limit') ?>')
    let timer

    function toggleCardInfo() {
        if (cardSourceEl.val() !== 'specified_card') {
            setCardEmpty()
            $('#showCardInfo').hide();
        } else {
            post()
            $('#showCardInfo').show();
        }
    }

    function toggleSerialNo (){
        let val = refundLimitEl.val()
        if (val === 'refund_after_withhold') {
            cardSourceEl.val('origin_repay_card').trigger('change');
            $('#showSerialNo').show()
        } else {
            serialNoEl.val('')
            $('#showSerialNo').hide()
        }
    }

    function setCardEmpty() {
        nameEl.val('')
        identityEl.val('')
        cardNoEl.val('')
        phoneEl.val('')
        branchNameEl.val('')
    }

    function post() {
        timer && window.clearTimeout(timer)
        timer = window.setTimeout(() => {
            const val = itemNoEl.val()
            if (!val) {
                return
            }
            console.log(val)
            if (cardSourceEl.val() === 'specified_card') {
                $.post('<?= Url::to(['asset-info'])?>', {
                    assetItemNo: val,
                }, function (res) {
                    if (res.code) {
                        setCardEmpty()
                        layer.alert(res.msg, {
                            icon: 2
                        })
                    } else {
                        nameEl.val(res.data.name)
                        identityEl.val(res.data.identity)
                        cardNoEl.val(res.data.cardNo)
                        phoneEl.val(res.data.phone)
                        branchNameEl.val(res.data.branchName)
                    }
                })
            }
        }, 500)
    }

    //当提交申请还款返回异常验证是否需要把四要素展示出来
    $(document).ready(function () {
        toggleCardInfo();
        toggleSerialNo();
    });
    cardSourceEl.on('change', toggleCardInfo)
    itemNoEl.on('change keyup', post);
    refundLimitEl.on('change', toggleSerialNo)
    <?php  $this->endBlock() ?>
    <?php  $this->registerJs($this->blocks['assetInfo']) ?>
</script>