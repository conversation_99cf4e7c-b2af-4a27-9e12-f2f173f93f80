<?php

use dcs\models\PrepareCollection;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model PrepareCollection */
/** @var $reserveTypes array */
/** @var $isUpdate bool */
$this->title = $model->id;
$this->params['breadcrumbs'][] = [
    'label' => '待归集订单列表',
    'url' => [
        'index',
    ],
];

?>

<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">基本信息</h3>
    </div>

    <?php $form = ActiveForm::begin([
        'action' => '',
        'method' => 'post',
        'type' => ActiveForm::TYPE_INLINE,
    ]) ?>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'collection_order_no',
            'loan_channel',
            'asset_item_no',
            'withhold_serial_no',
            'withhold_channel',
            'collection_channel',
            [
                'class' => MoneyDataColumn::class,
                'attribute' => 'withhold_amount',
            ],
            [
                'class' => MoneyDataColumn::class,
                'attribute' => 'amount',
            ],
            'deposit',
            [
                'attribute' => 'actual_deposit',
                'format' => 'raw',
                'value' => static function (PrepareCollection $model) use ($form) {
                    if ($model->status === PrepareCollection::STATUS_INIT) {
                        return $form->field($model, 'actual_deposit', [
                            'options' => [
                                'style' => 'min-width: 200px',
                            ]
                        ])->widget(Select2::class, [
                            'data' => PrepareCollection::getDeposits(),
                            'hideSearch' => true,
                            'pluginOptions' => [
                                'allowClear' => true,
                            ],
                            'options' => [
                                'prompt' => $model->getAttributeLabel('actual_deposit'),
                            ],
                        ]);
                    }

                    return $model->actual_deposit;
                }
            ],
            [
                'attribute' => 'withhold_entity',
                'format' => 'raw',
                'value' => static function (PrepareCollection $model) use ($form) {
                    if ($model->status === PrepareCollection::STATUS_INIT) {
                        return $form->field($model, 'withhold_entity', [
                            'options' => [
                                'style' => 'min-width: 200px',
                            ]
                        ])->widget(Select2::class, [
                            'data' => PrepareCollection::supportEntities(),
                            'hideSearch' => true,
                            'pluginOptions' => [
                                'allowClear' => true,
                            ],
                            'options' => [
                                'prompt' => $model->getAttributeLabel('withhold_entity'),
                            ],
                        ]);
                    }

                    return $model->withhold_entity;
                }
            ],
            'expect_collection_at',
            'finish_at',
            [
                'attribute' => 'status',
                'format' => ['in', PrepareCollection::STATUS_LIST],
            ],
            [
                'attribute' => 'settlement_status',
                'format' => ['in', PrepareCollection::SETTLEMENT_STATUS_LIST],
            ],
            'withhold_finish_at',
            'create_at',
            'update_at',
            [
                'label' => '',
                'format' => 'raw',
                'value' => Html::submitButton('保存', [
                    'class' => 'btn btn-primary',
                ]),
            ],
        ],
    ]) ?>

    <?php ActiveForm::end() ?>
</div>