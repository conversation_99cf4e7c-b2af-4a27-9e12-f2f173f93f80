<?php

use common\models\Asset;
use dcs\models\CleanNoLoanProvisionRate;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use xlerr\common\widgets\DatePicker;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model CleanNoLoanProvisionRate */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">


    <?= $form->field($model, 'subject')->widget(Select2::class, [
        'data' => CleanNoLoanProvisionRate::subjectList(),
//        'disabled' => !$model->isNewRecord,
    ]) ?>
    <?= $form->field($model, 'grant_channel')->widget(Select2::class, [
        'data' => Asset::channelList(true),
//        'disabled' => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'grant_date')->widget(DatePicker::class, [
        'options' => [
            'placeholder'  => '生效时间',
            'autocomplete' => 'off',
        ],
    ]) ?>
    <?= $form->field($model, 'rate')->textInput([
        'maxlength' => true,
    ]) ?>
    <?= $form->field($model, 'cmdb_no')->textInput([
        'maxlength' => true,
    ]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', ['class' => 'btn btn-primary']) ?>
</div>

<?php ActiveForm::end(); ?>
<script>
    <?php $this->beginBlock('jsBlock')?>
    const rate = $('#<?= Html::getInputId($model, 'rate') ?>'),
        condition = {
            min: 0,
            max: 1,
        };

    rate.on('keyup', function () {
        const self = $(this);
        let val = parseFloat(self.val());
        if (val < condition.min) {
            alert('比例不能小于' + condition.min);
            self.val('');
        } else if (val > condition.max) {
            alert('比例不能大于' + condition.max);
            self.val('');
        }
    });


    <?php $this->endBlock()?>
    <?php $this->registerJs($this->blocks['jsBlock'])?>
</script>

