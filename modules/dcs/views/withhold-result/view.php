<?php

use common\models\Asset;
use common\models\Bank;
use common\models\Withhold;
use xlerr\common\helpers\MoneyHelper;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var Asset $asset */
/* @var $model array */

$this->title = '代扣详情: ' . $model['withhold_detail_asset_item_no'];

$this->params['breadcrumbs'][] = [
    'label' => '代扣记录',
    'url'   => [
        'index',
        'itemNo' => $model['withhold_detail_asset_item_no'],
    ],
];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">代扣详情</div>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model'      => $model,
            'options'    => [
                'class' => 'table table-striped detail-view',
            ],
            'template'   => '<tr><th style="width:30%">{label}</th><td>{value}</td></tr>',
            'attributes' => [
                [
                    'label'     => '资产编号',
                    'attribute' => 'withhold_detail_asset_item_no',
                    'format'    => function ($itemNo) {
                        return Html::a($itemNo, ['assets/view-item', 'item_no' => $itemNo]);
                    },
                ],
                [
                    'label' => '总期次',
                    'value' => function () use ($asset) {
                        return $asset->asset_period_count;
                    },
                ],
                [
                    'attribute' => 'withhold_amount',
                    'value'     => sprintf('%s', MoneyHelper::f2y($model['withhold_amount'])) .
                        ($model['withhold_channel_fee'] > 0 ? vsprintf('%s还款金额:%s%s支付通道手续费-用户承担部分:%s', [
                            '<br>',
                            MoneyHelper::f2y($model['withhold_amount'] - $model['withhold_channel_fee']),
                            '<br>',
                            MoneyHelper::f2y($model['withhold_channel_fee']),
                        ]) : ''),
                    'format'    => 'html',
                    'label'     => '代扣金额',
                ],
                [
                    'label'     => '支付状态',
                    'attribute' => 'withhold_status',
                    'format'    => ['in', Withhold::status()],
                ],
                [
                    'label'     => '支付渠道',
                    'attribute' => 'withhold_channel',
                ],
                [
                    'attribute' => 'withhold_comment',
                    'label'     => '备注',
                ],
                [
                    'attribute' => 'withhold_channel_message',
                    'label'     => '通道返回信息',
                ],
                [
                    'label'     => '创建者',
                    'attribute' => 'withhold_request_creator',
                ],
                [
                    'label'     => '操作人',
                    'attribute' => 'withhold_request_operator',
                ],
                [
                    'label'     => '用户手机号',
                    'attribute' => 'withhold_user_phone',
                    'format'    => ['DDecrypt', false],
                ],
                [
                    'label'     => '用户姓名',
                    'attribute' => 'withhold_user_name',
                    'format'    => ['DDecrypt'],
                ],
                [
                    'label'     => '身份证',
                    'attribute' => 'withhold_user_idnum',
                    'format'    => ['DDecrypt', false],
                ],
                [
                    'label' => '用户银行卡卡行',
                    'value' => function () use ($model) {
                        return Bank::getBankNameForCode($model['withhold_request_bank_code']);
                    },
                ],
                [
                    'attribute' => 'withhold_serial_no',
                    'label'     => '订单号',
                ],
                [
                    'attribute' => 'withhold_custom_code',
                    'label'     => '自定义code',
                ],
                [
                    'attribute' => 'withhold_finish_at',
                    'label'     => '代扣完成时间',
                ],
                [
                    'attribute' => 'withhold_create_at',
                    'label'     => '创建时间',
                ],
                [
                    'attribute' => 'withhold_update_at',
                    'label'     => '更新时间',
                ],
            ],
        ]) ?>
    </div>
</div>
