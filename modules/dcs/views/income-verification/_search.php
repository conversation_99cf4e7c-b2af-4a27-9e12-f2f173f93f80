<?php

use cpm\models\CapitalChannel;
use dcs\models\IncomeVerification;
use dcs\models\IncomeVerificationSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use xlerr\common\widgets\MoneyInput;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model IncomeVerificationSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'loan_channel', [
            'options' => [
                'style' => 'min-width: 240px',
            ]
        ])->widget(Select2::class, [
            'data' => CapitalChannel::list(),
            'hideSearch' => false,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('loan_channel')
            ]
        ]) ?>

        <?= $form->field($model, 'deposit_flow_serial_no') ?>

        <?= $form->field($model, 'depbank_order_no') ?>

        <?= $form->field($model, 'status', [
            'options' => [
                'style' => 'min-width: 120px',
            ]
        ])->widget(Select2::class, [
            'data' => IncomeVerification::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('status'),
            ]
        ]) ?>

        <?= $form->field($model, 'startTradeDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'endTradeDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'in_acct_bank') ?>
        <?= $form->field($model, 'in_acct_no') ?>
        <?= $form->field($model, 'out_acct_bank') ?>
        <?= $form->field($model, 'out_acct_no') ?>
        <?= $form->field($model, 'amount')->widget(MoneyInput::class) ?>
        <?= $form->field($model, 'memo') ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>