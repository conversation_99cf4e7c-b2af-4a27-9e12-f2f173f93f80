<?php

use dcs\approvals\OfflineFeeDatasourceApproval;
use dcs\approvals\OfflineFeeRuleApproval;
use dcs\models\OfflineFeeDatasource;
use dcs\models\OfflineFeeRule;
use waterank\audit\widgets\AuditorWidget;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model OfflineFeeDatasource */
/* @var $actionName */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'data_source_name')->textInput([
        'maxlength' => 33,
        'disabled' => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'data_source')->textInput([
        'maxlength' => 16,
    ]) ?>

    <?= $form->field($model, 'data_source_sql')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode' => CodeEditor::MODE_SQL,
            'minLines' => 20,
            'maxLines' => 50,
        ],
    ]) ?>

    <?= AuditorWidget::widget([
        'data' => OfflineFeeDatasourceApproval::auditors(),
    ]) ?>
    <?= Html::activeHiddenInput($model, 'version') ?>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
        'data' => [
            'confirm' => '确定保存吗?',
            'method' => 'post',
        ],
    ]) ?>
</div>

<?php ActiveForm::end(); ?>

