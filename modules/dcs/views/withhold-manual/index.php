<?php

use common\models\WithholdManual;
use cpm\models\CapitalChannel;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use yii\data\DataProviderInterface;
use yii\helpers\Html;
use yii\web\View;

/** @var DataProviderInterface $dataProvider */
/** @var WithholdManual $searchModel */
/** @var View $this */

$this->title = '手动推送信息流';

?>

<div class="box search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= $form->field($searchModel, 'assetItemNo') ?>

        <?= $form->field($searchModel, 'phone') ?>

        <?= $form->field($searchModel, 'idNum') ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', [
            'class' => 'btn btn-primary',
        ]) ?>

        <?= Html::a('重置搜索条件', [''], [
            'class' => 'btn btn-default',
        ]) ?>

        <?= Html::a('新增推送', ['create'], ['class' => 'btn btn-success']) ?>

        <?php ActiveForm::end() ?>
    </div>
</div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        'serialNo',
        'assetItemNo',
        'period',
        [
            'attribute' => 'username',
            'format' => 'DDecrypt',
        ],
        [
            'attribute' => 'channel',
            'format' => ['in', CapitalChannel::list()],
        ],
        [
            'attribute' => 'repayType',
            'format' => ['in', ['SINGLE' => '还单期', 'SETTLEMENT' => '结清']],
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'principal',
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'interest',
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'fee',
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'lateinterest',
        ],
        'pushAt',
        'pushStatus',
    ],
]) ?>
