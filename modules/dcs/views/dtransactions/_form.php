<?php

use common\models\Dtransaction;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\Dtransaction */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="box-body">

    <?php
    $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'dtransaction_period')->textInput(['maxlength' => 10]) ?>

    <?= $form->field($model, 'dtransaction_type')->widget(Select2::class, [
        'data'       => Dtransaction::TYPES,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'dtransaction_amount_f')->widget(MoneyInput::class) ?>

    <?= $form->field($model, 'dtransaction_repaid_amount_f')->widget(MoneyInput::class) ?>

    <?= $form->field($model, 'dtransaction_status')->widget(Select2::class, [
        'data'       => Dtransaction::STATUS,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'dtransaction_expect_finish_time')->widget(DatePicker::class) ?>

    <?= $form->field($model, 'dtransaction_finish_at')->widget(DatePicker::class) ?>

</div>
<div class="box-footer">
    <?= Html::submitButton('保存', ['class' => 'btn btn-primary']) ?>
</div>

<?php
ActiveForm::end(); ?>
