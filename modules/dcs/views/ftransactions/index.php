<?php

use common\consts\Consts;
use common\models\Fee;
use dcs\models\FtransactionSearch;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $searchModel FtransactionSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$model = $searchModel;

$this->title = '资产费用纪录';

$this->params['breadcrumbs'][] = $this->title;
?>

<div class="box box-default">
    <div class="box-header with-border">
        <div class="box-title">条件筛选</div>
    </div>
    <div class="box-body">

        <?php
        $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'get',
            'type'   => ActiveForm::TYPE_INLINE,
        ]); ?>

        <?= $form->field($model, 'ftransaction_status')->widget(Select2::class, [
            'data'          => [
                'finish'   => '已完成',
                'nofinish' => '未完成',
                'update'   => '已更新',
            ],
            'hideSearch'    => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options'       => [
                'prompt' => $model->getAttributeLabel('ftransaction_status'),
            ],
        ]) ?>

        <?= Html::submitButton('搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']) ?>

        <?php
        ActiveForm::end(); ?>

    </div>
</div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'class'    => 'xlerr\common\grid\ActionColumn',
            'template' => '{view}',
        ],
        [
            'attribute' => 'asset.asset_item_no',
            'format'    => function ($v) {
                return Html::a($v, ['assets/index', 'asset_item_no' => $v], [
                    'target' => '_blank',
                ]);
            },
        ],
        'ftransaction_period',
        [
            'attribute' => 'fee.fee_type',
            'format'    => ['in', Fee::feeTypeDropdownList()],
        ],
        [
            'label'     => '实际应还金额',
            'attribute' => 'ftransaction_amount_f',
            'class'     => MoneyDataColumn::class,
        ],
        [
            'label'     => '实际已还金额',
            'attribute' => 'ftransaction_repaid_amount_f',
            'class'     => MoneyDataColumn::class,
        ],
        [
            'label'     => '已减免金额',
            'attribute' => 'ftransaction_decrease_amount_f',
            'class'     => MoneyDataColumn::class,
        ],
        [
            'label' => '应还总金额',
            'class' => MoneyDataColumn::class,
            'value' => function ($item) {
                return $item['ftransaction_amount_f'] + $item['ftransaction_decrease_amount_f'];
            },
        ],
        'ftransactionStatusText',
        [
            'label'     => '预期完成时间',
            'attribute' => 'ftransaction_expect_finish_time',
            'format'    => function ($v) {
                return $v == Consts::DEFAULT_DATE ? '-' : $v;
            },
        ],
        [
            'label'     => '完成时间',
            'attribute' => 'ftransaction_finish_at',
            'format'    => function ($v) {
                return $v == Consts::DEFAULT_DATE ? '-' : $v;
            },
        ],
    ],
]); ?>
