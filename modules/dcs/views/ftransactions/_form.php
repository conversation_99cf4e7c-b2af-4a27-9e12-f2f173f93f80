<?php

use common\models\Fee;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\Ftransaction */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="ftransaction-form">

    <?php $form = ActiveForm::begin(); ?>
    
    <?= $form->field($model, 'ftransaction_asset_id')->dropDownList($asset, ['class' => 'form-control chosen-select col-lg-7']) ?>
    
    <?= $form->field($model, 'ftransaction_period')->textInput(['maxlength' => 10]) ?>
    
    <div class="form-group">
        <label class="control-label">费用类型</label>
        <select id="fee_type" class="form-control" name="fee_type">
            <?php
            $types = (new Fee())->feeTypeDropdownList;
            foreach ($types as $key => $value) {
                $string = "<option value='$key' ";
                if (isset($model->fee->fee_type) && $model->fee->fee_type == $key) {
                    $string .= "selected";
                }
                $string .= ">$value</option>";
                echo $string;
            }
            ?>
        </select>
    </div>

    <?= $form->field($model, 'ftransaction_status')->dropDownList($model->ftransactionStatusDropdownList, ['prompt' => '']) ?>

    <?= $form->field($model, 'ftransaction_amount')->textInput(['maxlength' => 10]) ?>
    
    <?= $form->field($model, 'ftransaction_repaid_amount')->textInput(['maxlength' => 10]) ?>
    
    <?= $form->field($model, 'ftransaction_decrease_amount')->textInput(['maxlength' => 10]) ?>

    <?= $form->field($model, 'ftransaction_expect_finish_time')->textInput(['class' => 'datepicker-input form-control', 'data-date-format' => "yyyy-mm-dd"]) ?>
    
    <?= $form->field($model, 'ftransaction_finish_at')->textInput(['value' => $model->ftransaction_finish_at ? : '1000-01-01']) ?>
    
    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
