<?php

use dcs\models\DepbankManual;
use dcs\models\TransferInDepbankManual;
use kartik\widgets\DateTimePicker;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model TransferInDepbankManual */

$this->title = '普通线下转账等入金';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="form-group">
    <?= Html::label('选择存管', 'select-deposit', ['class' => 'control-label']) ?>
    <?= Select2::widget([
        'name' => 'select_deposit',
        'data' => DepbankManual::getActiveDepositList(), // 存管列表数据
        'options' => [
            'placeholder' => '请选择存管',
            'id' => 'select-deposit',
            'class' => 'select-deposit',
        ],
        'pluginOptions' => [
            'allowClear' => true,
        ],
    ]) ?>
</div>
<div class="box box-primary">
    <div class="box-header with-border">补录转账</div>
    <?php $form = ActiveForm::begin([
        'options' => ['class' => 'create-form'],
    ]); ?>
    <div class="box-body">
        <?= Html::activeHiddenInput($model, 'out_account_bank') ?>

        <?= $form->field($model, 'out_account_no')->widget(Select2::class, [
            'data' => [],
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'class' => 'transfer_out',
                'placeholder' => '请选择账户',
            ],
        ]) ?>

        <?= $form->field($model, 'in_account_no')->widget(Select2::class, [
            'data' => [],
            'options' => [
                'class' => 'transfer_in',
                'placeholder' => '请选择账户',
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'amount')->widget(MoneyInput::class) ?>

        <?= $form->field($model, 'finish_at')->widget(DateTimePicker::class, [
            'type' => DateTimePicker::TYPE_INPUT,
            'pluginOptions' => [
                'todayBtn' => 'linked',
                'todayHighlight' => true,
                'autoclose' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'memo')->textarea([
            'rows' => 6,
            'placeholder' => '请输入转账备注',
        ]) ?>

        <?= $form->field($model, 'reserve_type')->widget(Select2::class, [
            'data' => [],
            'options' => [
                'id' => 'reserve_in',
            ],
        ])->label('流入类型') ?>

        <?= $form->field($model, 'reserve_type')->widget(Select2::class, [
            'data' => [],
            'options' => [
                'id' => 'reserve_out',
            ],
        ])->label('流出类型') ?>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('保存', [
            'class' => 'btn btn-success',
        ]) ?>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<script>
    <?php $this->beginBlock('js') ?>
    let depositEl = $('#<?= Html::getInputId($model, 'out_account_bank') ?>'),
        outAccountId = '#<?= Html::getInputId($model, 'out_account_no') ?>',
        inAccountId = '#<?= Html::getInputId($model, 'in_account_no') ?>',
        elInReserve = $('#reserve_in'),
        elOutReserve = $('#reserve_out');

    let reserveAccounts, depositAccountOutMaps, depositAccountInMaps, reserveInTypes, reserveOutTypes = [];

    // 默认隐藏流入和流出类型的字段
    $(document).ready(function () {
        elInReserve.parent('div.form-group').hide();
        elOutReserve.parent('div.form-group').hide();

        // 根据条件显示或隐藏字段的逻辑
        $(outAccountId + ', ' + inAccountId).change(function () {
            let deposit = $('#select-deposit').val();
            if (reserveAccounts) {
                // if (reserveAccounts.indexOf($(outAccountId).val()) >= 0) {
                if (reserveAccounts.indexOf($(outAccountId).val().replace('_' + deposit, '')) >= 0) {
                    elOutReserve.removeAttr('disabled').parent('div.form-group').show();
                    elInReserve.attr('disabled', true).parent('div.form-group').hide();
                    setNewOptions(elOutReserve, reserveOutTypes);
                } else if (reserveAccounts.indexOf($(inAccountId).val().replace('_' + deposit, '')) >= 0) {
                    elInReserve.removeAttr('disabled').parent('div.form-group').show();
                    elOutReserve.attr('disabled', true).parent('div.form-group').hide();
                    setNewOptions(elInReserve, reserveInTypes);
                } else {
                    elInReserve.attr('disabled', true).parent('div.form-group').hide();
                    elOutReserve.attr('disabled', true).parent('div.form-group').hide();
                }
            }
        });
        $(outAccountId).trigger('change');
    });

    $('#select-deposit').on('change', function () {
        const deposit = $(this).val();
        depositEl.val(deposit)
        updateDepositOptions(deposit);
    });

    function setNewOptions(select, options) {
        select.empty()
        for (const [k, v] of Object.entries(options)) {
            select.append(new Option(v, k))
        }
    }

    function updateDepositOptions(deposit) {
        $.ajax({
            url: '<?= Url::to(['transfer-in-info']) ?>',
            method: 'GET',
            data: {deposit: deposit},
            success: function (response) {
                const outAccountSelects = $('.transfer_out');
                const inAccountSelects = $('.transfer_in');

                reserveAccounts = response.data.reserveAccounts;
                depositAccountOutMaps = response.data.depositAccountOutMaps;
                depositAccountInMaps = response.data.depositAccountInMaps;
                reserveInTypes = response.data.reserveInTypes;
                reserveOutTypes = response.data.reserveOutTypes;
                outAccountSelects.each(function () {
                    setNewOptions($(this), depositAccountOutMaps);
                });

                inAccountSelects.each(function () {
                    setNewOptions($(this), depositAccountInMaps);
                });
            }
        });
    }

    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>