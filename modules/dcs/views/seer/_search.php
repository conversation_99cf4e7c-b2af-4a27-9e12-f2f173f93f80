<?php

use dcs\services\SeerDepositService;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model SeerDepositService */

?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">搜索</div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>
    <div class="box-body">
        <?php
        $form = ActiveForm::begin([
            'action'        => [''],
            'method'        => 'get',
            'type'          => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= $form->field($model, 'startDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'channel', [
            'options' => [
                'style' => 'min-width: 180px',
            ],
        ])->widget(Select2::class, [
            'data' => array_reverse($model->channelList),
        ]) ?>


        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']); ?>

        <?= Html::a('下载', [
            '',
            'startDate' => $model->startDate,
            'channel'   => $model->channel,
            'download'  => 1,
        ], [
            'class'          => 'btn btn-success',
            'target'         => '_blank',
            'target_browser' => true,
        ]); ?>

        <?php
        ActiveForm::end() ?>
    </div>
</div>
