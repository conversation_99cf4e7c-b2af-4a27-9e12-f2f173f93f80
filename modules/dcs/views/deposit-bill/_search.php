<?php

use common\models\DepositBill;
use common\models\DepositBillSearch;
use kartik\widgets\DateTimePicker;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model DepositBillSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <i class="fa fa-search"></i>
        <h3 class="box-title">搜索</h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php
        $form = ActiveForm::begin([
            'action'        => ['index'],
            'method'        => 'get',
            'type'          => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'startDate')->widget(DateTimePicker::class, [
            'options'       => [
                'placeholder' => '开始日期',
            ],
            'type'          => DateTimePicker::TYPE_INPUT,
            'pluginOptions' => [
                'minView'        => 'month',
                'todayBtn'       => true,
                'todayHighlight' => true,
                'autoclose'      => true,
                'format'         => 'yyyy-mm-dd',
            ],
        ]) ?>

        <?= $form->field($model, 'endDate')->widget(DateTimePicker::class, [
            'options'       => [
                'placeholder' => '结束日期',
            ],
            'type'          => DateTimePicker::TYPE_INPUT,
            'pluginOptions' => [
                'minView'        => 'month',
                'todayBtn'       => true,
                'todayHighlight' => true,
                'autoclose'      => true,
                'format'         => 'yyyy-mm-dd',
            ],
        ]) ?>

        <?= $form->field($model, 'status', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 100px',
            ],
        ])->widget(Select2::class, [
            'data'          => DepositBill::statusList(),
            'hideSearch'    => true,
            'options'       => [
                'prompt' => $model->getAttributeLabel('status'),
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'channel', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 150px',
            ],
        ])->widget(Select2::class, [
            'data'          => DepositBill::channels(),
            'options'       => [
                'prompt' => $model->getAttributeLabel('channel'),
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'type', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 150px',
            ],
        ])->widget(Select2::class, [
            'data'          => DepositBill::TYPE_LIST,
            'hideSearch'    => true,
            'options'       => [
                'prompt' => $model->getAttributeLabel('type'),
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <div class="form-group">
            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
            <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>
        </div>

        <?php
        ActiveForm::end(); ?>

    </div>
</div>