<?php

/* @var $this yii\web\View */

/* @var $model PeriodicFileRecord */

use cpm\models\CapitalChannel;
use dcs\models\PeriodicFileRecord;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;

$this->title = '补推: ' . $model->periodic_file_record_rule_id;
$this->params['breadcrumbs'][] = ['label' => '定期文件规则', 'url' => ['index']];

$this->params['breadcrumbs'][] = '补推';

?>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">补推</div>
    </div>

    <?php $form = ActiveForm::begin(); ?>

    <div class="box-body">
        <div class="row">
            <div class="col-md-4">
                <?= $form->field($model, 'periodic_file_record_loan_channel')->widget(Select2::class, [
                    'data' => CapitalChannel::list(true),
                    'pluginOptions' => [
                        'disabled' => true
                    ]
                ]) ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($model, 'periodic_file_record_target_channel')->textInput(['readonly' => 'readonly']) ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($model, 'periodic_file_record_file_code')->textInput(['readonly' => 'readonly']) ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'pushStartDate')->widget(DatePicker::class, [
                    'type' => DatePicker::TYPE_INPUT,
                    'pluginOptions' => [
                        'todayBtn' => 'linked',
                        'todayHighlight' => true,
                        'autoclose' => true,
                    ],
                ])->label('开始日期')->hint('<span class="text-danger">指定补推日期为业务日期:business_date</span>',['style' => 'margin-top:5px']) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'pushEndDate')->widget(DatePicker::class, [
                    'type' => DatePicker::TYPE_INPUT,
                    'pluginOptions' => [
                        'todayBtn' => 'linked',
                        'todayHighlight' => true,
                        'autoclose' => true,
                    ],
                ])->label('结束日期')->hint('<span class="text-danger">指定补推日期为业务日期:business_date</span>',['style' => 'margin-top:5px']) ?>
            </div>
        </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('确认', [
            'class' => 'btn btn-success',
        ]) ?>
    </div>
    <?php ActiveForm::end(); ?>

</div>