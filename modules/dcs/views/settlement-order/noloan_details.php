<?php

use capital\models\CleanClearingTrans;
use capital\models\CleanPrechargeClearingTran;
use dcs\models\CleanClearingTransSearch;
use dcs\models\SettlementOrder;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use yii\grid\CheckboxColumn;
use yii\helpers\Html;
use yii\helpers\Url;

/** @var yii\web\View $this */
/** @var CleanClearingTransSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */
$this->title = '结算明细记录';
$this->params['breadcrumbs'][] = $this->title;
?>
    <div class="box box-default search">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa fa-search"></i>
                搜索
            </div>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
                </button>
            </div>
        </div>

        <div class="box-body">
            <div class="row">
                <div style="float: left;">
                    <?php $form = ActiveForm::begin([
                        'action' => ['noloan-details'],
                        'method' => 'get',
                        'type' => ActiveForm::TYPE_INLINE,
                        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
                    ]); ?>

                    <?= $form->field($searchModel, 'asset_item_no') ?>
                    <?= $form->field($searchModel, 'settlement_batch_no')->hiddenInput()->label(false) ?>

                    <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

                    <?php ActiveForm::end(); ?>
                </div>
                <div style="margin-left: 10px">
                    <?php
                    if ($searchModel->settlementOrder->status === SettlementOrder::INIT && $searchModel->settlementOrder->type === SettlementOrder::TYPE_MANUAL) {
                        echo Html::a('结算申请', Url::to(['manual-apply', 'orderNo' => $searchModel->settlement_batch_no]), [
                            'class' => 'btn btn-warning',
                            'data' => [
                                'confirm' => '确定重新发起结算申请吗?',
                                'method' => 'post',
                            ],
                        ]);
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
<?php
echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => CheckboxColumn::class,
            'checkboxOptions' => function (CleanPrechargeClearingTran $model) {
                $flag = $model->settlementOrder->status !== SettlementOrder::INIT;
                return [
                    'disabled' => $flag,
                    'style' => 'display:' . ($flag ? 'none' : 'block'),
                ];
            },
        ],
        [
            'class' => DialogActionColumn::class,
            'header' => $searchModel->settlementOrder->status !== SettlementOrder::SUCCESS ? Html::button('批量移除', [
                'class' => 'btn btn-xs btn-danger batch-remove',
            ]) : null,
            'template' => '{remove-cpc-tran}',
            'buttons' => [
                'remove-cpc-tran' => static function ($url, CleanPrechargeClearingTran $model) {
                    return Html::a('移除', $url, [
                        'class' => 'btn btn-xs btn-primary remove-btn',
                        'data' => [
                            'id' => $model->id,
                            'confirm' => '确定移除吗?',
                            'method' => 'post',
                        ],
                    ]);
                },
            ],
            'visibleButtons' => [
                'remove-cpc-tran' => static function (CleanPrechargeClearingTran $model) {
                    return $model->settlementOrder->status === SettlementOrder::INIT;
                },
            ]
        ],
        [
            'label' => '结算订单号',
            'attribute' => 'settlement_batch_no',
        ],
        [
            'label' => '资产编号',
            'attribute' => 'asset_item_no'
        ],
        [
            'label' => '费用类型',
            'attribute' => 'amount_type'
        ],
        'period',
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'amount',
        ],
        [
            'label' => '还款类型',
            'attribute' => 'trans_type'
        ],
        'transfer_out',
        'transfer_out_channel_code',
        'transfer_in',
        'transfer_in_channel_code',
        [
            'label' => '状态',
            'attribute' => 'status',
            'format' => ['in', CleanClearingTrans::STATUS_LIST]
        ],
        'expect_settlement_at',
        'settlement_at',
        'create_at',
    ],
]);
$resetUrl = Url::to(['batch-remove-cpc-tran']);
$batchNoEl = Html::getInputId($searchModel, 'settlement_batch_no');
$js = <<<JS
    const batchNoEl = $('#$batchNoEl');
    $(document)
        .on('click', 'button.remove-btn', function (e) {
            const id = e.target.getAttribute("data-id")
            resetRequest([id])
        })
        .on('click', 'button.batch-remove', function () {
            const ids = $('[name=\'selection[]\']:checked').map(function () { 
                return $(this).val(); 
            }).get();
            
            if (!ids.length) {
                layer.alert('请先勾选需要修改的数据!');
            } else {
                resetRequest(ids)
            }
        });

    function resetRequest(ids) {
        layer.confirm('确认移除?', {
            title: '重推',
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.post('{$resetUrl}', { ids: ids.join(','),batchNo: batchNoEl.val() }, function (data, status) {
                if (data.code === 0) {
                    layer.msg(data.message);
                     setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                } else {
                    layer.alert(data.message);
                }
            }).fail(function () {
               layer.alert('网络请求错误,请稍候再试!');
          });
        });
    }
JS;

$this->registerJs($js);

