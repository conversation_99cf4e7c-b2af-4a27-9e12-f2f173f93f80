<?php

use dcs\models\SettlementOrder;
use dcs\models\SettlementOrderSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model SettlementOrderSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>
        <?= $form->field($model, 'startDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建开始时间',
                'autocomplete' => 'off',
            ],
        ]) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建结束时间',
                'autocomplete' => 'off',
            ],
        ]) ?>
        <?= $form->field($model, 'order_no') ?>
        <?= $form->field($model, 'status', ['options' => [
            'style' => 'min-width: 200px',
        ]])->widget(Select2::class, [
            'data' => SettlementOrder::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '订单状态',
            ]
        ]) ?>
        <?= $form->field($model, 'loan_type', ['options' => [
            'style' => 'min-width: 150px',
        ]])->widget(Select2::class, [
            'data' => SettlementOrder::LOAN_TYPE_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '资产类型',
            ]
        ]) ?>

        <?= $form->field($model, 'loan_channel') ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>
