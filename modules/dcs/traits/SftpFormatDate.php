<?php

namespace dcs\traits;

use Carbon\Carbon;

trait SftpFormatDate
{
    public function parse(string $template, array $data)
    {
        //匹配  {{xxx}}
        return preg_replace_callback('/{{(.+?)}}/', function ($matches) use ($data) {
            $expression = $matches[1];
            //'$var.function(args)'
            preg_match('/(\S+)\.(.+)\(([^\/)]*)/', $expression, $matches);
            $param = $matches[1];
            $method = $matches[2];
            $args = array_map('trim', explode(',', $matches[3]));
            foreach ($args as &$arg) {
                if (preg_match('/^\[.*\/]$/', $arg)) {
                    $arg = json_decode($arg);
                } elseif (preg_match('/^["\'](.*?)["\']$/', $arg, $matches)) {
                    $arg = $matches[1];
                } elseif (is_numeric($arg)) {
                    $arg = +$arg;
                } elseif ($arg === 'true') {
                    $arg = true;
                } elseif ($arg === 'false') {
                    $arg = false;
                }
            }

            if (!isset($data[$param])) {
                return '';
            }

            $val = $data[$param];

            return $this->{$method}($val, ...$args) ?? '';

        }, $template);

    }

    public function dateFormat(string $date, string $format = 'Y-m-d'): string
    {
        return Carbon::parse($date)->format($format);
    }
}