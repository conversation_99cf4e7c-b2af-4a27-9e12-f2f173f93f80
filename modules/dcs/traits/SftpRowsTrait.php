<?php

namespace dcs\traits;

use yii\helpers\ArrayHelper;

trait SftpRowsTrait
{
    /**
     * @param                                $file     resource
     * @param                                $assets   array<string:array<int,array>>
     * @param                                $header   array<int,string>
     * @param                                $fileInfo array{date:string}
     *
     *
     * @return void
     */
    public function mergeRows($file, array $assets, array $header, array $fileInfo): void
    {
        if ($this->appendBuybackRepayDate) {
            $header[] = 'buyback_date';
        }

        $data[] = $header;

        foreach ($assets as $itemNo => $lines) {
            if ($this->periodColumn !== null) {
                ArrayHelper::multisort($lines, $this->periodColumn);
            }

            $asset = $lines[0] ?? current($lines);
            foreach ($this->sumColumnMap as $map) {
                $asset[$map] = array_sum(array_column($lines, $map)) ?? 0;
            }
            //追加代偿回购日期
            if ($this->appendBuybackRepayDate) {
                //历史资方通过匹配目录下载文件待迁移偿付流程
                if (isset($fileInfo['matches']['date'])) {
                    $date = $fileInfo['matches']['date'];
                    //新资方直接下载文件且部分迁移了偿付流程
                } elseif (isset($fileInfo['date'])) {
                    $date = $fileInfo['date'];
                }
                $asset['buyback_date'] = $date ?? '';
            }
            $data[] = $asset;
        }
        foreach ($data as $row) {
            fputcsv($file, $row, ',');
        }
    }
}