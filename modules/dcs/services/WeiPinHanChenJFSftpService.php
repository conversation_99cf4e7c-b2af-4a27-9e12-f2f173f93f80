<?php

namespace dcs\services;

class WeiPinHanChenJFSftpService extends LjCapitalSftpService
{
    public function generateNewName(array $fileInfo): string
    {
        $pathToArray = explode('/', trim($fileInfo['path'], '/'));
        $date = end($pathToArray);
        return preg_replace_callback('/{([^}]+)}/', function ($match) use ($fileInfo, $date) {
            $baseName = $fileInfo['matches'][$match[1]] ?? '';
            return $baseName . '-' . $date;
        }, $this->newName);
    }
}