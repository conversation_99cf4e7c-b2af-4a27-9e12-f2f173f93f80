<?php

namespace dcs\services;

use dcs\traits\SftpFileTrait;
use dcs\traits\SftpRowsTrait;
use yii\base\BaseObject;
use yii\base\UserException;

class FileService extends BaseObject
{
    use SftpRowsTrait;
    use SftpFileTrait;

    public bool $appendBuybackRepayDate = false;
    public ?string $pregSplitPattern = '/\s*\r?\n\s*/';

    public ?string $appendTitle = null;
    /**
     * 期次的字段
     *
     * @var string|null
     */
    public ?string $periodColumn = null;
    /**
     * @var string|null|array
     */
    public $itemNoColumn = null;
    /**
     * 列数据分割线(",","|","||")
     *
     * @var string|null
     */
    public string $rowSeparator = '|';
    /**
     * 是否是json文件格式
     *
     * @var bool
     */
    public bool $jsonFile = false;

    /**
     * 是否是csv文件需要用csv组件解析
     * @var bool
     */
    public bool $csvFile = false;

    /**
     * 是否需要行转列
     * @var bool
     */
    public bool $rowToColumn = false;

    /**
     * 行列配置信息
     *
     * @var array <map:array,index:array>
     */
    public array $rowColumnConfig = [];
    /**
     * @var array
     */
    public array $sumColumnMap = [];

    public ?array $assets = null;

    public ?array $header = null;

    public ?string $fileName = null;

    /**
     * @throws UserException
     */
    public function parseFile(string $content): FileService
    {
        [$assets, $header] = $this->parseData($content);
        $this->assets = $assets;
        $this->header = $header;
        return $this;
    }

    public function rows($file, $date): FileService
    {
        $this->mergeRows($file, $this->getAssets(), $this->getHeader(), ['date' => $date]);
        $fileMetaData = stream_get_meta_data($file);
        $this->fileName = $fileMetaData['uri'];
        return $this;
    }

    public function getAssets(): ?array
    {
        return $this->assets;
    }

    public function getHeader(): ?array
    {
        return $this->header;
    }

    public function getFileName(): ?string
    {
        return $this->fileName;
    }

}