<?php

namespace dcs\services;

use Carbon\Carbon;
use dcs\components\GatewayComponent;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Psr\Http\Message\ResponseInterface;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\di\Instance;
use yii\helpers\Json;

/**
 * Class CapitalFileApiService
 *
 * @package dcs\services
 */
class CapitalFileApiService
{
    protected GatewayComponent $sftpClient;

    /**
     * @var array
     */
    public $config;
    /**
     * @var string
     */
    public $channel;

    /**
     * @var string
     */
    public $business;

    /**
     * @var string
     */
    public $pattern;

    /**
     * @var string
     */
    public $path;


    public string $appendTitle = '';
    public string $dateFormat = 'Y-m-d';
    protected array $params;

    public string $nameDate = '';

    public function init(): void
    {
        $this->sftpClient = GatewayComponent::instance();
    }

    /**
     * @param string $channel
     * @param string $business
     * @param array  $params
     *
     * @return CapitalFileApiService
     * @throws UserException
     * @throws KVException
     * @throws InvalidConfigException
     */
    public static function create(string $channel, string $business, array $params = []): self
    {
        $baseConfig = KeyValue::takeAsArray('sftp_preg_config');
        $config = (array)($baseConfig[$channel][$business] ?? []);
        if (empty($config)) {
            throw new UserException(
                vsprintf("缺少配置: %s -> %s\n%s", [
                    $channel,
                    $business,
                    Json::encode($baseConfig),
                ])
            );
        }
        /** @var CapitalFileApiService $service */
        $service = Instance::ensure(Yii::createObject($config));

        $service->channel = $channel;
        $service->business = $business;
        $service->params = $params;
        $service->sftpClient = GatewayComponent::instance();

        return $service;
    }

    /**
     * @return resource
     * @throws UserException
     */
    public function download()
    {
        $response = $this->request();
        $file = $this->createTmp();

        if (!empty($this->appendTitle)) {
            fwrite($file, $this->appendTitle);
        }

        fwrite($file, (string)$response->getBody());

        return $file;
    }

    /**
     * @return array
     */
    public function makeRequestParams(): array
    {
        $routeParams = [];
        foreach ($this->routeParams ?? [] as $param => $value) {
            if (stristr($param, 'date')) {
                $startDate = Carbon::parse($this->params[0] ?? $value)->setTime(0, 0);
                $value = $startDate->format($this->dateFormat);
                $this->nameDate = $value;
            }
            $routeParams[$param] = $value;
        }

        return $routeParams;
    }

    /**
     * @return resource
     * @throws UserException
     */
    protected function createTmp()
    {
        $file = tmpfile();
        if (!$file) {
            throw new UserException('创建临时文件失败');
        }

        return $file;
    }

    /**
     * @return ResponseInterface
     * @throws UserException
     */
    protected function request(): ResponseInterface
    {
        $response = $this->sftpClient->fileApiGet($this->route ?? '', $this->makeRequestParams());
        if ($response->getStatusCode() !== GatewayComponent::GUZZLE_HTTP_STATUS_OK) {
            throw new UserException(
                vsprintf('请求文件失败1: %d %s', [
                    $response->getStatusCode(),
                    $response->getBody(),
                ])
            );
        }

        return $response;
    }
}
