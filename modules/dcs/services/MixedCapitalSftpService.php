<?php

namespace dcs\services;

use dcs\traits\SftpFileTrait;
use dcs\traits\SftpFormatDate;
use dcs\traits\SftpPathTrait;
use dcs\traits\SftpRowsTrait;

/**
 * @MixedCapitalSftpService
 */
class MixedCapitalSftpService extends CapitalSftpService
{
    use SftpPathTrait;
    use SftpRowsTrait;
    use SftpFileTrait;
    use SftpFormatDate;

    public ?string $checkExtension = null;

    /**
     * 是否需要行转列
     * @var bool
     */
    public bool $rowToColumn = false;

    public bool $appendBuybackRepayDate = false;

    public ?string $pregSplitPattern = '/\s*\r?\n\s*/';

    public string $startDate = '3 days ago';

    public string $endDate = 'today';

    public ?string $pathDateFormat = null;

    /**
     * 期次的字段
     *
     * @var string|null
     */
    public ?string $periodColumn = null;

    /**
     * 主要兼容金美信回购和代偿的数据是整合在一起的
     * bill_no|term_no|compen_prin|compen_int|compen_status|fund_partner_cd|composite_rate_tag|compensatory_type
     * PLCN2022092730652980AG6LLD841428|7|335.64|0.0|compen|JMX|IRR24|buyback
     * PLCN2022092730652980AG6LLD841428|8|341.51|0.0|compen|JMX|IRR24|buyback
     * PLCN2022092730652980AG6LLD841428|6|329.87|0.0|compen|JMX|IRR24|buyback
     * PLCN2022092730652980AG6LLD841428|5|324.19|48.27|compen|JMX|IRR24|compensatory
     * 第5期是代偿 第6期是回购["bill_no","compensatory_type"]
     * 资产编号索引
     *
     * @var string|null|array
     */
    public $itemNoColumn = null;


    /**
     * 列数据分割线(",","|","||")
     *
     * @var string|null
     */
    public string $rowSeparator = '|';
    /**
     * 是否是json文件格式
     *
     * @var bool
     */
    public bool $jsonFile = false;

    /**
     * 是否是csv文件需要用csv组件解析
     * @var bool
     */
    public bool $csvFile = false;

    /**
     * [
     * "应还总额",
     * "应还本金",
     * "应收利息",
     * "实还本金",
     * "实还利息",
     * "回购本金",
     * "回购利息",
     * "回购总额"
     * ]
     * @var array
     */
    public array $sumColumnMap = [];

    /**
     * @var bool
     */
    public bool $enableProcess = true;


    public function download(string $channel, array $fileInfo)
    {
        if (!$this->enableProcess) {
            return parent::download($channel, $fileInfo);
        }

        $this->httpRequest($channel, $fileInfo);

        //创建临时文件
        $file = $this->createTmpFile();

        [$assets, $header] = $this->parseData((string)$this->httpResponse->getBody());

        $this->mergeRows($file, $assets, $header, $fileInfo);

        return $file;
    }

}