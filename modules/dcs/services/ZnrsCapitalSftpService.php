<?php

namespace dcs\services;

use Carbon\Carbon;
use dcs\components\GateComponent;
use Generator;
use yii\base\UserException;
use yii\helpers\ArrayHelper;

class ZnrsCapitalSftpService extends CapitalSftpService
{
    public $checkExtension;
    public string $startDate = '3 days ago';
    public string $endDate = 'today';
    public string $dateFormat = 'Ymd';

    protected array $buybackAsset = [];

    public function pathList(): Generator
    {
        /** @noinspection DuplicatedCode */
        $startDate = Carbon::parse($this->params[0] ?? $this->startDate)->setTime(0, 0);
        $endDate = Carbon::parse($this->params[1] ?? $this->endDate)->setTime(0, 0);
        while ($startDate <= $endDate) {
            $date = $startDate->format($this->dateFormat);
            yield sprintf($this->path, $date);
            $startDate->addDay();
        }
    }

    public function download(string $channel, array $fileInfo)
    {
        /** @noinspection DuplicatedCode */
        $response = $this->sftpClient->sftpFileDownload($channel, $fileInfo['path'], $fileInfo['name']);
        if ($response->getStatusCode() !== GateComponent::GUZZLE_HTTP_STATUS_OK) {
            throw new UserException(
                vsprintf('下载文件失败: %s %s', [
                    $response->getStatusCode(),
                    $response->getReasonPhrase(),
                ])
            );
        }
        $file = tmpfile();

        if (!$file) {
            throw new UserException('创建临时文件失败');
        }


        preg_match($this->pattern, $fileInfo['name'], $matches);
        $date = $matches['date'] ?? '';
        $this->appendBuybackDate($file, (string)$response->getBody(), $date);

        return $file;
    }


    /**
     * @throws UserException
     */
    protected function appendBuybackDate($file, $content, $date): void
    {
        if (!empty($this->appendTitle)) {
            $headerArray = explode('|', $this->appendTitle);
        } else {
            throw new UserException('资方:[zhenong_rongsheng],sftp未配置标题栏!');
        }
        $rows = preg_split('/\s*\r?\n\s*/', trim($content));
        $data = [];
        $data[] = $headerArray;
        foreach ($rows as $key => $row) {
            $rowToArray = (explode('|', $row));
            $type = (int)($rowToArray[1] ?? 0);
            $itemNo = $rowToArray[0] ?? '';
            if (in_array($type, [2, 3, 4, 6, 99], true)) {
                $rowToArray[] = $date;
                $rowToArray = array_combine($headerArray, $rowToArray);
                $this->generateBuybackAsset($itemNo, $rowToArray);
            }
        }

        foreach ($this->buybackAsset as $assets) {
            $asset = $assets[0] ?? current($assets);
            ArrayHelper::multisort($assets, 'item_no');
            $asset['item_no'] = $assets[0]['item_no'];
            $asset['buyback_principal'] = array_sum(array_column($assets, 'buyback_principal')) ?? 0;
            $asset['buyback_interest'] = array_sum(array_column($assets, 'buyback_interest')) ?? 0;
            $data[] = $asset;
        }

        foreach ($data as $row) {
            fputcsv($file, $row, '|');
        }
    }

    /**
     * @param string $itemNo
     * @param array $asset
     *
     * @return void
     */
    protected function generateBuybackAsset(string $itemNo, array $asset): void
    {
        $this->buybackAsset[$itemNo][] = $asset;
    }

}