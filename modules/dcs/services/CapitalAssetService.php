<?php

namespace dcs\services;

use Carbon\Carbon;
use common\consts\Consts;
use common\models\Asset;
use common\models\CapitalAsset;
use common\models\CapitalTransaction;
use Exception;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Throwable;
use yii\base\UserException;
use yii\helpers\Json;

/**
 * Class CapitalAssetService
 *
 * @package dcs\services
 */
class CapitalAssetService
{
    /**
     * 还款前，将资产分为 normal,advance,overdue,void。如果是走资方通道，则将状态设置为finish.
     * 如果需要 纠正 就走纠正数据入库。
     *
     * @param string $itemNo
     * @param string $loanChannel
     * @param string $withholdAt
     * @param string $withholdChannel
     * @param int    $transId
     * @param int    $withholdAmount 代扣金额
     *
     * @throws KVException
     * @throws UserException
     * @throws Throwable
     */
    public static function updateTrans(
        string $itemNo,
        string $loanChannel,
        string $withholdAt,
        string $withholdChannel,
        int $transId,
        int $withholdAmount = 0
    ): void {
        $withholdDate = Carbon::parse($withholdAt);

        /** @var CapitalAsset $cAsset */
        $cAsset = CapitalAsset::findOne([
            'capital_asset_item_no' => $itemNo,
            'capital_asset_channel' => $loanChannel,
        ]);

        /** @var CapitalTransaction $cTran */
        $cTran = CapitalTransaction::findOne([
            'capital_transaction_id' => $transId,
        ]);

        $cTran->capital_transaction_user_repay_at = $withholdAt;
        $cTran->capital_transaction_withhold_result_channel = $withholdChannel;

        $config = KeyValue::takeAsArray('biz_capital_asset');
        if (!isset($config['open_void_channel'])) {
            throw new Exception('缺少刷新 capital_transaction_open_void_channel 配置');
        }
        $openVoidChannel = (array)$config['open_void_channel'];

        $expectFinishedAt = Carbon::parse($cTran->capital_transaction_expect_finished_at)->toDateString();

        // 资方取消标志已经开启,且为取消资产
        if (
            $cAsset->asset->asset_status === Asset::STATUS_WRITE_OFF &&
            in_array($cAsset->capital_asset_channel, $openVoidChannel, true)
        ) {
            $cTran->capital_transaction_operation_type = CapitalTransaction::OPERATION_TYPE_VOID;
        } elseif ($expectFinishedAt == $withholdDate) {
            $cTran->capital_transaction_operation_type = CapitalTransaction::OPERATION_TYPE_NORMAL;
        } elseif ($expectFinishedAt < $withholdDate) {
            $cTran->capital_transaction_operation_type = CapitalTransaction::OPERATION_TYPE_OVERDUE;
        } else {
            $cTran->capital_transaction_operation_type = CapitalTransaction::OPERATION_TYPE_ADVANCE;
            $cTran->capital_transaction_advance_at = $withholdDate;
            $cTran->capital_transaction_is_advance = Consts::YES;
            $cTran->capital_transaction_repaid_amount = (int)$withholdAmount;
        }

        if ($withholdChannel !== CapitalTransaction::WITHHOLD_CHANNEL_QSQ) {
            $cTran->capital_transaction_repaid_amount = (int)$withholdAmount;
            $cTran->capital_transaction_status = CapitalTransaction::STATUS_FINISHED;
        }

        //TODO 解决秦农报表的在贷实际结算时间
        $cTran->capital_transaction_actual_operate_at = self::makeActualOperateAt($cTran, $withholdAt);

        if (!$cTran->save()) {
            if ($cTran->hasErrors()) {
                throw new UserException(Json::encode($cTran->getErrors()));
            }
            throw new UserException('更新capitalTransaction出错.');
        }
    }

    /**
     * 解决秦农报表的在贷实际结算时间
     *
     * @param CapitalTransaction $capitalTransaction
     * @param string             $withholdAt
     *
     * @return string
     * @throws Exception
     */
    protected static function makeActualOperateAt(CapitalTransaction $capitalTransaction, string $withholdAt): string
    {
        $config = KeyValue::takeAsArray('biz_refresh_capital_tran_config');
        $channels = $config['actual_operate_special_channel'];

        if (empty($channels)) {
            throw new Exception('缺少刷新 actual_operate_special_channel 配置');
        }

        return (($capitalTransaction->capital_transaction_operation_type === CapitalTransaction::OPERATION_TYPE_ADVANCE)
            && ($capitalTransaction->capital_transaction_withhold_result_channel === Consts::WITHHOLD_QSQ)
            && (in_array($capitalTransaction->capital_transaction_channel, $channels, true))) ?
            $capitalTransaction->capital_transaction_expect_finished_at : $withholdAt;
    }
}
