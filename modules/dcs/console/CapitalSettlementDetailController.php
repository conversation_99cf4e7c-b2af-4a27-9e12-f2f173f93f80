<?php

namespace dcs\console;

use Carbon\Carbon;
use dcs\models\CapitalSettlementDetail;
use dcs\tasks\BuybackOverdueDaysSave;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Throwable;
use yii\console\Controller;
use yii\console\ExitCode;

class CapitalSettlementDetailController extends Controller
{
    /**
     * 将需要回购的数据刷到buyback表, 并通知还款
     *
     * @param $startDate
     * @param $endDate
     *
     * @return int
     * @throws Throwable
     * @throws KVException
     */
    public function actionToBuyback($startDate = null, $endDate = null): int
    {
        $startDate = Carbon::parse($startDate ?? '10 days ago')->toDateString();
        $endDate = Carbon::parse($endDate ?? 'today')->addDay()->toDateString();

        $toBuybackConfig = KeyValue::take('capital_settlement_detail_to_buyback');

        foreach ($toBuybackConfig as $channel => $toBuybackTypes) {
            $query = CapitalSettlementDetail::find()
                ->alias('csd')
                ->joinWith('buyback b', false)
                ->innerJoinWith('asset')
                ->where([
                    'csd.channel' => $channel,
                    'csd.type' => $toBuybackTypes,
                    'b.buyback_asset_item_no' => null,
                ])
                ->andWhere([
                    'and',
                    ['>=', 'csd.repay_date', $startDate],
                    ['<', 'csd.repay_date', $endDate],
                ]);

            echo $query->createCommand()->rawSql.PHP_EOL.PHP_EOL;

            /** @var CapitalSettlementDetail $one */
            foreach ($query->each() as $one) {
                BuybackOverdueDaysSave::make([
                    'asset_item_no' => $one['asset_item_no'],
                    'period_count' => $one->asset->asset_period_count,
                    'granted_principal_amount' => $one['asset_granted_principal_amount'],
                    'start_period' => $one['period'],
                    'buyback_total_principal_amount' => $one['repay_principal'],
                    'buyback_total_interest_amount' => $one['repay_interest'],
                    'buyback_start_date' => $one['repay_date'],
                    'buyback_category' => $one->type,
                    'channel' => $one['channel'],
                ]);
            }
        }

        return ExitCode::OK;
    }
}
