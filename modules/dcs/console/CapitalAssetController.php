<?php

namespace dcs\console;

use Carbon\Carbon;
use common\consts\Consts;
use common\models\Asset;
use common\models\CapitalAsset;
use common\models\CapitalTransaction;
use common\models\CleanFinal;
use common\models\Provision;
use common\models\WithholdResult;
use dcs\models\Buyback;
use dcs\services\CapitalAssetService;
use Exception;
use kvmanager\models\KeyValue;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\console\Controller;
use yii\console\ExitCode;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Console;
use yii\helpers\Json;

/**
 * 刷新资方还款计划
 * Class CapitalAssetController
 *
 * @package dcs\console
 */
class CapitalAssetController extends Controller
{
    /**
     * 刷新资方还款计划
     *
     * @param int         $rollback
     * @param string      $channel
     * @param string|null $startDate
     * @param string|null $endDate
     *
     * @return int
     * @throws Exception
     */
    public function actionRefreshCapitalTran($rollback = 0, $channel = '', $startDate = null, $endDate = null): int
    {
        $config = self::genConfig($channel);

        if ($rollback) {
            $rollback = (array)ArrayHelper::getValue($config, 'rollback');
            $this->process($startDate, $endDate, $rollback, [$this, 'rollback']);
        }

        // 刷新 credit
        $syncToCapitalTran = (array)ArrayHelper::getValue($config, 'sync_to_capital_tran');
        $this->process($startDate, $endDate, $syncToCapitalTran, [$this, 'syncToCapitalTran']);

        // buyback 数据刷新
        $endTime = $endDate ?? Carbon::now()->toDateTimeString();
        $buybackDcs = (array)ArrayHelper::getValue($config, 'buyback_dcs');
        $this->process($startDate, $endTime, $buybackDcs, [$this, 'buybackDcs']);

        // 资方代扣
        $capitalWithholdChannel = (array)ArrayHelper::getValue($config, 'capital_withhold');
        $this->process($startDate, $endTime, $capitalWithholdChannel, [$this, 'capitalWithhold']);

        // 我方代扣 真金服
        $dcs = (array)ArrayHelper::getValue($config, 'dcs');
        $this->process($startDate, $endDate, $dcs, [$this, 'qsqWithholdDcs']);

        // 提前结清逻辑 首金
        $advanceSettleReduce = (array)ArrayHelper::getValue($config, 'advance_settle_reduce');
        $this->process($startDate, $endDate, $advanceSettleReduce, [$this, 'advanceSettleReduce']);

        // DCS 提前结清数据
        $earlySettlement = (array)ArrayHelper::getValue($config, 'early_settlement');
        $this->process($startDate, $endTime, $earlySettlement, [$this, 'earlySettlementDcs']);

        // 我方代扣
        $qsqWithholdChannel = (array)ArrayHelper::getValue($config, 'qsq_withhold');
        $this->process($startDate, $endDate, $qsqWithholdChannel, [$this, 'qsqWithhold']);

        // 逾期
        $overdue = (array)ArrayHelper::getValue($config, 'overdue');
        $this->process($startDate, $endDate, $overdue, [$this, 'overdue']);

        // buyback 数据刷新
        $endTime = $endDate ?? Carbon::now()->toDateTimeString();
        $buybackDcs = (array)ArrayHelper::getValue($config, 'buyback_biz');
        $this->process($startDate, $endTime, $buybackDcs, [$this, 'buybackBiz']);

        // 刷新 credit amount
        $this->process($startDate, $endDate, $syncToCapitalTran, [$this, 'syncAmountToCapitalTran']);

        //刷新DCS担保费
        $endTime = $endDate ?? Carbon::now()->toDateTimeString();
        $guaranteeDcs = (array)ArrayHelper::getValue($config, 'guarantee_dcs');
        $this->process($startDate, $endTime, $guaranteeDcs, [$this, 'syncGuaranteeDcs']);

        return ExitCode::OK;
    }

    /**
     * DCS clean_capital_settlement_pending 读取担保费金额
     *
     * @param $startDate
     * @param $endDate
     * @param $channel
     *
     * @throws InvalidConfigException
     * @throws Exception
     */
    protected function syncGuaranteeDcs($startDate, $endDate, $channel): void
    {
        $db = instanceEnsureDb('dbCapital');
        $sql = <<<EOF
SELECT asset_item_no, asset_loan_channel, period, repay_type, settlement_amount
FROM clean_capital_settlement_pending
WHERE (asset_item_no, period, create_at, amount_type) IN
      (SELECT asset_item_no, period, MAX(create_at) create_at, amount_type
       FROM clean_capital_settlement_pending
       WHERE asset_loan_channel = :channel
         AND amount_type = 'guarantee'
         AND expect_finish_at >= :startDate
         AND expect_finish_at < :endDate
         AND status = 'success'
       GROUP BY asset_item_no, period);
EOF;
        $command = $db->createCommand($sql, [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'channel' => $channel,
        ]);

        $data = $command->queryAll();
        $this->stdout(vsprintf("%s: %s\n共%d条\n", [
            'DCS担保费刷新',
            $command->getRawSql(),
            count($data),
        ]), Console::FG_GREEN);


        foreach ($data as $row) {
            $data = [
                'capital_transaction_repaid_amount' => $row['settlement_amount'],
            ];
            CapitalTransaction::updateAll($data, [
                'capital_transaction_channel' => $channel,
                'capital_transaction_period' => $row['period'],
                'capital_transaction_type' => 'guarantee',
                'capital_transaction_asset_item_no' => $row['asset_item_no'],
            ]);
        }
    }

    /**
     * @param string|null $channel
     *
     * @return array
     * @throws Exception
     */
    public static function genConfig(?string $channel = null): array
    {
        $configFormKV = KeyValue::takeAsArray('biz_refresh_capital_tran_config');
        $config = [];

        $channelList = Asset::channelList();

        foreach (
            [
                'rollback' => true,
                'capital_withhold' => false,
                'qsq_withhold' => false,
                'overdue' => false,
                'dcs' => false,
                'advance_settle_reduce' => false,
                'sync_to_capital_tran' => false,
                'buyback_dcs' => false,
                'buyback_biz' => false,
                'early_settlement' => false,
                'guarantee_dcs' => false,
            ] as $item => $flag
        ) {
            $config_ = (array)($configFormKV[$item] ?? []) + ($flag ? $channelList : []);

            if ($channel) {
                if (array_key_exists($channel, $config_)) {
                    $config[$item] = [$channel => $config_[$channel]];
                } else {
                    $config[$item] = [];
                }
            } else {
                $config[$item] = $config_;
            }
        }

        return $config;
    }

    /**
     * @param string   $startDate
     * @param string   $endDate
     * @param array    $config
     * @param callable $func
     *
     * @throws Exception
     */
    protected function process($startDate, $endDate, array $config, callable $func): void
    {
        foreach ($config as $channel => $beforeDayNum) {
            $startDate_ = $startDate;
            if ($beforeDayNum === false) {
                continue;
            }

            if (empty($startDate_)) {
                $startDate_ = Carbon::yesterday()->toDateString();
                $beforeDayNum = (int)$beforeDayNum;

                if ($beforeDayNum) {
                    $startDate_ = Carbon::parse($startDate_)->subDays($beforeDayNum)->toDateString();
                }
            }

            $endDate_ = $endDate ?? Carbon::yesterday()->toDateString();

            $this->dailyLoop($startDate_, $endDate_, $func, $channel);
        }
    }

    protected function dailyLoop($startDate, $endDate, callable $func, ...$params): void
    {
        $startDate = Carbon::parse($startDate)->toDateString();
        while ($startDate <= $endDate) {
            $this->stdout(sprintf("[%s] %s\n", $startDate, Json::encode($params)), Console::FG_CYAN);

            $nextDate = Carbon::parse($startDate)->addDay()->toDateString();

            call_user_func($func, $startDate, $nextDate, ...$params);

            $startDate = $nextDate;
        }
    }

    /**
     * 提前结清逻辑
     *
     * @param $startDate
     * @param $endDate
     * @param $channel
     *
     * @throws Exception
     */
    protected function advanceSettleReduce($startDate, $endDate, $channel): void
    {
        $sql = <<<SQL
SELECT `clean_settle_reduce`.`asset_item_no`
     , 'qsq'                                                                 as withhold_channel
     , `clean_settle_reduce`.`push_at`                                       as finishAt
     , `capital_transaction`.`capital_transaction_period`                    as period
     , capital_asset.capital_asset_channel                                   as loanChannel
     , `capital_transaction_type`                                            as type
     , `capital_transaction`.`capital_transaction_id`
     , if(capital_transaction.capital_transaction_type = `clean_settle_reduce`.`type`,
          `clean_settle_reduce`.withhold_amount, capital_transaction_amount) as withhold_amount
from clean_settle_reduce
         inner join capital_asset on clean_settle_reduce.asset_item_no = capital_asset.capital_asset_item_no
         inner join capital_transaction
                    on capital_asset.capital_asset_id = capital_transaction.capital_transaction_asset_id
                        and capital_transaction.capital_transaction_period = clean_settle_reduce.asset_period
where `clean_settle_reduce`.`push_at` >= :startDate
  and `clean_settle_reduce`.`push_at` < :endDate
  and `clean_settle_reduce`.`type` = 'interest'
  and capital_asset.capital_asset_channel = :channel
group by `capital_transaction`.`capital_transaction_id`;
SQL;

        $this->executeWithholdData('首金提前结清', $sql, [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'channel' => $channel,
        ]);
    }

    /**
     * 根据清分数据刷新资方还款计划 真金服
     *
     * @param $startDate
     * @param $endDate
     * @param $channel
     *
     * @return void
     * @throws InvalidConfigException
     */
    protected function qsqWithholdDcs($startDate, $endDate, $channel)
    {
        $startDate = $startDate ?? Carbon::parse('4 days ago')->toDateString();
        $endDate = $endDate ?? Carbon::now()->toDateString();

        $db = instanceEnsureDb('dbCapital');
        $query = (new Query())
            ->from(['t' => 'clean_clearing_trans'])
            ->innerJoin(['f' => 'clean_final'], 'f.final_no = t.final_no')
            ->where([
                'f.asset_loan_channel' => $channel,
                'f.biz_type' => CleanFinal::BIZ_TYPE_REPAY,
                't.transfer_out' => Consts::PROVISION_ACCOUNT,
                't.transfer_in' => new Expression('concat("v_", f.asset_loan_channel)'),
                't.is_need_settlement' => Consts::YES,
            ])
            ->andWhere([
                'and',
                ['>=', 'f.actual_finish_time', $startDate],
                ['<', 'f.actual_finish_time', $endDate],
            ])
            ->groupBy(['f.asset_item_no'])
            ->select([
                'final_no' => new Expression('group_concat(f.final_no SEPARATOR ",")'),
                'operation_type' => 'f.biz_sub_type',
                'f.asset_loan_channel',
                'f.actual_finish_time',
                'f.asset_item_no',
                'f.create_time',
            ]);

        $this->stdout($query->createCommand()->rawSql . PHP_EOL, Console::FG_GREEN);

        $amountTypes = [
            'principal',
            'interest',
            'technical_service',
            'after_loan_manage',
        ];

        foreach ($query->each(100, $db) as $finalInfo) {
            $trans = (new Query())
                ->from(['t' => 'clean_clearing_trans'])
                ->innerJoin(['f' => 'clean_final'], 't.final_no=f.final_no')
                ->where([
                    't.final_no' => explode(',', $finalInfo['final_no']),
                ])
                ->select([
                    't.period',
                    't.amount_type',
                    't.amount',
                    'operation_type' => 'f.biz_sub_type',
                ]);
            $this->stdout($trans->createCommand()->rawSql . PHP_EOL, Console::FG_GREEN);

            $trans = $trans->all($db);
            $trans = ArrayHelper::index($trans, 'amount_type', 'period');

            if (empty($trans)) {
                continue;
            }

            foreach ($trans as $period => $detail) {
                $missingTypes = array_diff($amountTypes, array_keys($detail));
                foreach ($missingTypes as $type) {
                    $detail[] = [
                        'period' => $period,
                        'amount_type' => $type,
                        'amount' => 0,
                        'operation_type' => current($detail)['operation_type'],
                    ];
                }

                foreach ($detail as $row) {
                    $operationType = preg_replace('/_repay$/', '', $row['operation_type']);

                    $data = [
                        'capital_transaction_operation_type' => $operationType,
                        'capital_transaction_repaid_amount' => $row['amount'],
                        'capital_transaction_user_repay_at' => $finalInfo['actual_finish_time'],
                        'capital_transaction_withhold_result_channel' => 'qsq',
                        'capital_transaction_actual_operate_at' => $operationType !== CapitalTransaction::OPERATION_TYPE_NORMAL ?
                            $finalInfo['create_time'] : $finalInfo['actual_finish_time'],
                    ];
                    if ($operationType === CapitalTransaction::OPERATION_TYPE_ADVANCE) {
                        $data['capital_transaction_advance_at'] =
                            Carbon::parse($finalInfo['actual_finish_time'])->toDateString();
                        $data['capital_transaction_is_advance'] = Consts::YES;
                    }
                    CapitalTransaction::updateAll($data, [
                        'capital_transaction_operation_type' => 'grant',
                        'capital_transaction_channel' => $channel,
                        'capital_transaction_period' => $row['period'],
                        'capital_transaction_type' => $row['amount_type'],
                        'capital_transaction_asset_id' => CapitalAsset::find()
                            ->where(['capital_asset_item_no' => $finalInfo['asset_item_no']])
                            ->select('capital_asset_id')
                            ->scalar(),
                    ]);
                }
            }
        }
    }

    /**
     * 根据清清分 buyback 刷新资方还款计划
     *
     * @param $startDate
     * @param $endDate
     * @param $channel
     *
     * @return void
     * @throws InvalidConfigException
     */
    protected function buybackDcs($startDate, $endDate, $channel)
    {
        $db = instanceEnsureDb('dbCapital');
        $query = (new Query())
            ->from('clean_buyback')
            ->where([
                'loan_channel' => $channel,
            ])
            ->andWhere([
                'and',
                ['>=', 'create_at', $startDate],
                ['<', 'create_at', $endDate],
            ])
            ->select([
                'asset_item_no',
                'period',
                'amount_type',
                'amount',
                'expect_compensate_at',
                'loan_channel',
            ]);

        $this->stdout($query->createCommand()->rawSql . PHP_EOL, Console::FG_GREEN);

        foreach ($query->each(100, $db) as $buyback) {
            $data = [
                'capital_transaction_operation_type' => 'buyback',
                'capital_transaction_repaid_amount' => $buyback['amount'],
                'capital_transaction_user_repay_at' => $buyback['expect_compensate_at'],
                'capital_transaction_withhold_result_channel' => 'qsq',
                'capital_transaction_actual_operate_at' => $buyback['expect_compensate_at'],
            ];

            $data['capital_transaction_advance_at'] = Carbon::parse($buyback['expect_compensate_at'])->toDateString();

            CapitalTransaction::updateAll($data, [
                'AND',
                ['<>', 'capital_transaction_operation_type', CapitalTransaction::OPERATION_TYPE_INSPECT],
                ['=', 'capital_transaction_channel', $channel],
                ['=', 'capital_transaction_period', $buyback['period'],],
                ['=', 'capital_transaction_type', $buyback['amount_type'],],
                [
                    '=',
                    'capital_transaction_asset_id',
                    CapitalAsset::find()
                        ->where(['capital_asset_item_no' => $buyback['asset_item_no']])
                        ->select('capital_asset_id'),
                ],
                ['<>', 'capital_transaction_withhold_result_channel', $channel],
            ]);
        }
    }

    /**
     * 根据清清分 buyback 刷新资方还款计划
     *
     * @param $startDate
     * @param $endDate
     * @param $channel
     *
     * @return void
     * @throws InvalidConfigException
     */
    protected function earlySettlementDcs($startDate, $endDate, $channel)
    {
        $db = instanceEnsureDb('dbCapital');
        $query = (new Query())
            ->from('clean_early_settlement')
            ->where([
                'loan_channel' => $channel,
            ])
            ->andWhere([
                'and',
                ['>=', 'create_at', $startDate],
                ['<', 'create_at', $endDate],
            ])
            ->select([
                'asset_item_no',
                'period',
                'amount_type',
                'billable_amount',
                'withheld_amount',
                'loan_channel',
                'user_repay_at',
                'push_at',
            ]);

        $this->stdout($query->createCommand()->rawSql . PHP_EOL, Console::FG_GREEN);

        foreach ($query->each(100, $db) as $earlySettlement) {
            $data = [
                'capital_transaction_operation_type' => 'early_settlement',
                'capital_transaction_repaid_amount' => $earlySettlement['withheld_amount'],
                'capital_transaction_amount' => $earlySettlement['billable_amount'],
                'capital_transaction_user_repay_at' => $earlySettlement['user_repay_at'],
                'capital_transaction_actual_operate_at' => $earlySettlement['push_at'],
                'capital_transaction_is_advance' => Consts::YES,
            ];

            $data['capital_transaction_advance_at'] = Carbon::parse($earlySettlement['user_repay_at'])->toDateString();

            // 相关费用的更新
            CapitalTransaction::updateAll($data, [
                'AND',
                ['<>', 'capital_transaction_operation_type', CapitalTransaction::OPERATION_TYPE_INSPECT],
                ['=', 'capital_transaction_channel', $channel],
                ['=', 'capital_transaction_period', $earlySettlement['period'],],
                ['=', 'capital_transaction_type', $earlySettlement['amount_type'],],
                [
                    '=',
                    'capital_transaction_asset_id',
                    CapitalAsset::find()
                        ->where(['capital_asset_item_no' => $earlySettlement['asset_item_no']])
                        ->select('capital_asset_id'),
                ],
            ]);

            $data = [
                'capital_transaction_operation_type' => 'early_settlement',
                'capital_transaction_user_repay_at' => $earlySettlement['user_repay_at'],
                'capital_transaction_actual_operate_at' => $earlySettlement['push_at'],
                'capital_transaction_is_advance' => Consts::YES,
            ];

            // 本金费用的更新
            CapitalTransaction::updateAll($data, [
                'AND',
                ['<>', 'capital_transaction_operation_type', CapitalTransaction::OPERATION_TYPE_INSPECT],
                ['=', 'capital_transaction_channel', $channel],
                ['=', 'capital_transaction_period', $earlySettlement['period'],],
                ['=', 'capital_transaction_type', CapitalTransaction::TYPE_PRINCIPAL,],
                [
                    '=',
                    'capital_transaction_asset_id',
                    CapitalAsset::find()
                        ->where(['capital_asset_item_no' => $earlySettlement['asset_item_no']])
                        ->select('capital_asset_id'),
                ],
            ]);
        }
    }

    /**
     * 根据 buyback 刷新资方还款计划
     *
     * @param $startDate
     * @param $endDate
     * @param $channel
     *
     * @return void
     */
    protected function buybackBiz($startDate, $endDate, $channel)
    {
        $buybackList = Buyback::find()
            ->where([
                'buyback_channel' => $channel,
            ])
            ->andWhere([
                'and',
                ['>=', 'buyback_create_at', $startDate],
                ['<', 'buyback_create_at', $endDate],
            ])
            ->select([
                'asset_item_no' => 'buyback_asset_item_no',
                'period' => 'buyback_period',
                'expect_compensate_at' => 'buyback_start_date',
                'loan_channel' => 'buyback_channel',
                'interest_amount' => 'buyback_total_interest_amount',
                'is_buyback' => new Expression('if(buyback_category = "buyback","YES","NO")'),
            ])->asArray()->all();

        /** @var array $buyback */
        foreach ($buybackList as $buyback) {
            $data = [
                'capital_transaction_operation_type' => 'buyback',
                'capital_transaction_repaid_amount' => new Expression('capital_transaction_amount'),
                'capital_transaction_user_repay_at' => $buyback['expect_compensate_at'],
                'capital_transaction_withhold_result_channel' => 'qsq',
                'capital_transaction_actual_operate_at' => $buyback['expect_compensate_at'],
            ];

            $data['capital_transaction_advance_at'] = Carbon::parse($buyback['expect_compensate_at'])->toDateString();

            CapitalTransaction::updateAll($data, [
                'AND',
                ['<>', 'capital_transaction_operation_type', CapitalTransaction::OPERATION_TYPE_BUYBACK],
                ['=', 'capital_transaction_channel', $channel],
                ['>=', 'capital_transaction_period', $buyback['period'],],
                [
                    'NOT IN',
                    'capital_transaction_type',
                    [
                        CapitalTransaction::TYPE_INTEREST,
                        CapitalTransaction::TYPE_GUARANTEE,
                    ],
                ],

                [
                    '=',
                    'capital_transaction_asset_id',
                    CapitalAsset::find()
                        ->where(['capital_asset_item_no' => $buyback['asset_item_no']])
                        ->select('capital_asset_id')->one()->capital_asset_id ?? 0,
                ],
                ['<>', 'capital_transaction_withhold_result_channel', $channel],
            ]);

            $this->saveBuybackCapitalTran($buyback, $channel);

            $data = [
                'capital_transaction_operation_type' => 'buyback',
                'capital_transaction_repaid_amount' => 0,
                'capital_transaction_user_repay_at' => $buyback['expect_compensate_at'],
                'capital_transaction_withhold_result_channel' => 'qsq',
                'capital_transaction_actual_operate_at' => $buyback['expect_compensate_at'],
            ];

            $data['capital_transaction_advance_at'] = Carbon::parse($buyback['expect_compensate_at'])->toDateString();

            CapitalTransaction::updateAll($data, [
                'AND',
                ['<>', 'capital_transaction_operation_type', CapitalTransaction::OPERATION_TYPE_BUYBACK],
                ['=', 'capital_transaction_channel', $channel],
                ['>', 'capital_transaction_period', $buyback['period'],],
                [
                    'IN',
                    'capital_transaction_type',
                    [CapitalTransaction::TYPE_INTEREST, CapitalTransaction::TYPE_GUARANTEE],
                ],
                [
                    '=',
                    'capital_transaction_asset_id',
                    CapitalAsset::find()
                        ->where(['capital_asset_item_no' => $buyback['asset_item_no']])
                        ->select('capital_asset_id')->one()->capital_asset_id ?? 0,
                ],
                ['<>', 'capital_transaction_withhold_result_channel', $channel],
            ]);
        }
    }

    /**
     * 修改当期担保费
     *
     * @param array  $buyback
     * @param string $channel
     *
     * @return void
     */
    private function saveBuybackCapitalTran(array $buyback, string $channel): void
    {
        //如果是回购的资产需要回收当期次的担保费
        $data = [
            'capital_transaction_operation_type' => 'buyback',
            'capital_transaction_repaid_amount' => $buyback['interest_amount'] > 0
            && $buyback['is_buyback'] === 'YES'
                ? new Expression('capital_transaction_amount') : 0,
            'capital_transaction_user_repay_at' => $buyback['expect_compensate_at'],
            'capital_transaction_withhold_result_channel' => 'qsq',
            'capital_transaction_actual_operate_at' => $buyback['expect_compensate_at'],
        ];

        $data['capital_transaction_advance_at'] = Carbon::parse($buyback['expect_compensate_at'])->toDateString();

        CapitalTransaction::updateAll($data, [
            'AND',
            ['<>', 'capital_transaction_operation_type', CapitalTransaction::OPERATION_TYPE_BUYBACK],
            ['=', 'capital_transaction_channel', $channel],
            ['=', 'capital_transaction_period', $buyback['period'],],
            ['=', 'capital_transaction_type', CapitalTransaction::TYPE_GUARANTEE],
            [
                '=',
                'capital_transaction_asset_id',
                CapitalAsset::find()
                    ->where(['capital_asset_item_no' => $buyback['asset_item_no']])
                    ->select('capital_asset_id')->one()->capital_asset_id ?? 0,
            ],
            ['<>', 'capital_transaction_withhold_result_channel', $channel],
        ]);


        //修改当期利息
        $data = [
            'capital_transaction_operation_type' => 'buyback',
            'capital_transaction_repaid_amount' => $buyback['interest_amount'],
            'capital_transaction_user_repay_at' => $buyback['expect_compensate_at'],
            'capital_transaction_withhold_result_channel' => 'qsq',
            'capital_transaction_actual_operate_at' => $buyback['expect_compensate_at'],
        ];

        $data['capital_transaction_advance_at'] = Carbon::parse($buyback['expect_compensate_at'])->toDateString();

        CapitalTransaction::updateAll($data, [
            'AND',
            ['<>', 'capital_transaction_operation_type', CapitalTransaction::OPERATION_TYPE_BUYBACK],
            ['=', 'capital_transaction_channel', $channel],
            ['=', 'capital_transaction_period', $buyback['period'],],
            ['=', 'capital_transaction_type', CapitalTransaction::TYPE_INTEREST],
            [
                '=',
                'capital_transaction_asset_id',
                CapitalAsset::find()
                    ->where(['capital_asset_item_no' => $buyback['asset_item_no']])
                    ->select('capital_asset_id')->one()->capital_asset_id ?? 0,
            ],
            ['<>', 'capital_transaction_withhold_result_channel', $channel],
        ]);
    }

    /**
     * 资方代扣
     *
     * @param string $startDate
     * @param string $endDate
     * @param string $channel
     *
     * @return void
     * @throws Exception
     */
    protected function capitalWithhold($startDate, $endDate, $channel)
    {
        $capitalWithhold = KeyValue::takeAsArray('capital_withhold');
        $capitalWithholdList = "'" . implode('\', \'', $capitalWithhold) . "'";

        $sql = <<<EOL
SELECT
    `T`.`asset_item_no`          AS `asset_item_no`,
    `T`.`withhold_channel`       AS `withhold_channel`,
    `B`.`finish_at`              AS `finishAt`,
    `T`.`period`                 AS `period`,
    `T`.`loanChannel`            AS `loanChannel`,
    `T`.`type`                   AS `type`,
    `T`.`capital_transaction_id` AS `capital_transaction_id`,
    `T`.`withhold_amount`        AS `withhold_amount`
FROM
    (SELECT
         `asset_item_no`
       , `withhold_result_channel`                 AS `withhold_channel`
       , `withhold_result_finish_at`               AS `finishAt`
       , `dtransaction_period`                     AS `period`
       , `asset_loan_channel`                      AS `loanChannel`
       , `dtransaction_type`                       AS `type`
       , `capital_transaction_id`
       , sum(`withhold_result_transaction_amount`) AS `withhold_amount`
     FROM
         `withhold_history`
             INNER JOIN `withhold_result_transaction`
                        ON `withhold_result_id` = `withhold_result_transaction_withhold_result_id`
                            AND `withhold_result_transaction_type` = 'dtransaction'
             INNER JOIN `dtransaction` ON `withhold_result_transaction_transaction_id` = `dtransaction_id`
             INNER JOIN `asset` ON `dtransaction_asset_id` = `asset_id`
             INNER JOIN `capital_asset`
                        ON `asset_item_no` = `capital_asset_item_no`
                            AND `asset_loan_channel` = `capital_asset_channel`
             INNER JOIN `capital_transaction`
                        ON `capital_transaction_period` = `dtransaction_period`
                            AND `capital_asset_id` = `capital_transaction_asset_id`
                            AND `dtransaction_type` = concat('repay', `capital_transaction_type`)
     WHERE
           `withhold_result_finish_at` < :endDate
       AND `withhold_result_channel` IN ($capitalWithholdList)
       AND `withhold_result_status` = 'success'
       AND `capital_transaction_withhold_result_channel` != `withhold_result_channel`
       AND `capital_transaction_type` IN ('principal', 'interest')
       AND `asset_loan_channel` = :channel
     GROUP BY
         `capital_transaction_id`
     UNION
     SELECT
         `asset_item_no`
       , `withhold_result_channel`                 AS `withhold_channel`
       , `withhold_result_finish_at`               AS `finishAt`
       , `ftransaction_period`                     AS `period`
       , `asset_loan_channel`                      AS `loanChannel`
       , `fee_type`                                AS `type`
       , `capital_transaction_id`
       , sum(`withhold_result_transaction_amount`) AS `withhold_amount`
     FROM
         `withhold_history`
             INNER JOIN `withhold_result_transaction`
                        ON `withhold_result_id` = `withhold_result_transaction_withhold_result_id`
                            AND `withhold_result_transaction_type` = 'ftransaction'
             INNER JOIN `ftransaction` ON `withhold_result_transaction_transaction_id` = `ftransaction_id`
             INNER JOIN `fee` ON `ftransaction_fee_id` = `fee_id`
             INNER JOIN `asset` ON `fee`.`fee_asset_id` = `asset_id`
             INNER JOIN `capital_asset`
                        ON `asset_item_no` = `capital_asset_item_no` AND `asset_loan_channel` = `capital_asset_channel`
             INNER JOIN `capital_transaction`
                        ON `capital_transaction_period` = `ftransaction_period` AND
                           `capital_asset_id` = `capital_transaction_asset_id` AND (
                                   (`capital_asset_granted_at` >= '2019-06-01' AND
                                    `capital_transaction_type` = `fee_type`)
                                   OR
                                   (`capital_asset_granted_at` < '2019-06-01')
                               )
     WHERE

           `withhold_result_finish_at` < :endDate
       AND `withhold_result_channel` IN ($capitalWithholdList)
       AND `withhold_result_status` = 'success'
       AND `capital_transaction_withhold_result_channel` != `withhold_result_channel`
       AND `capital_transaction_type` NOT IN ('principal', 'interest')
       AND `asset_loan_channel` = :channel
     GROUP BY
         `capital_transaction_id`
    ) `T`
        INNER JOIN (
        SELECT
            `capital_asset_item_no`      AS `asset_item_no`,
            `capital_transaction_period` AS `period`,
            `withhold_result_finish_at`  AS `finish_at`
        FROM
            `withhold_history`
                INNER JOIN `withhold_result_transaction`
                           ON `withhold_result_id` = `withhold_result_transaction_withhold_result_id`
                               AND `withhold_result_transaction_type` = 'dtransaction'
                INNER JOIN `dtransaction` ON `withhold_result_transaction_transaction_id` = `dtransaction_id`
                INNER JOIN `asset` ON `dtransaction_asset_id` = `asset_id`
                INNER JOIN `capital_asset`
                           ON `asset_item_no` = `capital_asset_item_no`
                               AND `asset_loan_channel` = `capital_asset_channel`
                INNER JOIN `capital_transaction`
                           ON `capital_transaction_period` = `dtransaction_period`
                               AND `capital_asset_id` = `capital_transaction_asset_id`
                               AND `dtransaction_type` = concat('repay', `capital_transaction_type`)
        WHERE
              `withhold_result_finish_at` >= :startDate
          AND `withhold_result_finish_at` < :endDate
          AND `withhold_result_channel` IN ($capitalWithholdList)
          AND `withhold_result_status` = 'success'
          AND `dtransaction_status` = 'finish'
          AND `capital_transaction_type` IN ('principal')
          AND `asset_loan_channel` = :channel
        GROUP BY
            `capital_transaction_id`
    ) `B` ON `B`.`asset_item_no` = `T`.`asset_item_no` AND `B`.`period` = `T`.`period`
;
EOL;

        $this->executeWithholdData('资方代扣', $sql, [
            'channel' => $channel,
            'startDate' => $startDate,
            'endDate' => $endDate,
        ]);
    }

    /**
     * @param string $startDate
     * @param string $endDate
     * @param string $channel
     *
     * @return void
     * @throws Exception
     */
    protected function qsqWithhold($startDate, $endDate, $channel)
    {
        $sql = <<<EOL
select `asset_item_no`
     , 'qsq'                                                                                      as withhold_channel
     , if(provision_id AND withhold_result_id, withhold_result_finish_at, dtransaction_finish_at) AS finishAt
     , `capital_transaction_period`                                                               as period
     , asset_loan_channel                                                                         as loanChannel
     , dtransaction_type                                                                          as type
     , capital_transaction_id
     , dtransaction_repaid_amount_f                                                               as withhold_amount
from dtransaction
         inner join asset
                    on `dtransaction_asset_id` = asset_id
         inner join `capital_asset` as f
                    on `asset_item_no` = `capital_asset_item_no` and asset_loan_channel = capital_asset_channel
         inner join `capital_transaction`
                    on `capital_transaction_period` = `dtransaction_period`
                        and `capital_asset_id` = `capital_transaction_asset_id`
                        AND `dtransaction_type` = concat('repay', `capital_transaction_type`)
         LEFT JOIN withhold_history
                   ON withhold_result_asset_item_no = asset_item_no AND withhold_result_status = :success
         LEFT JOIN withhold_result_transaction
                   ON withhold_result_transaction_withhold_result_id = withhold_result_id AND
                      withhold_result_transaction_period = dtransaction_period
         LEFT JOIN provision ON provision_item_no = asset_item_no AND provision_type = :asset_void
where `dtransaction_finish_at` >= :startDate
  and `dtransaction_finish_at` < :endDate
  and `capital_transaction_expect_finished_at` >= :startDate
  and `capital_transaction_user_repay_at` < '2010-01-01'
  and `capital_transaction_withhold_result_channel` = ''
  and `capital_transaction_type` in ('principal', 'interest')
  and `asset_loan_channel` = :channel
group by capital_transaction_id
UNION
select `asset_item_no`
     , 'qsq'                        as withhold_channel
     , `ftransaction_finish_at`     as finishAt
     , `capital_transaction_period` as period
     , asset_loan_channel           as loanChannel
     , fee_type                     as type
     , capital_transaction_id
     , ftransaction_repaid_amount_f as withhold_amount
from ftransaction
         INNER JOIN fee
                    ON ftransaction_fee_id = fee_id
         INNER JOIN asset
                    ON fee.`fee_asset_id` = asset_id
         INNER JOIN `capital_asset`
                    ON `asset_item_no` = `capital_asset_item_no` AND asset_loan_channel = capital_asset_channel
         inner join `capital_transaction`
                    on capital_transaction_period = ftransaction_period
                        and capital_asset_id = capital_transaction_asset_id AND (
                               (capital_asset_granted_at >= '2019-06-01' AND capital_transaction_type = fee_type)
                               OR
                               (capital_asset_granted_at < '2019-06-01')
                           )
where `ftransaction_finish_at` >= :startDate
  and `ftransaction_finish_at` < :endDate
  and `capital_transaction_expect_finished_at` >= :startDate
  and `capital_transaction_user_repay_at` < '2010-01-01'
  and `capital_transaction_withhold_result_channel` = ''
  and `capital_transaction_type` not in ('principal', 'interest')
  and `asset_loan_channel` = :channel
group by capital_transaction_id;
EOL;

        $this->executeWithholdData('我方代扣', $sql, [
            'channel' => $channel,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'asset_void' => Provision::TYPE_ASSET_VOID,
            'success' => WithholdResult::STATUS_SUCCESS,
        ]);
    }

    /**
     * @param string $label
     * @param string $sql
     * @param array  $params
     *
     * @return void
     * @throws Exception
     */
    protected function executeWithholdData($label, $sql, $params = [])
    {
        $command = CapitalAsset::getDb()->createCommand($sql, $params);
        $withholdList = $command->queryAll();

        $this->stdout(vsprintf("%s: %s\n共%d条\n", [
            $label,
            $command->getRawSql(),
            count($withholdList),
        ]), Console::FG_GREEN);

        foreach ($withholdList as $row) {
            try {
                CapitalAssetService::updateTrans(
                    $row['asset_item_no'],
                    $row['loanChannel'],
                    $row['finishAt'],
                    $row['withhold_channel'],
                    $row['capital_transaction_id'],
                    $row['withhold_amount']
                );
            } catch (Throwable $ex) {
                Yii::error($ex->getMessage());
                $this->stderr($ex->getMessage(), Console::FG_RED);
            }
        }
    }

    /**
     * 刷逾期数据
     *
     * @param string $startDate
     * @param string $endDate
     * @param string $channel
     *
     * @return void
     */
    protected function overdue($startDate, $endDate, $channel)
    {
        $total = CapitalTransaction::updateAll([
            'capital_transaction_operation_type' => CapitalTransaction::OPERATION_TYPE_OVERDUE,
            'capital_transaction_withhold_result_channel' => 'qsq',
            'capital_transaction_actual_operate_at' => new Expression('capital_transaction_expect_finished_at'),
        ], [
            'and',
            ['=', 'capital_transaction_channel', $channel],
            ['=', 'capital_transaction_operation_type', CapitalTransaction::OPERATION_TYPE_GRANT],
            ['>=', 'capital_transaction_expect_finished_at', $startDate],
            ['<', 'capital_transaction_expect_finished_at', $endDate],
        ]);

        $this->stdout(sprintf("%s 到期未还资产修改为overdue, 共%d条\n", $channel, $total), Console::FG_GREEN);
    }

    /**
     * 回滚数据
     *
     * @param string $startDate
     * @param string $endDate
     * @param string $channel
     *
     * @return void
     */
    protected function rollback($startDate, $endDate, $channel)
    {
        $total = CapitalTransaction::updateAll([
            'capital_transaction_operation_type' => CapitalTransaction::OPERATION_TYPE_GRANT,
            'capital_transaction_is_advance' => Consts::NO,
            'capital_transaction_repaid_amount' => 0,
            'capital_transaction_user_repay_at' => '1000-01-01',
            'capital_transaction_actual_operate_at' => '1000-01-01',
            'capital_transaction_advance_at' => '1000-01-01',
            'capital_transaction_status' => CapitalTransaction::STATUS_UNFINISHED,
            'capital_transaction_withhold_result_channel' => '',
        ], [
            'and',
            ['=', 'capital_transaction_channel', $channel],
            ['in', 'capital_transaction_operation_type', ['normal', 'overdue']],
            ['>=', 'capital_transaction_expect_finished_at', $startDate],
            ['<', 'capital_transaction_expect_finished_at', $endDate],
        ]);

        $this->stdout(sprintf("正常\逾期回滚: %d条\n", $total), Console::FG_GREEN);

        $total = CapitalTransaction::updateAll([
            'capital_transaction_operation_type' => CapitalTransaction::OPERATION_TYPE_GRANT,
            'capital_transaction_is_advance' => Consts::NO,
            'capital_transaction_repaid_amount' => 0,
            'capital_transaction_user_repay_at' => '1000-01-01',
            'capital_transaction_actual_operate_at' => '1000-01-01',
            'capital_transaction_advance_at' => '1000-01-01',
            'capital_transaction_status' => CapitalTransaction::STATUS_UNFINISHED,
            'capital_transaction_withhold_result_channel' => '',
        ], [
            'and',
            ['=', 'capital_transaction_channel', $channel],
            ['in', 'capital_transaction_operation_type', ['advance', 'buyback']],
            ['>=', 'capital_transaction_advance_at', $startDate],
            ['<', 'capital_transaction_advance_at', $endDate],
        ]);

        $this->stdout(sprintf("提前\回购回滚: %d条\n", $total), Console::FG_GREEN);
    }

    /**
     * 刷新 capital_tran amount
     *
     * @param $startDate
     * @param $endDate
     * @param $channel
     *
     * @throws Exception
     */
    public function syncToCapitalTran($startDate, $endDate, $channel): void
    {
        $sql = <<<SQL
INSERT INTO
    `capital_transaction` (`capital_transaction_asset_id`, `capital_transaction_channel`, `capital_transaction_grant_id`, `capital_transaction_grant_type`, `capital_transaction_type`, `capital_transaction_period`)
SELECT
    `CT1`.`capital_transaction_asset_id`,
    `CT1`.`capital_transaction_channel`,
    `CT1`.`capital_transaction_grant_id`,
    `CT1`.`capital_transaction_grant_type`,
    'credit_fee',
    `CT1`.`capital_transaction_period`
FROM
    `fee`
        INNER JOIN `ftransaction` ON `fee_id` = `ftransaction_fee_id`
        INNER JOIN `asset` ON `asset_id` = `fee_asset_id`
        INNER JOIN `capital_asset` ON `capital_asset_item_no` = `asset_item_no`
        INNER JOIN `capital_transaction` `CT1` ON `CT1`.`capital_transaction_asset_id` = `capital_asset_id` AND
                                                  `CT1`.`capital_transaction_period` = `ftransaction_period` AND
                                                  `CT1`.`capital_transaction_type` = 'principal'
        LEFT JOIN `capital_transaction` `CT2` ON `CT2`.`capital_transaction_asset_id` = `capital_asset_id` AND
                                                 `CT2`.`capital_transaction_period` = `ftransaction_period` AND
                                                 `CT2`.`capital_transaction_type` = 'credit_fee'
WHERE
      `fee_type` = 'credit_fee'
  AND `asset_loan_channel` = :channel
  AND `ftransaction_create_at` >= :startDate
  AND `ftransaction_create_at` < :endDate
  AND `CT2`.`capital_transaction_id` IS NULL;
SQL;

        $params = [
            ':channel' => $channel,
            ':startDate' => $startDate,
            ':endDate' => $endDate,
        ];

        $command = CapitalTransaction::getDb()->createCommand($sql, $params);
        echo $command->rawSql . PHP_EOL;

        $rows = $command->execute();

        echo "[{$startDate}][{$channel}][刷新 credit_fee]{$channel} rows:" . $rows . PHP_EOL . PHP_EOL . PHP_EOL;
    }

    /**
     * 刷新 capital_tran amount
     *
     * @param $startDate
     * @param $endDate
     * @param $channel
     *
     * @throws Exception
     */
    public function syncAmountToCapitalTran($startDate, $endDate, $channel): void
    {
        $sql = <<<SQL
UPDATE
    `capital_transaction`
        INNER JOIN `capital_asset` ON `capital_transaction_asset_id` = `capital_asset_id`
        INNER JOIN `asset` ON `asset_item_no` = `capital_asset_item_no` AND
                              `capital_asset_channel` = `asset_loan_channel`
        INNER JOIN `fee` ON `asset_id` = `fee_asset_id` AND
                            `fee_type` = 'credit_fee'
        INNER JOIN `ftransaction` ON `fee_id` = `ftransaction_fee_id` AND
                                     `capital_transaction_period` = `ftransaction_period`
SET
    `capital_transaction_amount`        = `ftransaction_amount_f`
WHERE
      `capital_transaction_type` = 'credit_fee'
  AND `asset_loan_channel` = :channel
  AND `capital_transaction_repaid_amount` <> 0
  AND `capital_transaction_update_at` >= :startDate
  AND `capital_transaction_update_at` < :endDate;
SQL;

        $params = [
            ':channel' => $channel,
            ':startDate' => $startDate,
            ':endDate' => $endDate,
        ];

        $command = CapitalTransaction::getDb()->createCommand($sql, $params);
        echo $command->rawSql . PHP_EOL;

        $rows = $command->execute();

        echo "[{$startDate}][{$channel}][刷新 credit_fee amount]{$channel} rows:" . $rows . PHP_EOL . PHP_EOL . PHP_EOL;
    }
}
