<?php

namespace dcs\console;

use Carbon\Carbon;
use common\models\Asset;
use common\models\AssetTran;
use dcs\models\CapitalSettlementRuleConfig;
use yii\console\Controller;
use yii\console\ExitCode;

class RefreshCapitalSettlementController extends Controller
{
    /**
     * 重刷资方结算规则中起来日期、最早到期日、最晚到期日、最早代偿日
     *
     * @return int
     */
    public function actionRefreshDueAt(): int
    {
        /** @var CapitalSettlementRuleConfig[] $settleRules */
        $settleRules = CapitalSettlementRuleConfig::find()
            ->where([
                'channel_status' => CapitalSettlementRuleConfig::CHANNEL_STATUS_2, // 放量中
            ])
            ->all();
        foreach ($settleRules as $settleRule) {
            if (empty($settleRule->grant_start_at) || empty($settleRule->min_due_at)) {
                $minActualGrantDate = Asset::find()
                    ->where([
                        'asset_loan_channel' => $settleRule->asset_loan_channel,
                        'asset_status' => ['repay', 'payoff'],
                    ])
                    ->min('asset_actual_grant_at');
                if ($minActualGrantDate) {
                    $settleRule->grant_start_at = Carbon::parse($minActualGrantDate)->toDateString();
                    $assetItemNo = Asset::find()
                        ->where([
                            'asset_loan_channel' => $settleRule->asset_loan_channel,
                            'asset_status' => ['repay', 'payoff'],
                            'asset_actual_grant_at' => $minActualGrantDate,
                        ])
                        ->select('asset_item_no')
                        ->scalar();
                    if ($assetItemNo) {
                        $minDueDate = AssetTran::find()
                            ->where([
                                'asset_tran_asset_item_no' => $assetItemNo,
                                'asset_tran_category' => 'principal',
                            ])
                            ->min('asset_tran_due_at');
                        if ($minDueDate) {
                            $settleRule->min_due_at = Carbon::parse($minDueDate)->toDateString();
                        }
                    }
                }
            }

            if (empty($settleRule->min_compensate_at) && $settleRule->min_due_at && $settleRule->compensate_settlement_rule) {
                $settleRule->min_compensate_at = Carbon::parse($settleRule->min_due_at)
                    ->addDays($settleRule->compensate_settlement_rule)
                    ->toDateString();
            }

            $maxDueAt = Asset::find()
                ->where('asset_actual_grant_at >= CURRENT_DATE')
                ->andWhere([
                    'asset_loan_channel' => $settleRule->asset_loan_channel,
                    'asset_status' => ['repay', 'payoff'],
                ])
                ->max('asset_due_at');
            if ($maxDueAt) {
                $settleRule->max_due_at = Carbon::parse($maxDueAt)->toDateString();
            }

            $dirtyAttributes = $settleRule->dirtyAttributes;
            if (!empty($dirtyAttributes)) {
                CapitalSettlementRuleConfig::updateAll($dirtyAttributes, [
                    'id' => $settleRule->id,
                ]);
            }
        }

        return ExitCode::OK;
    }
}
