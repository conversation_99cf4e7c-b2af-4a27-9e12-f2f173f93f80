<?php

namespace dcs\tasks;

use repay\components\RepayComponent;
use xlerr\task\TaskHandler;
use yii\base\InvalidConfigException;
use yii\base\UserException;

/**
 * 还款系统修改用户名任务
 */
class ReviseUserInfos extends TaskHandler
{
    public $name;
    public $idNum;
    public $phoneNum;

    public function rules()
    {
        return [
            [['name', 'idNum', 'phoneNum'], 'trim'],
            [['name', 'idNum'], 'required'],
            [['idNum', 'name', 'phoneNum'], 'match', 'pattern' => '/^enc_/'], // 密文验证
        ];
    }

    /**
     * @return array
     * @throws InvalidConfigException
     * @throws UserException
     */
    public function process(): array
    {
        $client = RepayComponent::instance();
        if (!$client->reviseUserInfos($this->task->task_key, $this->name, $this->idNum, $this->phoneNum)) {
            throw new UserException('请求还款系统出错: ' . $client->getError());
        }

        return $client->getResponse();
    }
}
