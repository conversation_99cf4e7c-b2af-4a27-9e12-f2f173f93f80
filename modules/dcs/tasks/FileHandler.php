<?php

namespace dcs\tasks;

use common\behaviors\ImportExpressionFunctionProvider;
use common\components\ExpressionComponent;
use dcs\services\FileService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use import\kernel\response\ImportResponse;
use import\services\ImportService;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use PhpOffice\PhpSpreadsheet\Exception;
use Throwable;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\di\Instance;
use yii\helpers\ArrayHelper;

class FileHandler extends SyncTaskHandler
{

    protected static ?array $fileParseConfig = null;
    public $file_url;
    public $business_date;

    public $analysis_type;

    public $loan_channel;

    /**
     * @return array
     * @throws KVException
     */
    public function rules(): array
    {
        $types = KeyValue::take('file_parse_analysis_type');

        return [
            [['file_url', 'business_date', 'analysis_type', 'loan_channel'], 'required'],
//            ['analysis_type', 'in', 'range' => ['compensate', 'buyback', 'reimbursing','chargeback']],
            ['analysis_type', 'in', 'range' => $types],
        ];
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function initConfig(): void
    {
        if (self::$fileParseConfig === null) {
            $value = KeyValue::take('file_parse_config');
            $key = sprintf('%s.%s', $this->loan_channel, $this->analysis_type);
            if (($config = ArrayHelper::getValue($value, $key)) === null) {
                throw new UserException(sprintf('资方: %s 存在未知的解析类型配置: [%s]', $this->loan_channel, $this->analysis_type));
            }
            self::$fileParseConfig = $config;
        }
        ExpressionComponent::instance()->registerProvider(new ImportExpressionFunctionProvider());
    }

    /**
     * @return TaskResult
     * @throws \Exception
     */
    public function process(): TaskResult
    {
        $this->initConfig();
        ob_start();
        try {
            $guzzleClient = new Client([
                'timeout' => 2.0,
            ]);
            $content = (string)($guzzleClient->get($this->file_url)->getBody());
            $params = array_merge(self::$fileParseConfig['file'] ?? [], ['class' => FileService::class]);
            /**
             * @var FileService $service
             */
            $service = Instance::ensure(Yii::createObject($params), FileService::class);
            $file = tmpfile();
            $fileName = $service->parseFile($content)->rows($file, $this->business_date)->getFileName();
            $line = $this->parseAfterHandler($fileName)->getSuccessLine();
            ob_end_clean();
            return TaskResult::success(['count' => $line]);
        } catch (GuzzleException|Throwable $e) {
            ob_end_clean();
            return TaskResult::failure($e->getMessage());
        }

    }

    /**
     * @param string $fileName
     * @return ImportResponse
     * @throws Exception
     * @throws InvalidConfigException
     * @throws Throwable
     * @throws UserException
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws \ReflectionException
     * @throws \yii\db\Exception
     */
    protected function parseAfterHandler(string $fileName): ImportResponse
    {
        $importConfig = self::$fileParseConfig['import'] ?? [];
        $importService = new ImportService($fileName, $importConfig, $importConfig['memory'] ?? '128M');
        return $importService->process();
    }
}