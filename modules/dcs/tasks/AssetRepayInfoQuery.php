<?php

namespace dcs\tasks;

use repay\components\RepayComponent;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;

class AssetRepayInfoQuery extends SyncTaskHandler
{
    /** @var string */
    public $itemNo;

    public function rules(): array
    {
        return [
            [['itemNo'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws InvalidConfigException
     */
    public function process(): TaskResult
    {
        $client = RepayComponent::instance();
        $client->assetInfo($this->itemNo);

        return new TaskResult($client->getResponse());
    }
}
