<?php

namespace dcs\tasks;

use common\models\AssetCard;
use common\models\AssetIndividual;
use common\models\Card;
use common\models\Individual;
use Throwable;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;

use function xlerr\desensitise\decrypt;

/**
 * 更新用户信息
 */
class UserInfoUpdate extends SyncTaskHandler
{
    public $idNum;
    public $name;
    public $formerName;
    public $cardNum;
    public $phoneNum;

    public function formName(): string
    {
        return 'data';
    }

    public function rules(): array
    {
        return [
            [['idNum', 'name', 'formerName', 'cardNum', 'phoneNum'], 'trim'],
            [['idNum', 'name'], 'required'],
            [['idNum', 'name', 'formerName', 'cardNum', 'phoneNum'], 'match', 'pattern' => '/^enc_/'], // 密文验证
            [
                ['idNum'],
                'exist',
                'targetClass' => Individual::class,
                'targetAttribute' => 'individual_idnum_encrypt',
                'message' => Individual::class.'数据不存在',
            ],
        ];
    }

    /**
     * @return TaskResult
     * @throws Throwable
     * @throws UserException
     */
    public function process(): TaskResult
    {
        $name = decrypt($this->name);

        $individualData = [
            'individual_name' => $name,
            'individual_name_encrypt' => $this->name,
        ];
        $assetIndividualData = [
            'asset_individual_name' => $name,
            'asset_individual_name_encrypt' => $this->name,
        ];
        $assetCardData = [
            'asset_card_owner_name' => $name,
            'asset_card_account_name' => $name,
            'asset_card_owner_name_encrypt' => $this->name,
            'asset_card_account_name_encrypt' => $this->name,
        ];
        if ($this->phoneNum) {
            $phoneNum = decrypt($this->phoneNum);
            $individualData = array_merge([
                'individual_tel' => $phoneNum,
                'individual_tel_encrypt' => $this->phoneNum,
            ]);
            $assetIndividualData = array_merge([
                'asset_individual_tel' => $phoneNum,
                'asset_individual_tel_encrypt' => $this->phoneNum,
            ]);
            $assetCardData = array_merge([
                'asset_card_account_tel' => $phoneNum,
                'asset_card_account_tel_encrypt' => $this->phoneNum,
            ]);
        }

        $affectedRow = Individual::updateAll($individualData, [
            'individual_idnum_encrypt' => $this->idNum,
        ]);
        if ($affectedRow !== 1) {
            throw new UserException('修改individual表信息失败');
        }

        $affectedRow = AssetIndividual::updateAll($assetIndividualData, [
            'asset_individual_idnum_encrypt' => $this->idNum,
        ]);
        if ($affectedRow === 0) {
            throw new UserException('修改asset_individual表信息失败');
        }

        $affectedRow = AssetCard::updateAll($assetCardData, [
            'asset_card_owner_idnum_encrypt' => $this->idNum,
        ]);
        if ($affectedRow === 0) {
            throw new UserException('修改asset_card表信息失败');
        }

        // card可能没有数据, 所以不校验修改结果
        Card::updateAll([
            'card_user_name' => $name,
            'card_account_name' => $name,
            'card_user_name_encrypt' => $this->name,
            'card_account_name_encrypt' => $this->name,
        ], [
            'card_user_id_encrypt' => $this->idNum,
        ]);

        // 支付系统: 作废绑卡信息
        PaymentAbolishBind::make([
            'idNum' => $this->idNum,
            'cardNum' => $this->cardNum,
        ], [
            'task_key' => $this->task->synctask_key,
        ]);

        // 还款系统: 修改用户改名
        ReviseUserInfos::make([
            'name' => $this->name,
            'idNum' => $this->idNum,
            'phoneNum' => $this->phoneNum,
        ], [
            'task_key' => $this->task->synctask_key,
        ]);

        // 放款系统

        return TaskResult::success();
    }

    public function attributeLabels(): array
    {
        return [
            'idNum' => '身份证号码',
            'name' => '用户姓名', // 新姓名
            'formerName' => '用户曾用名', // 旧姓名
            'cardNum' => '银行卡号',
            'phoneNum' => '手机号',
        ];
    }
}
