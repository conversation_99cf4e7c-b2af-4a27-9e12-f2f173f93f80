<?php

namespace dcs\tasks;

use common\models\Liabilities;
use xlerr\task\TaskHandler;
use yii\base\UserException;

class AutoGenerateRepaymentPlan extends TaskHandler
{

    public int $liabilities_id;

    public function process()
    {

    }


    /**
     * @return Liabilities|null
     * @throws UserException
     */
    protected function findLiabilities(): ?Liabilities
    {
        $liabilities = Liabilities::findOne($this->liabilities_id);
        if (!$liabilities) {
            throw new UserException(sprintf('保理资方ID:[%d]记录不存在!', $this->liabilities_id));
        }
        return $liabilities;
    }
}