<?php

namespace dcs\tasks;

use Carbon\Carbon;
use cpm\models\CapitalChannel;
use dcs\models\CapitalSettlementRuleConfig;
use dcs\models\DepbankOrder;
use dcs\models\FlowBankBiz;
use RuntimeException;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use yii\db\Expression;

class FunderReceived extends SyncTaskHandler
{
    public string $startDate = '';
    public string $endDate = '';

    public function rules(): array
    {
        return [
            [['startDate', 'endDate'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     */
    public function process(): TaskResult
    {
        $dbIncomeFees = CapitalSettlementRuleConfig::find()
            ->select('bd_income_fees')
            ->indexBy('asset_loan_channel')
            ->column();

        $data = [];

        $channelHuman = CapitalChannel::list(false, 'name');

        $startDate = $this->startDate;
        $endDate = Carbon::parse($this->endDate)->addDay()->toDateString();


        foreach ($dbIncomeFees as $channel => $settlementTypeString) {
            if (empty($settlementTypeString) || !is_string($settlementTypeString)) {
                continue;
            }

            $settlementTypes = preg_split('/\s*,\s*/', $settlementTypeString, -1, PREG_SPLIT_NO_EMPTY);
            if ($settlementTypes) {
                $records = FlowBankBiz::find()
                    ->select([
                        'funder_code' => 'flow_bank_biz_funder_code',
                        'amount_type' => 'flow_bank_biz_amount_type',
                        'amount' => 'sum(flow_bank_biz_roll_in_money)',
                        'trade_date' => 'flow_bank_biz_trade_date',
                    ])
                    ->where([
                        'flow_bank_biz_funder_code' => $channel,
                        'flow_bank_biz_amount_type' => $settlementTypes,
                    ])
                    ->andWhere([
                        'and',
                        ['<', 'flow_bank_biz_deleted_at', '2000-01-01'],
                        ['>', 'flow_bank_biz_roll_in_money', 0],
                        ['>=', 'flow_bank_biz_trade_date', $startDate],
                        ['<', 'flow_bank_biz_trade_date', $endDate],
                    ])
                    ->groupBy([
                        'flow_bank_biz_funder_code',
                        'flow_bank_biz_amount_type',
                        'flow_bank_biz_trade_date',
                    ])
                    ->indexBy(fn(array $row) => vsprintf('%s,%s,%s', [
                        $row['funder_code'],
                        $row['amount_type'],
                        $row['trade_date'],
                    ]))
                    ->asArray()
                    ->all();

                $capitalCollectedTypes = array_intersect($settlementTypes, [
                    'capital_collected_service_fee',
                    'capital_collected_service_fee1',
                ]);
                if ($capitalCollectedTypes) {
                    $channelName = $channelHuman[$channel] ?? null;
                    if (empty($channelName)) {
                        throw new RuntimeException('获取资金方名称错误');
                    }

                    $capitalCollectedTypes = array_map(static function ($type) {
                        return $type === 'capital_collected_service_fee' ? 'capital_collected_service_fee2' : $type;
                    }, $capitalCollectedTypes);

                    $capitalCollectedRecords = DepbankOrder::find()
                        ->where([
                            'and',
                            ['in', 'label', (array)$capitalCollectedTypes],
                            ['like', 'memo', $channelName],
                            ['=', 'status', 'successful'],
                            ['>=', 'finish_at', $startDate],
                            ['<', 'finish_at', $endDate],
                        ])
                        ->select([
                            'funder_code' => new Expression(sprintf("'%s'", $channel)),
                            'trade_date' => 'finish_at',
                            'amount_type' => new Expression('IF(label = \'capital_collected_service_fee2\', \'capital_collected_service_fee\', label)'),
                            'amount',
                        ])
                        ->indexBy(function (array $row) use ($channel) {
                            return vsprintf('%s,%s,%s', [
                                $channel,
                                $row['amount_type'],
                                $row['trade_date']
                            ]);
                        })
                        ->asArray()
                        ->all();

                    $records = array_replace($records, $capitalCollectedRecords);
                }

                foreach ($records as $row) {
                    $row['funder_name'] = $channelHuman[$row['funder_code']] ?? $row['funder_code'];
                    $row['trade_bizid'] = 0;
                    $data[] = $row;
                }
            }
        }

        return TaskResult::success($data);
    }
}
