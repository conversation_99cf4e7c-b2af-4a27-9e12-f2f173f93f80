<?php

namespace dcs\tasks;

use common\models\Asset;
use repay\components\RepayComponent;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;
use yii\base\UserException;

class ManualWithhold extends SyncTaskHandler
{
    public $request_key;
    public $asset_item_no;
    public $withhold_amount;
    public $operator;

    public function rules()
    {
        return [
            [['request_key', 'withhold_amount', 'asset_item_no', 'operator'], 'required'],
            [['withhold_amount'], 'integer'],
        ];
    }

    /**
     * @return TaskResult
     * @throws InvalidConfigException
     * @throws UserException
     */
    public function process(): TaskResult
    {
        $asset = Asset::findOne(['asset_item_no' => $this->asset_item_no]);
        if (!$asset) {
            throw new UserException('资产不存在: '.$this->asset_item_no);
        }

        $client = RepayComponent::instance();
        $client->manualWithholdEncrypt([
            'from_system' => 'BIZ',
            'key' => $this->request_key,
            'type' => 'WebManualWithhold',
            'busi_key' => $asset->asset_item_no,
            'data' => [
                'from_system' => 'BIZ',
                'project_num' => $asset->asset_item_no,
                'period' => null,
                'amount' => $this->withhold_amount,
                'operator' => $this->operator,
            ],
        ]);

        return TaskResult::success($client->getResponse());
    }
}
