<?php

namespace dcs\tasks;

use common\consts\Consts;
use common\models\Asset;
use common\models\AssetCard;
use common\models\AssetIndividual;
use common\models\Dtransaction;
use common\models\Enterprise;
use kvmanager\KVException;
use xlerr\common\helpers\MoneyHelper;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;

class FoxQueryOverdueAsset extends SyncTaskHandler
{
    /** @var string */
    public $itemNo;

    public function rules(): array
    {
        return [
            [['itemNo'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws KVException
     */
    public function process(): TaskResult
    {
        $asset = Asset::getAssetByItemNo($this->itemNo);
        if (!$asset) {
            return TaskResult::failure('资产不存在');
        }

        return TaskResult::success($this->foxAssetBuild($asset));
    }

    /**
     * @param Asset $asset
     *
     * @return array
     * @throws KVException
     */
    protected function foxAssetBuild(Asset $asset): array
    {
        $data = [
            'asset' => [
                'asset_item_number' => $asset->asset_item_no,
                'asset_from_system' => $asset->asset_from_system ?: 'qsq',
                'asset_type' => Asset::assetTypeList()[$asset->asset_type] ?? '',
                'asset_sub_type' => $asset->asset_sub_type,
                'asset_name' => $asset->asset_name,
                'asset_sign_at' => $asset->asset_sign_at,
                'asset_grant_at' => $asset->asset_grant_at,
                'asset_due_at' => $asset->asset_due_at,
                'asset_interest_amount' => $asset->asset_interest_amount_f,
                'asset_repaid_interest_amount' => $asset->asset_repaid_interest_amount_f,
                'asset_principal_amount' => $asset->asset_principal_amount_f,
                'asset_repaid_principal_amount' => $asset->asset_repaid_principal_amount_f,
                'asset_penalty_amount' => MoneyHelper::y2f($asset->asset_late_amount),
                'asset_repaid_penalty_amount' => MoneyHelper::y2f($asset->asset_repaid_late_amount),
                'asset_decrease_penalty_amount' => MoneyHelper::y2f($asset->asset_decrease_late_amount),
                'asset_fee_amount' => MoneyHelper::y2f($asset->asset_fee_amount),
                'asset_repaid_fee_amount' => MoneyHelper::y2f($asset->asset_repaid_fee_amount),
                'asset_channel' => $asset->firstChannel->channel_name ?? '',
                'asset_city_code' => '',
                'asset_status' => $asset->asset_status,
                'asset_loan_channel' => $asset->asset_loan_channel,
                'asset_repaid_amount' => array_sum([
                    MoneyHelper::y2f($asset->asset_repaid_fee_amount),
                    MoneyHelper::y2f($asset->asset_repaid_late_amount),
                    $asset->asset_repaid_interest_amount_f,
                    $asset->asset_repaid_principal_amount_f,
                ]),
                'asset_period_type' => $asset->asset_period_type,
                'asset_period_count' => $asset->asset_period_count,
                'asset_period_days' => $asset->asset_period_days,
                'asset_ref_order_no' => $asset->assetExtend->asset_extend_ref_order_no ?? '',
                'asset_ref_order_type' => $asset->assetExtend->asset_extend_ref_order_type ?? '',
                'asset_risk_level' => $asset->assetExtend->asset_extend_risk_level ?? '',
                'asset_sub_order_type' => $asset->assetExtend->asset_extend_sub_order_type ?? '',
                'asset_product_name' => $asset->assetExtend->asset_extend_product_name ?? '',
                'asset_actual_grant_at' => $asset->asset_actual_grant_at,
                'asset_owner' => $asset->asset_owner ?? Asset::OWNER_KN,
                'asset_version' => (int)($asset->asset_version ?? 0),
                'asset_credit_term' => $asset->getCreditTerm(),
            ],
            'receive_card' => $asset->getReceiveCard()
                ? self::convertAssetCardToFoxReceiveCard($asset->getReceiveCard()) : null,
            'borrower' => $asset->assetIndividualBorrower
                ? $this->convertToFoxIndividual($asset->assetIndividualBorrower) : null,
            'repayer' => $asset->assetIndividualRepay
                ? $this->convertToFoxIndividual($asset->assetIndividualRepay) : null,
            'subborrower' => $asset->assetIndividualSubborrow
                ? $this->convertToFoxIndividual($asset->assetIndividualSubborrow) : null,
            'mortgageer' => $asset->assetIndividualMortagee
                ? $this->convertToFoxIndividual($asset->assetIndividualMortagee) : null,
            'securer' => $asset->assetIndividualSecure
                ? $this->convertToFoxIndividual($asset->assetIndividualSecure) : null,
            'borrow_enterprise' => $asset->assetEnterpriseBorrow
                ? $this->convertToFoxEnterprise($asset->assetEnterpriseBorrow) : null,
            'secure_enterprise' => $asset->assetEnterpriseSecure
                ? $this->convertToFoxEnterprise($asset->assetEnterpriseSecure) : null,
            'asset_transactions' => [],
        ];

        if ($asset->isNoLoan() && $asset->refAsset) {
            $data['asset']['asset_from_app'] = $asset->refAsset->asset_from_app;
            $data['asset']['asset_repayment_app'] = $asset->refAsset->asset_repayment_app;
        } else {
            $data['asset']['asset_from_app'] = $asset->asset_from_app;
            $data['asset']['asset_repayment_app'] = $asset->asset_repayment_app;
        }

        $dtranQuery = $asset->getDtransaction()->andWhere([
            'dtransaction_type' => [
                Dtransaction::DTRANSACTION_TYPE_REPAYINTEREST,
                Dtransaction::DTRANSACTION_TYPE_REPAYPRINCIPAL,
            ],
        ]);
        /** @var Dtransaction $dtran */
        foreach ($dtranQuery->each() as $dtran) {
            $data['asset_transactions'][] = [
                'asset_transaction_type' => $dtran->dtransaction_type,
                'asset_transaction_expect_finish_time' => $dtran->dtransaction_expect_finish_time,
                'asset_transaction_period' => $dtran->dtransaction_period,
                'asset_transaction_remark' => $dtran->dtransaction_remark,
                'asset_transaction_decrease_amount' => $dtran->dtransaction_decrease_amount,
                'asset_transaction_repaid_amount' => $dtran->dtransaction_repaid_amount_f,
                'asset_transaction_amount' => $dtran->dtransaction_amount_f
                    - $dtran->dtransaction_decrease_amount,
                'asset_transaction_status' => [
                        Consts::TRANSACTION_STATUS_NO_FINISH => Consts::TRANSACTION_STATUS_UN_FINISH,
                    ][$dtran->dtransaction_status] ?? Consts::TRANSACTION_STATUS_FINISH,
                'asset_transaction_finish_at' => $dtran->dtransaction_finish_at === Consts::DEFAULT_DATE
                    ? '' : $dtran->dtransaction_finish_at,
            ];
        }

        foreach ($asset->fee as $fee) {
            foreach ($fee->ftransactions as $ftran) {
                if (in_array($fee->fee_type, [Consts::FEE_TYPE_DELAY, Consts::FEE_TYPE_DELAY_INTEREST], true)) {
                    continue;
                }

                $data['asset_transactions'][] = [
                    'asset_transaction_type' => 'repay'.$fee->fee_type,
                    'asset_transaction_amount' => $ftran->ftransaction_amount_f,
                    'asset_transaction_expect_finish_time' => $ftran->ftransaction_expect_finish_time,
                    'asset_transaction_period' => $ftran->ftransaction_period,
                    'asset_transaction_remark' => '',
                    'asset_transaction_decrease_amount' => $ftran->ftransaction_decrease_amount_f,
                    'asset_transaction_repaid_amount' => $ftran->ftransaction_repaid_amount_f,
                    'asset_transaction_status' => [
                            Consts::TRANSACTION_STATUS_FINISH => Consts::TRANSACTION_STATUS_FINISH,
                        ][$ftran->ftransaction_status] ?? Consts::TRANSACTION_STATUS_UN_FINISH,
                    'asset_transaction_finish_at' => $ftran->ftransaction_finish_at > '1970-01-01'
                        ? $ftran->ftransaction_finish_at : '',
                ];
            }
        }

        return $data;
    }

    protected function convertToFoxEnterprise(Enterprise $enterprise): array
    {
        return [
            'enterprise_name' => $enterprise->enterprise_name,
            'enterprise_license' => $enterprise->enterprise_license,
            'enterprise_office_addr' => $enterprise->enterprise_office_addr,
            'enterprise_tel' => $enterprise->enterprise_tel,
            'enterprise_legal_person' => $enterprise->enterprise_legal_person,
            'enterprise_legal_person_tel' => $enterprise->enterprise_legal_person_tel,
            'enterprise_legal_person_num' => $enterprise->enterprise_legal_person_num,
            'enterprise_first_contact_name' => $enterprise->enterprise_contact,
            'enterprise_first_contact_tel' => $enterprise->enterprise_tel,
            'enterprise_second_contact_name' => $enterprise->enterprise_contact_backup,
            'enterprise_second_contact_tel' => $enterprise->enterprise_tel_backup,
            'enterprise_remark' => '',
        ];
    }

    protected function convertToFoxIndividual(AssetIndividual $individual): array
    {
        return [
            'individual_name' => $individual->asset_individual_name,
            'enc_individual_name' => $individual->asset_individual_name_encrypt,
            'code_individual_name' => $individual->asset_individual_name,
            'individual_idnum' => $individual->asset_individual_idnum,
            'enc_individual_idnum' => $individual->asset_individual_idnum_encrypt,
            'code_individual_idnum' => $individual->asset_individual_idnum,
            'individual_gender' => $individual->asset_individual_gender,
            'individual_residence' => $individual->asset_individual_residence,
            'individual_workplace' => $individual->asset_individual_workplace,
            'individual_permanent' => $individual->asset_individual_id_addr,
            'individual_company' => $individual->asset_individual_corp_name,
            'individual_tel' => $individual->asset_individual_tel,
            'enc_individual_tel' => $individual->asset_individual_tel_encrypt,
            'code_individual_tel' => $individual->asset_individual_tel,
            'individual_work_tel' => $individual->asset_individual_corp_tel,
            'enc_individual_work_tel' => $individual->asset_individual_corp_tel_encrypt,
            'code_individual_work_tel' => $individual->asset_individual_corp_tel,
            'individual_residence_tel' => '',
            'enc_individual_residence_tel' => '',
            'code_individual_residence_tel' => '',
            'individual_mate_name' => $individual->asset_individual_mate_name,
            'enc_individual_mate_name' => $individual->asset_individual_mate_name_encrypt,
            'code_individual_mate_name' => $individual->asset_individual_mate_name,
            'individual_mate_tel' => $individual->asset_individual_mate_tel,
            'enc_individual_mate_tel' => $individual->asset_individual_mate_tel_encrypt,
            'code_individual_mate_tel' => $individual->asset_individual_mate_tel,
            'individual_relative_name' => $individual->asset_individual_relative_name,
            'enc_individual_relative_name' => $individual->asset_individual_relative_name_encrypt,
            'code_individual_relative_name' => $individual->asset_individual_relative_name,
            'individual_relative_relation' => $individual->asset_individual_relative_relation,
            'individual_relative_tel' => $individual->asset_individual_relative_tel,
            'enc_individual_relative_tel' => $individual->asset_individual_relative_tel_encrypt,
            'code_individual_relative_tel' => $individual->asset_individual_relative_tel,
            'individual_workmate_name' => $individual->asset_individual_workmate_name,
            'enc_individual_workmate_name' => $individual->asset_individual_workmate_name_encrypt,
            'code_individual_workmate_name' => $individual->asset_individual_workmate_name,
            'individual_workmate_tel' => $individual->asset_individual_workmate_tel,
            'enc_individual_workmate_tel' => $individual->asset_individual_workmate_tel_encrypt,
            'code_individual_workmate_tel' => $individual->asset_individual_workmate_tel,
            'individual_remark' => '',
            'individual_nation' => $individual->asset_individual_nation,
            'individual_email' => $individual->asset_individual_email,
        ];
    }

    private static function convertAssetCardToFoxReceiveCard(AssetCard $assetCard): array
    {
        return [
            'card_bank_code' => $assetCard->asset_card_account_bank_code,
            'card_bank_name' => $assetCard->asset_card_account_bank_name,
            'card_num_encrypt' => $assetCard->asset_card_account_card_number_encrypt,
        ];
    }
}
