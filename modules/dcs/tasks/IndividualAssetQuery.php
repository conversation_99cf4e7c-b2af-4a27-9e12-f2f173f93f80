<?php

namespace dcs\tasks;

use common\business\AssetHelper;
use common\consts\Consts;
use common\models\Asset;
use common\models\AssetExtend;
use common\models\Dtransaction;
use common\models\Individual;
use repay\components\RepayComponent;
use xlerr\desensitise\Desensitise;
use xlerr\task\models\Task;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;

use function xlerr\desensitise\encrypt;

class IndividualAssetQuery extends SyncTaskHandler
{
    /** @var string */
    public $idNum;

    public function rules(): array
    {
        return [
            [['idNum'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws InvalidConfigException
     */
    public function process(): TaskResult
    {
        $idNum = $this->idNum;
        if (!str_starts_with($idNum, 'enc_')) {
            $idNum = encrypt($idNum, Desensitise::TYPE_IDENTITY_NUMBER)->hash;
        }
        $individual = Individual::findOne(['individual_idnum_encrypt' => $idNum]);
        if (!$individual) {
            return TaskResult::failure('身份证号码在系统不存在');
        }

        $assetData = [];
        foreach ($individual->borrowAssets as $asset) {
            if (
                $asset->asset_loan_channel === Consts::ASSET_LOAN_CHANNEL_NOLOAN ||
                in_array($asset->asset_status, [Asset::STATUS_SIGN, Asset::STATUS_SALE], true)
            ) {
                continue;
            }

            $loanAsset = AssetExtend::find()->where([
                'asset_extend_asset_id' => $asset->asset_id,
            ])->andWhere(['not in', 'asset_extend_sub_order_type', ['consumer']])->exists();
            if (!$loanAsset) {
                continue;
            }

            $assetResult = AssetHelper::getAssetForFK($asset);
            if ($asset->asset_status !== Asset::STATUS_PAYOFF) {
                $repayComponent = RepayComponent::instance();
                if ($repayComponent->assetInfo($asset->asset_item_no) && $responseData = $repayComponent->getData()) {
                    $assetFinish = true;

                    $asset_repaid_principal_amount = 0;

                    foreach ($responseData['repay'] as $rbizDtran) {
                        $repaidAmount = (int)($rbizDtran['repaid_amount'] ?? 0);

                        $asset_repaid_principal_amount += $repaidAmount;

                        if ($repaidAmount >= $rbizDtran['expect_repay_amount']) {
                            foreach ($assetResult->asset_transactions as $dtran) {
                                if (
                                    $dtran->asset_transaction_type === Dtransaction::DTRANSACTION_TYPE_REPAYPRINCIPAL
                                    && $dtran->asset_transaction_period == $rbizDtran['period']
                                ) {
                                    $dtran->asset_transaction_status        = Dtransaction::DTRANSACTION_STATUS_FINISH;
                                    $dtran->asset_transaction_repaid_amount = $repaidAmount;
                                    $dtran->asset_transaction_finish_at     = $rbizDtran['repaid_at'];
                                    break;
                                }
                            }
                        } else {
                            $assetFinish = false;
                        }
                    }
                    $assetResult->asset->asset_repaid_principal_amount = $asset_repaid_principal_amount;

                    $assetResult->asset->asset_status = $assetFinish ? Asset::STATUS_PAYOFF : Asset::STATUS_REPAY;
                }
            }
            $assetData[] = $assetResult;
        }

        return TaskResult::success($assetData);
    }
}
