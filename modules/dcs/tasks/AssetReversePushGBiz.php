<?php

namespace dcs\tasks;

use dcs\models\AssetReverse;
use grant\components\GrantComponent;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\helpers\ArrayHelper;

class AssetReversePushGBiz extends TaskHandler
{
    /***
     * @return TaskResult
     * @throws InvalidConfigException
     * @throws UserException
     */
    public function process(): TaskResult
    {
        $itemNoList = json_decode($this->task->task_request_data, true);

        $requestData = [
            'type'        => 'AssetReverse',
            'key'         => $this->task->task_key,
            'from_system' => $this->task->task_from_system,
            'data'        => array_values($itemNoList),
        ];

        $client = GrantComponent::instance();
        if (!$client->assetReverse($requestData)) {
            throw new UserException($client->getError());
        }

        $activeList = (array)ArrayHelper::getValue($client->getData(), 'active_list');
        if (!empty($activeList)) {
            $affectedCount = AssetReverse::updateAll([
                'status' => AssetReverse::STATUS_GBIZ_RECEIVE,
            ], [
                'status'        => AssetReverse::STATUS_ENTERING,
                'asset_item_no' => $activeList,
            ]);
            if (count($activeList) !== $affectedCount) {
                throw new UserException('更新数和预计更新数不一致');
            }
        }

        $inactiveList = (array)ArrayHelper::getValue($client->getData(), 'inactive_map');
        if (!empty($inactiveList)) {
            foreach ($inactiveList as $itemNo => $errInfo) {
                $result = AssetReverse::updateAll([
                    'memo'   => $errInfo,
                    'status' => AssetReverse::STATUS_FAIL,
                ], [
                    'status'        => AssetReverse::STATUS_ENTERING,
                    'asset_item_no' => $itemNo,
                ]);
                if (!$result) {
                    throw new UserException('记录错误信息失败: ' . $itemNo);
                }
            }
        }

        return TaskResult::success($client->getData());
    }
}
