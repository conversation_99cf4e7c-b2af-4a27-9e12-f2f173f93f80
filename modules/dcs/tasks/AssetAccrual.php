<?php

namespace dcs\tasks;

use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;
use yii\db\Exception;

class AssetAccrual extends SyncTaskHandler
{
    public $item_no;
    public $period;

    public function formName(): string
    {
        return 'data';
    }

    public function rules(): array
    {
        return [
            [['item_no', 'period'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws InvalidConfigException
     * @throws Exception
     */
    public function process(): TaskResult
    {
        $sql = <<<SQL
SELECT * 
FROM clean_accrual 
WHERE asset_item_no = :item_no
  AND period = :period
  AND status <> 'void'
SQL;

        $command = instanceEnsureDb('dbCapital')->createCommand($sql, $this->attributes);

        $result = (array)($command->queryOne() ?: []);

        return TaskResult::success($result);
    }
}
