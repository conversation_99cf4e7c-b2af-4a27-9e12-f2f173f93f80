<?php

namespace dcs\tasks;

use Carbon\Carbon;
use common\components\DcsComponent;
use dcs\models\DepbankManual;
use xlerr\task\models\Task;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;
use yii\base\UserException;

class DepbankOrderApply extends TaskHandler
{
    public $orderNo;
    public $orderType;

    public $amount;
    public $outAcctNo;
    public $outAcctBank;
    public $inAcctNo;
    public $inAcctBank;

    public $memo;


    public function rules(): array
    {
        return [
            [['memo', 'inAcctNo', 'inAcctBank'], 'safe'],
            [['orderNo', 'orderType', 'amount', 'outAcctNo', 'outAcctBank'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws UserException
     * @throws \Throwable
     * @throws InvalidConfigException
     */
    public function process(): TaskResult
    {
        $depbankManual = DepbankManual::findOne([
            'order_no' => $this->orderNo,
        ]);

        if (!$depbankManual) {
            throw new UserException('记录不存在:' . $this->orderNo);
        }

        if ($depbankManual->status !== DepbankManual::STATUS_OA_SUCCESS) {
            throw new UserException('交易状态错误: ' . $depbankManual->status);
        }

        $affect = DepbankManual::updateAll([
            'status' => DepbankManual::STATUS_TRADE_PROCESSING,
            'trade_at' => Carbon::now()->toDateTimeString(),
        ], [
            'order_no' => $this->orderNo,
            'status' => DepbankManual::STATUS_OA_SUCCESS
        ]);

        if ($affect !== 1) {
            throw new UserException('修改交易状态失败!');
        }

        DepbankOrderQuery::make([
            'orderNo' => $this->orderNo,
        ], [
            'task_priority' => Task::PRIORITY_1,
            'task_next_run_date' => Carbon::parse('3 minutes')->toDateTimeString(),
        ]);

        $client = DcsComponent::instance();
        if (
            !$client->depbankOrderApply(
                $this->orderType,
                $this->orderNo,
                $this->outAcctNo,
                $this->outAcctBank,
                $this->inAcctNo,
                $this->inAcctBank,
                $this->amount,
                $this->memo,
                $depbankManual->reserve_type,
            )
        ) {
            throw new UserException('API ERROR: ' . $client->getError());
        }

        return TaskResult::success($client->getResponse());

    }
}