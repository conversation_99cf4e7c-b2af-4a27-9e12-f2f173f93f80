<?php

namespace dcs\tasks;

use Carbon\Carbon;
use Xlerr\CpopIncome\Models\CpopIncomeItem;
use Throwable;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;

class CpopFunderReceivable extends SyncTaskHandler
{
    public string $startDate = '';
    public string $endDate = '';

    public function rules(): array
    {
        return [
            [['startDate', 'endDate'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws Throwable
     */
    public function process(): TaskResult
    {
        $startDate = Carbon::parse($this->startDate)->setTime(0, 0);
        $endDate = Carbon::parse($this->endDate)->setTime(0, 0);
        if ($startDate > $endDate) {
            throw new UserException('开始日期不能大于结束日期');
        }

        $sql = <<<SQL
with statementData as (SELECT ciis.loan_channel,
                              cii.acct_title,
                              if(ciirr.memo like '%延迟三个月%', ciis.date + interval 3 month, ciis.date) as date,
                              ciis.inc_amount
                       FROM cpop_income_item_statement ciis
                                inner join cpop_income_item cii
                                           on ciis.loan_channel = cii.loan_channel and ciis.acct_title = cii.acct_title
                                inner join cpop_income_item_report_rule ciirr
                                           on cii.inc_item_no = ciirr.inc_item_no and ciirr.status = 'valid' and
                                              ciirr.target = 'Bd'
                       WHERE ciis.acct_title in
                             ('ac_technical_service_in1', 'ac_technical_service_in2', 'ac_technical_service_in3',
                              'capital_collected_service_fee', 'capital_collected_consult'))
select loan_channel                                                                   as funder_code,
       ifnull(name, loan_channel)                                                     as funder_name,
       case acct_title
           when 'ac_technical_service_in1' then 'technical_service_in1'
           when 'ac_technical_service_in2' then 'technical_service_in2'
           when 'ac_technical_service_in3' then 'technical_service_in3'
           when 'capital_collected_service_fee' then 'capital_collected_service_fee'
           when 'capital_collected_consult' then 'capital_collected_service_fee1' end as amount_type,
       date,
       sum(inc_amount)                                                                as amount
from statementData
         left join cpm_capital_channel on loan_channel = code
where date >= :startDate
  and date < :endDate + interval 1 day
group by funder_code, amount_type, date;
SQL;

        $data = CpopIncomeItem::getDb()->createCommand($sql, [
            'startDate' => $startDate->toDateString(),
            'endDate' => $endDate->toDateString(),
        ])->queryAll();

        return TaskResult::success($data);
    }
}
