<?php

namespace dcs\tasks;

use Carbon\Carbon;
use Xlerr\SettlementFlow\Models\Order;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;
use yii\db\Exception;

class CpopFunderReceived extends SyncTaskHandler
{
    public string $startDate = '';
    public string $endDate = '';

    public function rules(): array
    {
        return [
            [['startDate', 'endDate'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws Exception
     * @throws UserException
     */
    public function process(): TaskResult
    {
        $startDate = Carbon::parse($this->startDate)->setTime(0, 0);
        $endDate = Carbon::parse($this->endDate)->setTime(0, 0);
        if ($startDate > $endDate) {
            throw new UserException('开始日期不能大于结束日期');
        }

        $sql = <<<SQL
select wa.channel                   as funder_code,
       ifnull(ccc.name, wa.channel) as funder_name,
       fee_type                     as amount_type,
       payment_amount               as amount,
       created_at                   as trade_date,
       0                            as trade_bizid
from cpop_settlement_order wa
         inner join cpop_income_item cii on cii.ms_rule_id = wa.rule_id
         inner join cpop_income_item_report_rule ciirr
                    on ciirr.inc_item_no = cii.inc_item_no and ciirr.status = 'valid' and ciirr.target = 'Bd'
         left join cpm_capital_channel ccc on wa.channel = ccc.code
where wa.inc_type = 'revenue'
  and wa.status = 5
  and wa.payment_channel not in ('repay_charge', 'deposit_charge', 'refund')
  and wa.created_at >= :startDate
  and wa.created_at < :endDate + interval 1 day
SQL;

        $data = Order::getDb()->createCommand($sql, [
            'startDate' => $startDate->toDateString(),
            'endDate' => $endDate->toDateString(),
        ])->queryAll();

        return TaskResult::success($data);
    }
}
