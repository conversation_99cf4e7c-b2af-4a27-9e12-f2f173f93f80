<?php

namespace dcs\procedures;

use capital\StringHelper;
use dcs\procedures\tasks\VirtualReserve;
use RuntimeException;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Procedures\AbstractTransferProcedure;
use xlerr\task\models\Task;

class ReserveVirtual extends AbstractTransferProcedure
{
    public static function key(): string
    {
        return 'virtual_reserve';
    }

    public function name(): string
    {
        return '存管备款至虚户';
    }

    public function data(): array
    {
        return array_replace(parent::data(), [
            'fromAccountType' => 'virtual',
            'toAccountType' => 'virtual',
        ]);
    }

    public function run(Order $order, OrderProcedure $procedure): void
    {
        $serialNo = vsprintf('%s%011d', [
            StringHelper::genUniqueString('VR'),
            $procedure->id
        ]);

        $affected = OrderProcedure::updateAll([
            'serial_no' => $serialNo,
        ], [
            'status' => OrderProcedure::STATUS_PROCESSING,
            'serial_no' => '',
            'id' => $procedure->id,
        ]);
        if ($affected !== 1) {
            throw new RuntimeException('生成订单号失败');
        }
        VirtualReserve::make([
            'procedureId' => $procedure->id,
        ], [
            'task_priority' => Task::PRIORITY_1,
        ]);
    }
}
