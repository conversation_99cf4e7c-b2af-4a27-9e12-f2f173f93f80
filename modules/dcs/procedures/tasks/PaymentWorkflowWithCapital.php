<?php

namespace dcs\procedures\tasks;

use common\components\CapitalComponent;
use common\components\OaComponent;
use kvmanager\KVException;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Models\Rule;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\helpers\Json;

/**
 * 同步付款流程接口
 *
 * @link https://git.kuainiujinke.com/b/capital-docs/-/wikis/apis/BIZ%E7%B3%BB%E7%BB%9F/biz#%E5%8D%81%E5%9B%9B%E5%90%8C%E6%AD%A5%E4%BB%98%E6%AC%BE%E6%B5%81%E7%A8%8B%E6%8E%A5%E5%8F%A3
 */
class PaymentWorkflowWithCapital extends TaskHandler
{
    public $orderId;
    public $procedureId;

    public function rules(): array
    {
        return [
            [['orderId', 'procedureId'], 'required'],
        ];
    }


    /**
     * @return TaskResult
     * @throws InvalidConfigException
     * @throws KVException
     * @throws UserException
     */
    public function process(): TaskResult
    {
        $procedure = OrderProcedure::findOne([
            'id' => $this->procedureId,
            'order_id' => $this->orderId,
        ]);
        if (!$procedure || $procedure->status !== OrderProcedure::STATUS_PROCESSING) {
            return TaskResult::failure('未找到制单流程数据，或流程状态错误');
        }

        $order = $procedure->order;

        $client = OaComponent::instance();
        if (!$client->employeeInfoByEmail($order->operator->email)) {
            throw new UserException($client->getError());
        }

        $employeeInfo = $client->getData();

        $data = [
            'id' => $procedure->id,
            'form_id' => $order->id,
            'funder_code' => $order->channel,
            'amount_type' => $order->fee_type,
            'title' => $order->title,
            'applicant_email' => $order->operator->email,
            'applicant_name' => $order->operator->username,
            'applicant_department' => $employeeInfo['main_department_name'] ?? '运营部',
            'apply_at' => $order->created_at,
            'payer_name' => $procedure->data('fromAccountValue.account_name'),
            'payer_account' => $procedure->data('fromAccountValue.account_card_no'),
            'payer_bank' => $procedure->data('fromAccountValue.account_bank'),
            'payer_branch_bank' => $procedure->data('fromAccountValue.account_subbranch'),
            'payer_branch_bank_city' => $procedure->data('fromAccountValue.account_city'),
            'pay_amount' => $order->payment_amount,
            'payee_name' => $procedure->data('toAccountValue.account_name'),
            'payee_account' => $procedure->data('toAccountValue.account_card_no'),
            'payee_bank' => $procedure->data('toAccountValue.account_bank'),
            'payee_branch_bank' => $procedure->data('toAccountValue.account_subbranch'),
            'payee_branch_bank_city' => $procedure->data('toAccountValue.account_city'),
            'pay_cost_type' => Rule::feeTypeName($order->fee_type),
            'pay_cause' => $procedure->data('commentValue', ''),
            'pay_memo' => $procedure->data('commentValue', ''),
        ];

        $client = CapitalComponent::instance();
        $result = $client->paymentWorkflow($data, $this->makeUniqKey((string)$this->procedureId));
        if (!$result) {
            throw new UserException($client->getError());
        }

        // 记录提交信息
        OrderProcedure::updateAll([
            'serial_no' => $procedure->id,
            'context' => Json::encode([
                'submit' => [
                    'request' => $data,
                    'response' => (string)$client->getRawResponse()->getBody()
                ],
            ])
        ], [
            'id' => $procedure->id,
        ]);

        return TaskResult::success();
    }

    protected function makeUniqKey(string $val): string
    {
        return sprintf('WorkflowApplyProcedure:%s', $val);
    }
}
