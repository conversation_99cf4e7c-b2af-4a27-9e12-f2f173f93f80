<?php

namespace dcs\procedures\tasks;

use Carbon\Carbon;
use common\components\DcsComponent;
use dcs\models\CapitalSettlementRuleConfig;
use dcs\procedures\ReserveVirtual;
use RuntimeException;
use Throwable;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use xlerr\task\models\Task;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;
use yii\helpers\Json;

/**
 * 虚户备款
 */
class VirtualReserve extends TaskHandler
{
    /**
     * 流程id
     *
     * @var int
     */
    public int $procedureId = 0;

    public function rules(): array
    {
        return [
            [['procedureId'], 'required'],
            [['procedureId'], 'integer', 'min' => 1],
        ];
    }

    /**
     * @return TaskResult
     * @throws UserException
     * @throws Throwable
     */
    public function process(): TaskResult
    {
        $procedure = OrderProcedure::findOne([
            'id' => $this->procedureId,
            'type' => ReserveVirtual::key(),
        ]);
        if (!$procedure) {
            return TaskResult::failure('未知流程: ' . $this->procedureId);
        }

        if ($procedure->status !== OrderProcedure::STATUS_PROCESSING) {
            throw new UserException('流程状态错误: ' . $procedure->status);
        }

        if (empty($procedure->serial_no)) {
            throw new UserException('订单号不能为空');
        }

        ReserveQuery::make([
            'procedureId' => $procedure->id,
        ], [
            'task_priority' => Task::PRIORITY_1,
            'task_next_run_date' => Carbon::parse('3 minutes')->toDateTimeString(),
        ]);

        $order = $procedure->order;

        $outAcct = strtolower(vsprintf('%s_%s', [
            $procedure->data('fromAccountValue.card_no'),
            $procedure->data('fromAccountValue.deposit'),
        ]));
        $outAcctBank = $procedure->data('fromAccountValue.deposit');
        $inAcct = strtolower(vsprintf('%s_%s', [
            $procedure->data('toAccountValue.card_no'),
            $procedure->data('toAccountValue.deposit'),
        ]));
        $inAcctBank = $procedure->data('toAccountValue.deposit');
        $amount = $order->payment_amount;
        $memo = vsprintf('%s_%s_%s', [
            strtoupper(CapitalSettlementRuleConfig::findByCode($order->channel)->short_code ?? $order->channel),
            $order->fee_type,
            Carbon::parse($order->created_at)->toDateString(),
        ]);
        $label = $order->fee_type;

        $client = DcsComponent::instance();
        if (
            !$client->depbankOrderApply(
                'internal_transfer',
                $procedure->serial_no,
                $outAcct,
                $outAcctBank,
                $inAcct,
                $inAcctBank,
                $amount,
                $memo,
                $label
            )
        ) {
            throw new UserException('API ERROR: ' . $client->getError());
        }

        $affected = (int)OrderProcedure::updateAll([
            'context' => Json::encode([
                'submit' => [
                    'request' => [
                        'orderType' => 'internal_transfer',
                        'orderNo' => $procedure->serial_no,
                        'outAcctNo' => $outAcct,
                        'outAcctBank' => $outAcctBank,
                        'inAcctNo' => $inAcct,
                        'inAcctBank' => $inAcctBank,
                        'amount' => $order->payment_amount,
                        'memo' => $memo,
                        'label' => $order->fee_type,
                    ],
                    'response' => (string)$client->getRawResponse()->getBody(),
                ]
            ])
        ], [
            'id' => $procedure->id,
        ]);

        if ($affected !== 1) {
            throw new RuntimeException('更新Context失败:' . $procedure->id);
        }

        return TaskResult::success($client->getResponse());
    }
}
