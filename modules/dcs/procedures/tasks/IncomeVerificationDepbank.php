<?php

namespace dcs\procedures\tasks;

use Carbon\Carbon;
use common\components\DcsComponent;
use dcs\models\IncomeVerification;
use RuntimeException;
use Throwable;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use xlerr\task\models\Task;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;
use yii\helpers\Json;

/**
 * 手动核验
 */
class IncomeVerificationDepbank extends TaskHandler
{
    /**
     * 流程id
     *
     * @var int
     */
    public int $procedureId = 0;

    public function rules(): array
    {
        return [
            [['procedureId'], 'required'],
            [['procedureId'], 'integer', 'min' => 1],
        ];
    }

    /**
     * @return TaskResult
     * @throws UserException
     * @throws Throwable
     */
    public function process(): TaskResult
    {
        $procedure = OrderProcedure::findOne([
            'id' => $this->procedureId,
        ]);
        if (!$procedure) {
            return TaskResult::failure('未知流程: ' . $this->procedureId);
        }

        if ($procedure->status !== OrderProcedure::STATUS_PROCESSING) {
            throw new UserException('流程状态错误: ' . $procedure->status);
        }

        if (empty($procedure->serial_no)) {
            throw new UserException('订单号不能为空');
        }

        ReserveQuery::make([
            'procedureId' => $procedure->id,
        ], [
            'task_priority' => Task::PRIORITY_1,
            'task_next_run_date' => Carbon::parse('3 minutes')->toDateTimeString(),
        ]);

        $order = $procedure->order;
        $params = [
            'operator' => $order->created_by,
            'incomeVerificationWay' => IncomeVerification::VERIFICATION_WAY_SETTLEMENT,
            'incomeVerificationId' => $procedure->data('income_verification_id'),
            'loanChannel' => $order->channel,
            'billStartDate' => $order->bill_start_date,
            'billEndDate' => $order->bill_end_date,
            'settlementRuleId' => $order->rule_id,
            'settlementOrderId' => $order->id,
            'depbankLabel' => $order->fee_type,
            'orderNo' => $procedure->serial_no,
        ];

        $client = DcsComponent::instance();
        if (!$client->depbankIncomeVerify($params)) {
            throw new UserException('API ERROR: ' . $client->getError());
        }

        $affected = (int)OrderProcedure::updateAll([
            'context' => Json::encode([
                'submit' => [
                    'request' => $params,
                    'response' => (string)$client->getRawResponse()->getBody(),
                ]
            ])
        ], [
            'id' => $procedure->id,
        ]);

        if ($affected !== 1) {
            throw new RuntimeException('更新Context失败:' . $procedure->id);
        }

        return TaskResult::success($client->getResponse());
    }
}
