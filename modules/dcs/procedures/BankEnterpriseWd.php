<?php

namespace dcs\procedures;

use dcs\procedures\models\BankEnterpriseModel;
use dcs\procedures\tasks\PaymentWorkflowWithCapital;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use xlerr\task\models\Task;

class BankEnterpriseWd extends JointAccountTransfer
{
    public string $modelClass = BankEnterpriseModel::class;

    public static function key(): string
    {
        return 'bank_enterprise';
    }

    public function name(): string
    {
        return 'WD出款(银企直联)';
    }

    public function component(): array
    {
        $components = parent::component();

        array_splice($components, -1, 0, ['keep-payment-voucher']);

        return $components;
    }

    public function run(Order $order, OrderProcedure $procedure): void
    {
        PaymentWorkflowWithCapital::make([
            'orderId' => $order->id,
            'procedureId' => $procedure->id,
        ], [
            'task_priority' => Task::PRIORITY_1,
        ]);
    }
}
