<?php

namespace dcs\procedures;

use cpm\models\CapitalChannel;
use dcs\procedures\approvals\DepositForBIApproval;
use kvmanager\KVException;
use Throwable;
use Xlerr\ApplicationPayment\Interfaces\OaAuditInterface;
use Xlerr\ApplicationPayment\Traits\OaAuditCallbackTrait;
use xlerr\common\helpers\MoneyHelper;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Models\Rule;
use Xlerr\SettlementFlow\Procedures\AbstractProcedure;
use yii\base\UserException;

class BiAudit extends AbstractProcedure implements OaAuditInterface
{
    use OaAuditCallbackTrait;

    public static function key(): string
    {
        return 'bi_audit';
    }

    public function name(): string
    {
        return 'BI审核';
    }

    public function component(): string
    {
        return 'no-configuration';
    }

    /**
     * @param Order $order
     * @param OrderProcedure $procedure
     *
     * @return void
     * @throws Throwable
     * @throws KVException
     * @throws UserException
     */
    public function run(Order $order, OrderProcedure $procedure): void
    {
        $amountTypeName = Rule::feeTypeName($order->fee_type);
        $channelName = CapitalChannel::name($order->channel);
        $approval = new DepositForBIApproval([
            'serial_number' => uuid_create(),
            'procedure_id' => $procedure->id,
            'form_id' => $order->id,
            'funder_name' => $channelName,
            'funder_code' => $order->channel,
            'amount_type' => $order->fee_type,
            'amount_type_name' => $amountTypeName,
            'amount_human' => MoneyHelper::f2y($order->payment_amount, true),
            'amount' => $order->payment_amount,
            'remark' => vsprintf("%s_%s\n%s", [
                $amountTypeName,
                $channelName,
                $order->bankFlow->remark ?? '',
            ]),
        ], [
            'operator' => $order->created_by,
        ]);

        $approval->audit();
    }
}
