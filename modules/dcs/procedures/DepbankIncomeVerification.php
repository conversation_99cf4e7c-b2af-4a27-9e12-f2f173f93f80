<?php

namespace dcs\procedures;

use capital\StringHelper;
use dcs\models\IncomeVerification;
use dcs\procedures\models\DepbankIncomeVerificationModel;
use dcs\procedures\tasks\IncomeVerificationDepbank;
use RuntimeException;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Procedures\AbstractTransferProcedure;
use Xlerr\SettlementFlow\Procedures\ProcedureComment;
use xlerr\task\models\Task;

class DepbankIncomeVerification extends AbstractTransferProcedure
{
    public string $modelClass = DepbankIncomeVerificationModel::class;

    public static function key(): string
    {
        return 'depbank_income_verification';
    }

    public function name(): string
    {
        return 'WD存管收入手动确认';
    }

    public function component(): array
    {
        return [
            'transfer-form',
            'depbank-income-verification'
        ];
    }

    public function data(): array
    {
        return array_replace(parent::data(), [
            'toAccountType' => 'virtual',
        ]);
    }

    public function run(Order $order, OrderProcedure $procedure): void
    {
        $incomeVerification = IncomeVerification::findOne([
            'id' => $procedure->data('income_verification_id'),
        ]);
        if (!$incomeVerification) {
            throw new RuntimeException('流水不存在');
        }

        $affected = OrderProcedure::updateAll([
            'serial_no' => $incomeVerification->depbank_order_no,
        ], [
            'status' => OrderProcedure::STATUS_PROCESSING,
            'serial_no' => '',
            'id' => $procedure->id,
        ]);
        if ($affected !== 1) {
            throw new RuntimeException('生成订单号失败');
        }
        IncomeVerificationDepbank::make([
            'procedureId' => $procedure->id,
        ], [
            'task_priority' => Task::PRIORITY_1,
        ]);
    }
}
