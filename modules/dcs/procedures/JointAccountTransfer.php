<?php

namespace dcs\procedures;

use dcs\procedures\interfaces\JointAccountTransferInterface;
use dcs\procedures\tasks\JointAccountPaymentWorkflowWithCapital;
use dcs\procedures\traits\JatCallbackTrait;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Procedures\AbstractTransferProcedure;
use xlerr\task\models\Task;

class JointAccountTransfer extends AbstractTransferProcedure implements JointAccountTransferInterface
{
    use JatCallbackTrait;

    public static function key(): string
    {
        return 'joint_account_transfer';
    }

    public function name(): string
    {
        return '共管户转账';
    }

    public function run(Order $order, OrderProcedure $procedure): void
    {
        JointAccountPaymentWorkflowWithCapital::make([
            'applyId' => $order->id,
            'procedureId' => $procedure->id,
        ], [
            'task_priority' => Task::PRIORITY_1,
        ]);
    }
}
