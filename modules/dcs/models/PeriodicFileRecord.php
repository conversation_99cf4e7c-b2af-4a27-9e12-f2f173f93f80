<?php

namespace dcs\models;

use Carbon\Carbon;
use Exception;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "periodic_file_record".
 *
 * @property int $periodic_file_record_id
 * @property int $periodic_file_record_rule_id 定期文件规则id
 * @property string $periodic_file_record_loan_channel 资方名称
 * @property string $periodic_file_record_target_channel 目标通道（某个资方、某个担保方）
 * @property string $periodic_file_record_period_type 期类型（D日,M月）
 * @property string $periodic_file_record_file_code 文件类型
 * @property string $periodic_file_record_process_date 预期执行时间
 * @property string $periodic_file_record_business_date 业务日期
 * @property string $periodic_file_record_execute_time 执行推送时间
 * @property string $periodic_file_record_file_digest 文件摘要
 * @property int $periodic_file_record_shard_num 文件分片编号
 * @property string $periodic_file_record_target_file_url 目标文件地址
 * @property string $periodic_file_record_store_file_url 持久化文件地址
 * @property string $periodic_file_record_status 记录状态initial (未处理)、enqueued（已入队）、completed(已完成)、canceled（已取消）
 * @property string|null $periodic_file_record_memo 备注
 * @property int $periodic_file_record_retry_times 重试次数
 * @property int $periodic_file_record_confirm_flag 资方确认标识:0-未确认，1-已确认
 * @property string $periodic_file_record_create_at
 * @property string $periodic_file_record_update_at
 * @property PeriodicFileRule $fileRule
 */
class PeriodicFileRecord extends \yii\db\ActiveRecord
{

    public const STATUS_LIST = [
        'initial' => '未处理',
        'enqueued' => '已入队',
        'completed' => '已完成',
        'canceled' => '已取消',
    ];
    public $pushStartDate;
    public $pushEndDate;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'periodic_file_record';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['pushStartDate', 'pushEndDate'], 'required'],
            [['periodic_file_record_rule_id', 'periodic_file_record_shard_num', 'periodic_file_record_status', 'periodic_file_record_retry_times', 'periodic_file_record_confirm_flag'], 'integer'],
            [['periodic_file_record_period_type', 'periodic_file_record_memo'], 'string'],
            [['periodic_file_record_process_date', 'periodic_file_record_business_date', 'periodic_file_record_execute_time', 'periodic_file_record_create_at', 'periodic_file_record_update_at'], 'safe'],
            [['periodic_file_record_loan_channel', 'periodic_file_record_file_digest'], 'string', 'max' => 64],
            [['periodic_file_record_target_channel', 'periodic_file_record_file_code'], 'string', 'max' => 32],
            [['periodic_file_record_target_file_url', 'periodic_file_record_store_file_url'], 'string', 'max' => 2048],
            [['periodic_file_record_loan_channel', 'periodic_file_record_file_code', 'periodic_file_record_business_date'], 'unique', 'targetAttribute' => ['periodic_file_record_loan_channel', 'periodic_file_record_file_code', 'periodic_file_record_business_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'periodic_file_record_id' => 'ID',
            'periodic_file_record_rule_id' => '定期文件规则id',
            'periodic_file_record_loan_channel' => '资方名称',
            'periodic_file_record_target_channel' => '目标通道',
            'periodic_file_record_period_type' => '期类型',
            'periodic_file_record_file_code' => '文件类型',
            'periodic_file_record_process_date' => '预期执行日期',
            'periodic_file_record_business_date' => '业务日期',
            'periodic_file_record_execute_time' => '执行推送时间',
            'periodic_file_record_file_digest' => '文件摘要',
            'periodic_file_record_shard_num' => '文件分片编号',
            'periodic_file_record_target_file_url' => '目标文件地址',
            'periodic_file_record_store_file_url' => '持久化文件地址',
            'periodic_file_record_status' => '记录状态',
            'periodic_file_record_memo' => '备注',
            'periodic_file_record_retry_times' => '重试次数',
            'periodic_file_record_confirm_flag' => '资方确认标识',
            'periodic_file_record_create_at' => '创建时间',
            'periodic_file_record_update_at' => '最后修改时间',
        ];
    }

    public function push(): bool
    {
        $transaction = self::getDb()->beginTransaction();
        $startDate = Carbon::parse($this->pushStartDate);
        $endDate = Carbon::parse($this->pushEndDate);
        $session = Yii::$app->getSession();
        try {
            $data = [];
            while ($startDate <= $endDate) {
                $data[] = [
                    'periodic_file_record_rule_id' => $this->periodic_file_record_rule_id,
                    'periodic_file_record_loan_channel' => $this->periodic_file_record_loan_channel,
                    'periodic_file_record_target_channel' => $this->periodic_file_record_target_channel,
                    'periodic_file_record_period_type' => $this->periodic_file_record_period_type,
                    'periodic_file_record_file_code' => $this->periodic_file_record_file_code,
                    'periodic_file_record_process_date' => Carbon::now()->toDateString(),
                    'periodic_file_record_business_date' => $startDate->toDateString(),
                    'periodic_file_record_execute_time' => Carbon::now()->toDateTimeString(),
                ];
                $startDate->addDay();
            }
            $hint = self::find()->where([
                'periodic_file_record_loan_channel' => $this->periodic_file_record_loan_channel,
                'periodic_file_record_file_code' => $this->periodic_file_record_file_code])
                ->andWhere(['periodic_file_record_business_date' => array_column($data, 'periodic_file_record_business_date')])
                ->select('periodic_file_record_business_date')
                ->asArray()->all();
            $hint = array_column($hint, 'periodic_file_record_business_date');

            foreach ($data as $key => $item) {
                if (in_array($item['periodic_file_record_business_date'], $hint)) {
                    unset($data[$key]);
                }
            }
            static::getDb()->createCommand()->batchInsert(self::tableName(), [
                'periodic_file_record_rule_id',
                'periodic_file_record_loan_channel',
                'periodic_file_record_target_channel',
                'periodic_file_record_period_type',
                'periodic_file_record_file_code',
                'periodic_file_record_process_date',
                'periodic_file_record_business_date',
                'periodic_file_record_execute_time',
            ], $data)->execute();
            $transaction->commit();
            if ($hint) {
                $session->setFlash('warning', sprintf('以下业务日期已存在推送记录,%s', Json::encode($hint)));
            }
            $session->setFlash('success', '提交成功!');
            return true;
        } catch (Exception $e) {
            $transaction->rollBack();
            $session->setFlash('error', $e->getMessage());
            return false;
        }
    }

    public function getFileRule(): \yii\db\ActiveQuery
    {
        return $this->hasOne(PeriodicFileRule::class, ['period_file_rule_id' => 'periodic_file_record_rule_id']);
    }
}
