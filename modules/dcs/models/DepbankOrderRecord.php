<?php

namespace dcs\models;

use Yii;

/**
 * This is the model class for table "depbank_order_record".
 *
 * @property int $id 主键
 * @property string $order_no 商户订单号
 * @property string $trade_no 请求流水号
 * @property string $flow 流程
 * @property string $status 状态(ready, process, fail, success)
 * @property string $out_order_no 外部流水号
 * @property string $target_acct_bank 目标银行,存管银行或者支付通道QSBANK or CPCN
 * @property string $resp_code 代付响应码
 * @property string $resp_message 代付响应信息
 * @property string $comment 备注
 * @property string $finish_at 完成时间
 * @property string $create_at 创建时间
 * @property string $update_at 更新时间
 */
class DepbankOrderRecord extends \yii\db\ActiveRecord
{
    public const FLOW_PAYSVR_TALLY = 'paysvr_tally';
    public const FLOW_PAYSVR_WITHDRAW = 'paysvr_withdraw';
    public const FLOW_DEPOSIT_INTERNAL_TRANSFER = 'deposit_internal_transfer';
    public const FLOW_DEPOSIT_STATEMENT_MATCH = 'deposit_statement_match';
    public const FLOW_DEPOSIT_TALLY_REVOKE = 'deposit_tally_revoke';
    public const FLOW_DEPOSIT_TALLY = 'deposit_tally';
    public const FLOW_DEPOSIT_TRANSFER_TO_BANKCARD = 'deposit_transfer_to_bankcard';
    public const FLOW_DEPOSIT_WITHDRAW_TO_BANKCARD = 'deposit_withdraw_to_bankcard';
    public const FLOW_LIST = [
        self::FLOW_PAYSVR_TALLY => '支付通道记账',
        self::FLOW_PAYSVR_WITHDRAW => '支付通道提现',
        self::FLOW_DEPOSIT_INTERNAL_TRANSFER => '存管内部转账',
        self::FLOW_DEPOSIT_STATEMENT_MATCH => '存管入金流水确认',
        self::FLOW_DEPOSIT_TALLY_REVOKE => '存管记账撤销',
        self::FLOW_DEPOSIT_TALLY => '存管记账',
        self::FLOW_DEPOSIT_TRANSFER_TO_BANKCARD => '存管对公打款',
        self::FLOW_DEPOSIT_WITHDRAW_TO_BANKCARD => '提现至该账户同名实卡',
    ];


    public const READY = 'ready';
    public const PROCESSING = 'processing';
    public const SUCCESSFUL = 'successful';
    public const FAIL = 'failed';

    public const STATUS_LIST = [
        self::READY => '待处理',
        self::PROCESSING => '处理中',
        self::SUCCESSFUL => '成功',
        self::FAIL => '失败',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'depbank_order_record';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbCapital');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['finish_at', 'create_at', 'update_at'], 'safe'],
            [['order_no', 'trade_no', 'target_acct_bank'], 'string', 'max' => 64],
            [['flow', 'status'], 'string', 'max' => 48],
            [['out_order_no'], 'string', 'max' => 512],
            [['resp_code', 'resp_message', 'comment'], 'string', 'max' => 256],
            [['trade_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'order_no' => '商户订单号',
            'trade_no' => '请求流水号',
            'flow' => '流程',
            'status' => '状态',
            'out_order_no' => '外部流水号',
            'target_acct_bank' => '目标银行',
            'resp_code' => '代付响应码',
            'resp_message' => '代付响应信息',
            'comment' => '备注',
            'finish_at' => '完成时间',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
        ];
    }
}
