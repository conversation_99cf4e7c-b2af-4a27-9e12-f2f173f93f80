<?php

namespace dcs\models;

use Carbon\Carbon;
use Xlerr\CpopIncome\Models\CpopIncomeItem;
use Xlerr\CpopIncome\Models\CpopIncomeItemStatement;
use cpm\models\CapitalChannel;
use dashboard\grid\MoneyTotalDataColumn;
use yii\base\Model;
use yii\data\SqlDataProvider;
use yii\helpers\ArrayHelper;

class IncStatementSearch extends Model
{
    public $incType;
    public $channel;
    public $startDate;
    public $endDate;

    public function rules(): array
    {
        return [
            [['channel'], 'default', 'value' => ''],
            [['startDate'], 'default', 'value' => Carbon::parse('30 days ago')->toDateString()],
            [['endDate'], 'default', 'value' => Carbon::yesterday()->toDateString()],
        ];
    }

    public function formName(): string
    {
        return '';
    }

    public function attributeLabels(): array
    {
        return [
            'channel' => '资金方',
            'startDate' => '开始日期',
            'endDate' => '结束日期',
        ];
    }

    /**
     * @param string $incType
     *
     * @return array
     */
    public static function channels(string $incType): array
    {
        $channelCodes = CpopIncomeItem::find()
            ->where([
                'and',
                ['=', 'inc_type', $incType],
                ['>', 'ms_rule_id', 0],
                ['!=', 'acct_title', 'deposit'],
                ['=', 'status', 'valid']
            ])
            ->select('loan_channel')
            ->orderBy(['id' => SORT_DESC])
            ->distinct()
            ->column();

        return ArrayHelper::filter(CapitalChannel::list(), $channelCodes);
    }

    /**
     * @param array $params
     *
     * @return array
     */
    public function search(array $params): array
    {
        $this->load($params);
        $this->validate();

        $channels = self::channels($this->incType);
        if (empty($this->channel)) {
            $this->channel = array_key_first($channels);
        }

        $types = CpopIncomeItem::find()
            ->alias('inc')
            ->joinWith('calcRules rule')
            ->where([
                'inc.loan_channel' => $this->channel,
                'inc.inc_type' => $this->incType,
                'inc.status' => 'valid',
            ])
            ->andWhere([
                'and',
                ['>', 'inc.ms_rule_id', 0],
                ['!=', 'inc.acct_title', 'deposit'],
            ])
            ->select([
                'inc.acct_title',
                'inc.acct_title_desc',
                'rule.calc_group',
                'rule.formula_desc',
            ])
            ->groupBy([
                'inc.acct_title',
                'rule.calc_group'
            ])
            ->orderBy([
                'inc.acct_title' => SORT_ASC,
                'rule.calc_group' => SORT_ASC
            ])
            ->asArray()
            ->all();

        $selects = [];
        $columns = [
            [
                'label' => '日期',
                'attribute' => 'date',
                'footer' => '合计',
            ],
            [
                'label' => '资金方',
                'attribute' => 'loan_channel',
                'format' => ['in', $channels],
                'footer' => '-',
            ],
        ];
        foreach ($types as $item) {
            $label = sprintf('%s(%s)', $item['acct_title_desc'], $item['calc_group']);
            $attribute = md5(sprintf('%s~%s', $item['acct_title'], $item['calc_group']));
            $selects[] = vsprintf(
                'sum(if(inc.acct_title = \'%s\' and rule.calc_group = \'%s\', stts.inc_amount, 0)) as `%s`',
                [
                    $item['acct_title'],
                    $item['calc_group'],
                    $attribute,
                ]
            );

            $columns[] = [
                'label' => $label,
                'attribute' => $attribute,
                'class' => MoneyTotalDataColumn::class,
            ];
        }

        $query = CpopIncomeItemStatement::find()
            ->alias('stts')
            ->joinWith('incomeItem inc', false)
            ->joinWith('incomeItemCalcRule rule', false)
            ->where([
                'inc.loan_channel' => $this->channel,
                'inc.inc_type' => $this->incType,
                'inc.status' => 'valid',
            ])
            ->andWhere([
                'and',
                ['>', 'inc.ms_rule_id', 0],
                ['!=', 'inc.acct_title', 'deposit'],
                ['>=', 'stts.date', $this->startDate],
                ['<', 'stts.date', Carbon::parse($this->endDate)->addDay()->toDateString()],
            ])
            ->select([
                'inc.loan_channel',
                'stts.date',
            ])
            ->addSelect($selects)
            ->groupBy('stts.date');

        $command = $query->createCommand();

        return [
            'dataProvider' => new SqlDataProvider([
                'sql' => $command->sql,
                'params' => $command->params,
                'db' => CpopIncomeItem::getDb(),
                'sort' => [
                    'attributes' => ['date'],
                    'defaultOrder' => ['date' => SORT_ASC],
                ],
            ]),
            'columns' => $columns,
            'channels' => $channels,
        ];
    }
}
