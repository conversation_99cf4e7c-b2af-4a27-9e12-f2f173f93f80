<?php

namespace dcs\models;

use Carbon\Carbon;
use common\models\ReportCapital;
use kvmanager\KVException;
use yii\db\ActiveQuery;
use yii\db\Expression;

/**
 * Class CapitalFlowEverydayAccountBalanceSearch
 *
 * @package dcs\models
 */
class CapitalFlowEverydayAccountBalanceSearch extends ReportCapital
{
    /**
     * @var Carbon|string
     */
    public $startDate;

    /**
     * @var Carbon|string
     */
    public $endDate;


    /**
     * @var string
     */
    public $channel;

    /**
     * @return string
     */
    public function formName(): string
    {
        return '';
    }

    /**
     * @inheritDoc
     */
    public function rules(): array
    {
        return [
            [['startDate', 'endDate'], 'default', 'value' => Carbon::yesterday()->toDateString()],
            [['channel'], 'string'],
        ];
    }

    /**
     * @return array
     * @throws KVException
     */
    public static function accountList(): array
    {
        return [
            'ht_qianjingjing' => '钱京京华通',
            'ht_tengqiao' => '腾桥华通',
            'miyang' => '觅飏济宁',
            'jining' => '腾桥济宁',
            'qs_tengqiao' => '腾桥齐商',
            'zz_tengqiao' => '腾桥枣庄',
            'qs_qianjingjing' => '钱京京齐商',
        ];
    }


    /**
     * @return string[]
     *
     * @psalm-return array{0: 'beginning_balance', 1: 'ending_balance', 2: 'day_in_balance', 3: 'day_out_balance'}
     */
    public static function getTypes(): array
    {
        return [
            'beginning_balance',
            'ending_balance',
            'day_in_balance',
            'day_out_balance',
        ];
    }

    /**
     * @param array $params
     *
     * @return ActiveQuery
     * @throws KVException
     */
    public function search(array $params): ActiveQuery
    {
        $this->load($params);
        $this->validate();
        $query = self::find();
        if (empty($this->channel)) {
            $query->where([
                'channel' => array_keys(self::accountList()),
            ]);
        } else {
            $query->where([
                'channel' => $this->channel,
            ]);
        }

        $selects = ['date', 'channel'];
        foreach (self::getTypes() as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0))', $type));
        }
        $query
            ->andWhere(['type' => self::getTypes()])
            ->andFilterWhere([
                'channel' => $this->channel,
            ])->andFilterWhere([
                'and',
                ['>=', 'date', $this->startDate],
                ['<', 'date', Carbon::parse($this->endDate)->addDay()->toDateString()],
            ])
            ->select($selects)
            ->groupBy(['date', 'channel']);

        return $query;
    }
}
