<?php

namespace dcs\models;

use common\traits\PrivacyProtectedTrait;
use waterank\audit\models\Audit;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%offline_fee_datasource}}".
 *
 * @property int $id
 * @property string $data_source_name
 * @property string data_source
 * @property string data_source_sql
 * @property string $create_user
 * @property string $create_at
 * @property string $update_at
 * @property int $version
 */
class OfflineFeeDatasource extends ActiveRecord
{
    use PrivacyProtectedTrait;

    /** @var Audit */
    public $audit;

    /**
     * @return string
     */
    public static function tableName(): string
    {
        return '{{%offline_fee_datasource}}';
    }

//    public function optimisticLock(): ?string
//    {
//        return 'version';
//    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'data_source_name',
                    'data_source',
                    'data_source_sql',
                ],
                'required',
            ],
            [['data_source_name', 'data_source_sql', 'create_user'], 'string'],
            [['create_at', 'update_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id' => '编号',
            'data_source_name' => '名称',
            'data_source' => '数据源类型',
            'data_source_sql' => 'sql',
            'create_user' => '添加人',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
        ];
    }

    public static function findAllAndGroupByDataSourceName(): array
    {
        $query = OfflineFeeDatasource::find();
        $all = $query->all();
        $result = [];
        foreach ($all as $datasource) {
            $result[$datasource->id] = $datasource->data_source_name;
        }
        return $result;
    }

}
