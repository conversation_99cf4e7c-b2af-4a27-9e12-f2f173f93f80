<?php

namespace dcs\models;

use common\models\Card;
use xlerr\desensitise\Desensitise;
use Yii;
use yii\data\ActiveDataProvider;

use function xlerr\desensitise\encrypt;

/**
 * CardSearch represents the model behind the search form about `common\models\Card`.
 */
class CardSearch extends Card
{
    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['card_user_name_encrypt', 'card_num_encrypt'], 'trim'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = Card::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'attributes'   => ['card_id'],
                'defaultOrder' => ['card_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);
        $this->validate();

        $rejected = function ($response) {
            Yii::$app->getSession()->addFlash('error', '加密错误: ' . ($response['message'] ?? ''));
        };

        $username = '';
        if ($this->card_user_name_encrypt) {
            $username = encrypt($this->card_user_name_encrypt, Desensitise::TYPE_NAME, 0, $rejected)->hash;
        }

        $cardNum = '';
        if ($this->card_num_encrypt) {
            $cardNum = encrypt($this->card_num_encrypt, Desensitise::TYPE_NAME, 0, $rejected)->hash;
        }

        $query->andFilterWhere([
            'card_num_encrypt'       => $cardNum,
            'card_user_name_encrypt' => $username,
        ]);

        return $dataProvider;
    }
}
