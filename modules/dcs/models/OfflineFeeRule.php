<?php

namespace dcs\models;

use common\traits\PrivacyProtectedTrait;
use waterank\audit\models\Audit;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%offline_fee_rule}}".
 *
 * @property int $id
 * @property string $asset_loan_channel
 * @property string $fee_type
 * @property string $fee_type_desc
 * @property string $sub_fee_type
 * @property string $income_expense_type
 * @property float $rate
 * @property string $calculation_type
 * @property string $calculation_desc
 * @property string $payment_mode
 * @property int $data_source_id
 * @property string $data_source
 * @property string $data_source_sql
 * @property string $start_date
 * @property string $end_date
 * @property string $status
 * @property string $need_up
 * @property string $comment
 * @property string $create_user
 * @property string $create_at
 * @property string $update_at
 * @property int $version
 */
class OfflineFeeRule extends ActiveRecord
{
    use PrivacyProtectedTrait;

    /** @var Audit */
    public $audit;

    public const STATUS_ACTIVE = 'valid';
    public const STATUS_NEGATIVE = 'void';

    public static array $statusList = [
        'valid' => 'valid',
        'void' => 'void',
    ];

    /**
     * @return string
     */
    public static function tableName(): string
    {
        return '{{%offline_fee_rule}}';
    }

    public function optimisticLock(): ?string
    {
        return 'version';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'fee_type',
                    'sub_fee_type',
                    'income_expense_type',
                    'rate',
                    'calculation_type',
                    'calculation_desc',
                    'payment_mode',
                    'start_date',
                    'need_up',
                ],
                'required',
            ],
            [
                ['income_expense_type'],
                function () {
                    if ($this->need_up == 'N') {
                        return;
                    }
                    $query = OfflineFeeRule::find();
                    $count = $query
                        ->andFilterWhere(['=', 'fee_type', $this->fee_type])
                        ->andFilterWhere(['!=', 'income_expense_type', $this->income_expense_type])
                        ->andFilterWhere(['=', 'income_expense_type', 'Y'])
                        ->count();
                    if ($count > 0) {
                        $this->addError('income_expense_type', '相同费用类型不允许同时存在收入和支出');
                    }
                },
            ],
            [['rate', 'data_source_id'], 'number'],
            [['data_source_sql'], 'string'],
            [['start_date', 'end_date', 'create_at', 'update_at'], 'safe'],
            [
                ['asset_loan_channel', 'fee_type', 'fee_type_desc', 'sub_fee_type', 'calculation_type'],
                'string',
                'max' => 64,
            ],
            [['calculation_desc'], 'string', 'max' => 255],
            [['payment_mode', 'data_source', 'status'], 'string', 'max' => 16],
            [['comment', 'create_user'], 'string', 'max' => 100],
            [
                ['asset_loan_channel', 'sub_fee_type', 'start_date'],
                'unique',
                'targetAttribute' => ['asset_loan_channel', 'sub_fee_type', 'start_date'],
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id' => '编号',
            'asset_loan_channel' => '资金方',
            'fee_type' => '费用类型',
            'fee_type_desc' => '费用类型描述',
            'sub_fee_type' => '子费用类型',
            'income_expense_type' => '收支类型',
            'rate' => '比例/单价',
            'calculation_type' => '计算类型',
            'calculation_desc' => '计算公式描述',
            'payment_mode' => '缴费方式',
            'data_source' => '数据源类型',
            'data_source_id' => '关联数据源',
            'data_source_sql' => '数据源sql',
            'start_date' => '规则开始生效日期',
            'end_date' => '规则生效结束日期',
            'status' => '状态',
            'comment' => '备注',
            'need_up' => '是否需要上报',
            'create_user' => '添加人',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
        ];
    }

    public static function incomeExpenseTypeList(): array
    {
        return [
            'income' => '收入',
            'expense' => '支出',
        ];
    }

    public static function needUpList(): array
    {
        return [
            'Y' => '是',
            'N' => '否',
        ];
    }

    public static function feeTypeList(): array
    {
        $result = [];
        $groupByInfo = self::find()->select('fee_type')->groupBy('fee_type')->column();
        foreach ($groupByInfo as $item) {
            $result[$item] = $item;
        }

        return $result;
    }

    public static function subFeeTypeList(): array
    {
        $result = [];
        $groupByInfo = self::find()->select('sub_fee_type')->groupBy('sub_fee_type')->column();
        foreach ($groupByInfo as $item) {
            $result[$item] = $item;
        }

        return $result;
    }

    public static function channelList(): array
    {
        $result = [];
        $groupByInfo = self::find()->select('asset_loan_channel')->groupBy('asset_loan_channel')->column();
        foreach ($groupByInfo as $item) {
            $result[$item] = $item;
        }

        return $result;
    }

    public function getOfflineFeeDatasource()
    {
        return $this->hasOne(OfflineFeeDatasource::class, ['id' => 'data_source_id']);
    }
}
