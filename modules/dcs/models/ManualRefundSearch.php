<?php

namespace dcs\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * ManualRefundSearch represents the model behind the search form about `dcs\models\ManualRefund`.
 */
class ManualRefundSearch extends ManualRefund
{
    public $startDate;
    public $endDate;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['endDate'], 'default', 'value' => Carbon::today()->toDateString()],
            [['startDate'], 'default', 'value' => Carbon::parse('6 days ago')->toDateString()],
            [
                [
                    'asset_item_no',
                    'receiver_phone',
                    'receiver_id_num',
                    'refund_status',
                    'source_system'
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ManualRefund::find()->with(['asset']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => [
                    'id',
                ],
                'defaultOrder' => [
                    'id' => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andWhere([
            'and',
            ['>=', 'refund_create_at', $this->startDate],
            ['<', 'refund_create_at', Carbon::parse($this->endDate)->addDay()->toDateString()],
        ]);

        // grid filtering conditions
        $query->andFilterWhere([
            'asset_item_no' => $this->asset_item_no,
            'receiver_phone' => $this->receiver_phone,
            'receiver_id_num' => $this->receiver_id_num,
            'refund_status' => $this->refund_status,
            'source_system' => $this->source_system
        ]);

        return $dataProvider;
    }
}
