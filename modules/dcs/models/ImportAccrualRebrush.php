<?php

namespace dcs\models;

use common\consts\Consts;
use common\models\User;
use dcs\tasks\AccrualRebrushNotify;
use Generator;
use Throwable;
use Yii;
use yii\base\UserException;
use yii\db\Exception;
use yii\web\UploadedFile;

/**
 * Class ImportAccrualRebrush
 *
 * @package dcs\models
 */
class ImportAccrualRebrush extends CleanAccrualRebrush
{
    /**
     * @var UploadedFile
     */
    public $file;

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['file'], 'required'],
            [
                ['file'],
                'file',
                'extensions'               => ['csv'],
                'maxSize'                  => 50 * 1024 * 1024,
                'checkExtensionByMimeType' => false,
            ],
            [
                ['file'],
                'filter',
                'filter' => function (UploadedFile $file) {
                    if (self::find()->where(['batch_no' => $file->getBaseName()])->exists()) {
                        $this->addError('file', '不能多次导入同一文件');
                    }

                    return $file;
                },
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'file' => '文件',
        ];
    }

    /**
     * @param UploadedFile $file
     *
     * @return Generator
     * @throws UserException
     */
    public function parseFile(UploadedFile $file)
    {
        $f = @fopen($file->tempName, 'r');
        if (!$f) {
            throw new UserException('打开文件失败');
        }

        fgetcsv($f);

        while (!feof($f) && $row = fgetcsv($f)) {
            yield $row;
        }

        fclose($f);
    }

    /**
     * @param $data
     *
     * @return int
     * @throws Exception
     */
    public function batchWrite($data)
    {
        if ($data) {
            return static::getDb()->createCommand()->batchInsert(self::tableName(), [
                'asset_item_no',
                'period',
                'new_tech_account',
                'new_rate',
                'operator',
                'batch_no',
            ], $data)->execute();
        }

        return 0;
    }

    /**
     * @throws Throwable
     */
    public function import(): void
    {
        $total = 0;
        $batch = [];
        /** @var User $user */
        $user    = Yii::$app->getUser()->getIdentity();
        $batchNo = $this->file->getBaseName();

        $session = Yii::$app->getSession();

        $transaction = self::getDb()->beginTransaction();
        try {
            foreach ($this->parseFile($this->file) as $i => $row) {
                $row = array_filter(array_map('trim', $row), function ($val) {
                    return strlen($val);
                });
                if (empty($row)) {
                    continue;
                }
                if (count($row) !== 4 || intval($row[1]) === 0 || $row[3] < 0 || $row[3] > 1) {
                    throw new UserException(sprintf('数据异常，文件第%d行', $i + 2));
                }
                $row[] = $user->username;
                $row[] = $batchNo;

                $batch[] = $row;

                if ($i % 1000 === 0) {
                    $total += $this->batchWrite($batch);
                    $batch = [];
                }
            }

            $total += $this->batchWrite($batch);

            if ($total) {
                AccrualRebrushNotify::make([
                    'from_system' => Consts::SYSTEM_NAME_BIZ,
                    'key'         => 'BIZ' . Yii::$app->getSecurity()->generateRandomString(20),
                    'type'        => 'AccrualReBrush',
                    'data'        => [
                        'batch_no' => $batchNo,
                    ],
                ]);
            }

            $transaction->commit();

            $session->setFlash('success', sprintf('导入%d条数据', $total));
        } catch (Throwable $e) {
            $transaction->rollBack();
            $session->setFlash('error', $e->getMessage());
        }
    }
}
