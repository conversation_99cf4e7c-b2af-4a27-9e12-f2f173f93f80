<?php

namespace dcs\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * Class CleanAccrualMonthLog
 *
 * @package dcs\models
 */
class CleanAccrualMonthLog extends ActiveRecord
{
    /**
     * @return string
     */
    public static function tableName(): string
    {
        return 'clean_accrual_month_log';
    }

    /**
     * @return Connection
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCapital');
    }

    /**
     * @inheritDoc
     */
    public function rules(): array
    {
        return [

        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [];
    }
}
