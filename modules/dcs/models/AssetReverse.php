<?php

namespace dcs\models;

use Carbon\Carbon;
use common\models\Asset;
use common\models\CapitalAsset;
use dcs\tasks\AssetReversePushGBiz;
use Exception;
use Throwable;
use xlerr\task\models\Task;
use Yii;
use yii\base\UserException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "asset_reverse".
 *
 * @property int                      $id
 * @property string                   $asset_item_no
 * @property int                      $period_count
 * @property string                   $loan_channel
 * @property string                   $memo
 * @property string                   $create_at
 * @property string                   $update_at
 * @property string                   $callback_at
 * @property string                   $status
 * @property-read  CapitalAsset       $capitalAsset
 * @property-read  AssetReverseTran[] $trans
 */
class AssetReverse extends ActiveRecord
{
    public const STATUS_ENTERING     = 'entering';
    public const STATUS_FAIL         = 'fail';
    public const STATUS_GBIZ_RECEIVE = 'gbiz_receive';
    public const STATUS_NEED_CONFIRM = 'need_confirm';
    public const STATUS_SUCCESS      = 'success';

    /**
     * @return string[]
     *
     * @psalm-return array{entering: '待处理', fail: '失败', gbiz_receive: '待资方结清', need_confirm: '待确认', success: '已结清'}
     */
    public static function statusList(): array
    {
        return [
            self::STATUS_ENTERING     => '待处理',
            self::STATUS_FAIL         => '失败',
            self::STATUS_GBIZ_RECEIVE => '待资方结清',
            self::STATUS_NEED_CONFIRM => '待确认',
            self::STATUS_SUCCESS      => '已结清',
        ];
    }

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'asset_reverse';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'asset_item_no',
                    'loan_channel',
                    'status',

                ],
                'trim',
            ],
            [['asset_item_no', 'period_count', 'loan_channel', 'status'], 'required'],
            [['asset_item_no'], 'string', 'max' => 64],
            [['loan_channel'], 'string', 'max' => 32],
            [['status'], 'string', 'max' => 16],
            [['memo'], 'string', 'max' => 255],
            [['period_count'], 'integer'],
            [
                [
                    'create_at',
                    'update_at',
                    'callback_at',
                ],
                'safe',
            ],
            [
                ['asset_item_no'],
                'unique',
                'targetAttribute' => ['loan_channel', 'asset_item_no'],
                'message'         => '{values}已存在!',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id'            => 'ID',
            'asset_item_no' => '资产编号',
            'period_count'  => '总期次',
            'loan_channel'  => '资金方',
            'memo'          => '备注',
            'create_at'     => '创建时间',
            'update_at'     => '更新时间',
            'callback_at'   => '回调时间',
            'status'        => '状态',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getCapitalAsset(): ActiveQuery
    {
        return $this->hasOne(CapitalAsset::class, [
            'capital_asset_item_no' => 'asset_item_no',
            'capital_asset_channel' => 'loan_channel',
        ]);
    }

    /**
     * @return ActiveQuery
     */
    public function getTrans(): ActiveQuery
    {
        return $this->hasMany(AssetReverseTran::class, ['asset_reverse_id' => 'id']);
    }

    /**
     * @param $itemNoList
     *
     * @return bool
     * @throws \yii\db\Exception
     */
    public function check($itemNoList): bool
    {
        $error = [];
        if (empty($itemNoList)) {
            $error[] = '请输入资产编号';
        } else {
            $sql = <<<SQL
SELECT sum(`dtransaction_repaid_amount_f`) + sum(`ftransaction_repaid_amount_f`)
FROM `asset`
         INNER JOIN `dtransaction` ON `dtransaction_asset_id` = `asset_id` AND `dtransaction_period` > 0
         INNER JOIN `ftransaction` ON `ftransaction_asset_id` = `asset_id`
WHERE `asset_item_no` = :assetItemNo
SQL;
            foreach ($itemNoList as $itemNo) {
                /** @var Asset $asset */
                $asset = Asset::find()->where(['asset_item_no' => $itemNo])->one();
                if (!$asset) {
                    $error[] = $itemNo . ': 资产不存在';
                    continue;
                }
                if ($asset->asset_status !== Asset::STATUS_REPAY) {
                    $error[] = $itemNo . ': 资产状态异常';
                    continue;
                }


                $repayAmount = (int)Asset::getDb()->createCommand($sql, ['assetItemNo' => $itemNo])->queryScalar();
                if ($repayAmount > 0) {
                    $error[] = $itemNo . ': 存在还款数据';
                }
            }
        }

        if (!empty($error)) {
            $this->addError('asset_item_no', implode(', ', $error));

            return false;
        }

        return true;
    }

    /**
     * @return bool
     * @throws Throwable
     * @throws \yii\db\Exception
     */
    public function import()
    {
        $itemNoList = array_map('trim', explode("\n", $this->asset_item_no));
        $itemNoList = array_unique(array_filter($itemNoList));
        if (!$this->check($itemNoList)) {
            return false;
        }
        $transaction = self::getDb()->beginTransaction();
        try {
            foreach ($itemNoList as $itemNo) {
                $asset             = Asset::findOne(['asset_item_no' => $itemNo]);
                $ar                = new self();
                $ar->asset_item_no = $itemNo;
                $ar->period_count  = $asset->asset_period_count;
                $ar->loan_channel  = $asset->asset_loan_channel;
                $ar->status        = self::STATUS_ENTERING;
                $ar->create_at     = Carbon::now()->toDateString();
                if (!$ar->insert()) {
                    if ($ar->hasErrors()) {
                        throw new UserException(implode(', ', $ar->getErrors('asset_item_no')));
                    }
                    throw new UserException('保存失败');
                }
            }

            AssetReversePushGBiz::make($itemNoList, [
                'task_priority' => Task::PRIORITY_1,
            ]);

            $transaction->commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollBack();
            Yii::error($e->getMessage());
            $this->addError('asset_item_no', $e->getMessage());

            return false;
        }
    }
}
