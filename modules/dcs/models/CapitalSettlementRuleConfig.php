<?php

namespace dcs\models;

use common\models\User;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use yii\behaviors\BlameableBehavior;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "capital_settlement_rule_config".
 *
 * @property int $id
 * @property string $asset_loan_channel 机构
 * @property string $short_code 简码
 * @property string $admin_channel 管理机构
 * @property int $channel_status 机构状态 0:待灰度 1:已灰度未放量 2:放量中 3:存量资产 4:已到期未结清 5:已结清
 * @property string $grant_start_at 起量时间
 * @property string $min_due_at 最早到期日
 * @property string $max_due_at 最晚到期日
 * @property string $min_compensate_at 最早代偿日
 * @property string $settlement_mode 结算方式 day:每日预充 week:每周预充 holiday:节假日预充
 * @property int|null $compensate_settlement_rule 代偿规则
 * @property string $compensate_settlement_time 代偿时间
 * @property int|null $compensate_remit_rule 代偿打款规则
 * @property int|null $buyback_rule_day 连续逾期天数
 * @property int|null $buyback_rule_period 连续逾期期数
 * @property int|null $buyback_rule_period_total 累计逾期期数
 * @property int|null $buyback_rule_period_adv 提前回购期数
 * @property int|null $buyback_settlement_rule 回购宽限期
 * @property int|null $buyback_remit_rule 回购打款规则
 * @property int|null $switch_us_day 切我方日期
 * @property string $switch_us_time 切我方时间
 * @property int|null $offline_repay_remit_rule 线下还款打款规则
 * @property string $remit_rule 打款规则
 * @property string|null $desc 补充说明
 * @property int $create_user 创建人
 * @property int $update_user 修改人
 * @property string $create_at 创建时间
 * @property string $update_at 更新时间
 * @property string $bd_income_fees BD收入费用类型字段,技术服务费收入1/技术服务费收入2/资方代收服务费/资方代收代偿后还款
 * @property int $funding_nature 机构性质(1.自营 2. 外部资方)
 * @property string $offline_repay_method 线下还款方式(not_support:未支持,capital_withhold:资方通道,our_withhold: 我方通道)
 * @property string $weidu_income_refund WD收入回款方式：1.通道自动结算到实卡、2.资方打款至虚户、3.资方打款至实卡、4.我方代付至虚户
 * @property int $grant_date_onloan_report '是否开启按放款日在贷报表：0-不开启，1-开启',
 * @property int $capital_withhold_report '是否开启资方扣款报表：0-不开启，1-开启'
 * @property int $enable_ab_prin_via_abp_report '是否开启按权责期次每日权责本金报表：0-不开启，1-开启'
 * @property int $granted_with_rateno_report '是否开启每日放款(按费率编号分类)报表：0-不开启，1-开启'
 * @property int $ab_period_amount_with_gd_report '是否开启每日权责期次本金利息(按放款日分类)报表：0-不开启，1-开启'
 * @property int $is_allocation 是否分摊，0不分摊，1分摊
 * @property int $company_id 公司ID
 * @property string $company_name 公司名称
 * @property string $biz_region 公司名称
 */
class CapitalSettlementRuleConfig extends ActiveRecord
{

    public const CHANNEL_STATUS_0 = 0;
    public const CHANNEL_STATUS_1 = 1;
    public const CHANNEL_STATUS_2 = 2;
    public const CHANNEL_STATUS_3 = 3;
    public const CHANNEL_STATUS_4 = 4;
    public const CHANNEL_STATUS_5 = 5;
    public const CHANNEL_STATUS_LIST = [
        self::CHANNEL_STATUS_0 => '待灰度',
        self::CHANNEL_STATUS_1 => '已灰度未放量',
        self::CHANNEL_STATUS_2 => '放量中',
        self::CHANNEL_STATUS_3 => '存量资产',
        self::CHANNEL_STATUS_4 => '已到期未结清',
        self::CHANNEL_STATUS_5 => '已结清',
    ];

    public const ALLOCATION_NON_ALLOCATABLE = 0; //无需分摊
    public const ALLOCATION_ALLOCATABLE = 1;//需要分摊
    public const ALLOCATION_MAP = [
     self::ALLOCATION_NON_ALLOCATABLE => '否',
     self::ALLOCATION_ALLOCATABLE => '是',
    ];



    public const WD_INCOME_REFUND_LIST = [
        1 => '通道自动结算到实卡',
        2 => '资方打款至虚户',
        3 => '资方打款至实卡',
        4 => '我方代付至虚户',
    ];


    public const REMIT_RULE_LIST = [
        'D' => '自然日',
        'T' => '工作日',
    ];

    public const OFFLINE_REPAY_METHOD_LIST = [
        'not_support' => '未支持',
        'capital_withhold' => '资方通道',
        'our_withhold' => '我方通道',
    ];

    public const NATURE_SELF_OPERATED = 1;
    public const NATURE_OTHER = 2;
    public const NATURE_BAOLI = 3;
    public const NATURE_GUARANTEE_COMPANY = 5; //担保公司
    public const NATURE_TECH_COMPANY = 6; //科技公司
    public const NATURE_PAYMENT_CHANNEL = 7; //支付通道
    public const NATURE_DEPOSIT = 8; //存管银行

    public const NATURE_LIST = [
        self::NATURE_SELF_OPERATED => '自营资方',
        self::NATURE_OTHER => '外部资方',
        self::NATURE_BAOLI => '保理资方',
        self::NATURE_GUARANTEE_COMPANY => '担保公司',
        self::NATURE_TECH_COMPANY => '科技公司',
        self::NATURE_PAYMENT_CHANNEL => '支付通道',
        self::NATURE_DEPOSIT => '存管银行',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'capital_settlement_rule_config';
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'create_user',
                'updatedByAttribute' => 'update_user',
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'asset_loan_channel',
                    'short_code',
                    'channel_status',
                    'settlement_mode',
                    'remit_rule',
                    'funding_nature',
                    'buyback_rule_period_adv',
                    'grant_date_onloan_report',
                    'capital_withhold_report',
                    'enable_ab_prin_via_abp_report',
                    'granted_with_rateno_report',
                    'ab_period_amount_with_gd_report',
                    'biz_region',
                ],
                'required',
            ],
            [
                ['admin_channel'],
                'filter',
                'filter' => function ($val) {
                    if (empty($val)) {
                        return $this->asset_loan_channel;
                    }

                    return $val;
                }
            ],
            [['grant_start_at', 'min_due_at', 'max_due_at', 'min_compensate_at'], 'date', 'format' => 'Y-m-d'],
            [
                ['compensate_settlement_time', 'switch_us_time'],
                'match',
                'pattern' => '/^(\d{2}:){2}\d{2}$/',
                'message' => '格式错误, 示例: 08:00:00, 14:30:00',
            ],
            [
                ['create_at', 'update_at', 'fees', 'funding_nature', 'offline_repay_method', 'weidu_income_refund'],
                'safe',
            ],
            [
                [
                    'compensate_settlement_rule',
                    'buyback_rule_day',
                    'buyback_rule_period',
                    'buyback_rule_period_total',
                    'create_user',
                    'update_user',
                    'buyback_rule_period_adv',
                    'grant_date_onloan_report',
                    'capital_withhold_report',
                    'enable_ab_prin_via_abp_report',
                    'granted_with_rateno_report',
                    'ab_period_amount_with_gd_report',
                    'is_allocation',
                    'company_id',
                ],
                'integer',
            ],
            [['buyback_settlement_rule'], 'integer', 'min' => 0, 'max' => 31],
            [['switch_us_day'], 'integer', 'min' => 0, 'max' => 100],
            [['compensate_remit_rule', 'buyback_remit_rule', 'offline_repay_remit_rule'], 'integer', 'min' => 0],
            [['asset_loan_channel'], 'string', 'max' => 64],
            [['settlement_mode'], 'string', 'max' => 32],
            [['desc'], 'string', 'max' => 512],
            [['bd_income_fees', 'company_name'], 'string', 'max' => 256],
            [['asset_loan_channel', 'short_code'], 'unique'],
            [['is_allocation', 'company_id'], 'default', 'value' => 0],
            [['company_name'], 'default', 'value' => '']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'asset_loan_channel' => '机构编码',
            'short_code' => '机构简码',
            'admin_channel' => '管理机构',
            'channel_status' => '机构状态',
            'grant_start_at' => '起量时间',
            'min_due_at' => '最早到期日',
            'max_due_at' => '最晚到期日',
            'min_compensate_at' => '最早代偿日',
            'settlement_mode' => '结算方式',
            'compensate_settlement_rule' => '代偿规则',
            'compensate_settlement_time' => '代偿时间',
            'compensate_remit_rule' => '代偿打款规则',
            'buyback_rule_day' => '连续逾期天数回购',
            'buyback_rule_period' => '连续逾期期次回购',
            'buyback_rule_period_total' => '累计逾期期次回购',
            'buyback_rule_period_adv' => '提前回购期数',
            'buyback_settlement_rule' => '回购宽限期',
            'buyback_remit_rule' => '回购打款规则',
            'switch_us_day' => '切我方日期',
            'switch_us_time' => '切我方时间',
            'offline_repay_remit_rule' => '线下还款打款规则',
            'remit_rule' => '打款规则',
            'desc' => '补充说明',
            'create_user' => '创建人',
            'update_user' => '修改人',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
            'fees' => 'BD收入费用类型',
            'funding_nature' => '机构性质',
            'offline_repay_method' => '线下还款方式',
            'weidu_income_refund' => '唯渡收入回款方式',
            'grant_date_onloan_report' => '按放款日在贷报表',
            'capital_withhold_report' => '资方扣款报表',
            'enable_ab_prin_via_abp_report' => '按权责期次每日权责本金报表',
            'granted_with_rateno_report' => '每日放款(按费率编号分类)报表',
            'ab_period_amount_with_gd_report' => '每日权责期次本金利息(按放款日分类)报表',
            'is_allocation' => '是否分摊',
            'company_id' => '公司ID',
            'company_name' => '公司名称',
            'biz_region' => '业务地区',
        ];
    }

    /**
     * @return string[]
     * @psalm-return array{day: '每日预充', week: '每周预充', holiday: '节假日预充', not: '不需要预充'}
     */
    public static function getSettlementMode(): array
    {
        return [
            'day' => '每日预充',
            'week' => '每周预充',
            'holiday' => '节假日预充',
            'not' => '不需要预充',
        ];
    }


    /**
     * @return string[]
     * @throws KVException
     */
    public static function getBdFeesList(): array
    {
        return KeyValue::take('bd_fees_list');
    }

    public function setFees($fees): void
    {
        $this->bd_income_fees = implode(',', array_filter((array)$fees));
    }

    public function getFees(): array
    {
        return (array)preg_split('/\s*,\s*/', trim((string)$this->bd_income_fees), -1, PREG_SPLIT_NO_EMPTY);
    }

    public function getCreateUser(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'create_user']);
    }

    public function getUpdateUser(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'update_user']);
    }

    public static function findByCode(string $code): ?self
    {
        return self::findOne(['asset_loan_channel' => $code]);
    }
}
