<?php

namespace dcs\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * SettlementOrderSearch represents the model behind the search form of `dcs\models\SettlementOrder`.
 */
class PrepareCollectionSearch extends PrepareCollection
{
    public $startDate;
    public $endDate;

    public function formName(): string
    {
        return '';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['startDate'], 'default', 'value' => Carbon::now()->subWeek()->toDateString()],
            [['endDate'], 'default', 'value' => Carbon::today()->toDateString()],
            [
                [
                    'collection_order_no',
                    'asset_item_no',
                    'status',
                    'startDate',
                    'endDate',
                    'withhold_channel',
                    'actual_deposit',
                    'loan_channel',
                    'withhold_serial_no'
                ],
                'safe'
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['id', 'expect_collection_at'],
                'defaultOrder' => ['id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query
            ->andFilterWhere([
                'withhold_channel' => $this->withhold_channel,
                'status' => $this->status,
                'asset_item_no' => $this->asset_item_no,
                'loan_channel' => $this->loan_channel,
                'actual_deposit' => $this->actual_deposit,
                'withhold_serial_no' => $this->withhold_serial_no,
                'collection_order_no' => $this->collection_order_no
            ])
            ->andFilterWhere(['>=', 'expect_collection_at', $this->startDate])
            ->andFilterWhere([
                '<',
                'expect_collection_at',
                Carbon::parse($this->endDate)->addDay()->toDateString(),
            ]);


        return $dataProvider;
    }
}
