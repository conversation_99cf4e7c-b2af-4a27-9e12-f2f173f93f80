<?php

namespace dcs\models;

use common\models\Asset;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "buyback".
 *
 * @property int        $buyback_id
 * @property string     $buyback_channel
 * @property string     $buyback_asset_item_no
 * @property int        $buyback_asset_period_count
 * @property int        $buyback_asset_granted_principal_amount
 * @property int        $buyback_period
 * @property string     $buyback_start_date
 * @property string     $buyback_end_date
 * @property int        $buyback_hold_day
 * @property int        $buyback_total_principal_amount
 * @property int        $buyback_total_interest_amount
 * @property string     $buyback_create_at
 * @property string     $buyback_update_at
 * @property string     $buyback_category
 * @property string     $buyback_type
 * @property string     $buyback_serial_no
 * @property string     $buyback_notify_principal
 * @property string     $buyback_notify_interest
 * @property int        $buyback_notify_repay_no
 * @property-read Asset $asset
 */
class Buyback extends ActiveRecord
{
    public const BUYBACK_TYPE_QUERY  = 'query';    //资方提供的预估数据
    public const BUYBACK_TYPE_NOTIFY = 'notify';   //回调通知的结果(资方真实扣款的记录)
    public const BUYBACK_TYPE_CALC   = 'calc';     // 计算得出

    public const BUYBACK_CATEGORY_BUYBACK    = 'buyback';       // 回购
    public const BUYBACK_CATEGORY_CHARGEBACK = 'chargeback';    // 退单

    /**
     * buyback 配置
     */
    public const BUYBACK_CONFIG_KEY = 'biz_buyback_config';

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'buyback';
    }

    public static function categorys(): array
    {
        return [
            self::BUYBACK_CATEGORY_CHARGEBACK => '退单',
            self::BUYBACK_CATEGORY_BUYBACK    => '回购',
        ];
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['buyback_asset_item_no'], 'required'],
            [
                [
                    'buyback_asset_period_count',
                    'buyback_asset_granted_principal_amount',
                    'buyback_period',
                    'buyback_hold_day',
                    'buyback_total_principal_amount',
                    'buyback_total_interest_amount',
                ],
                'integer',
            ],
            [['buyback_start_date', 'buyback_end_date', 'buyback_create_at', 'buyback_update_at'], 'safe'],
            [['buyback_asset_item_no', 'buyback_type', 'buyback_serial_no'], 'string', 'max' => 64],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'buyback_id'                             => 'Buyback ID',
            'buyback_channel'                        => '资金方类型',
            'buyback_asset_item_no'                  => '资产编号',
            'buyback_asset_period_count'             => '资产还款期数',
            'buyback_asset_granted_principal_amount' => '资产放款本金',
            'buyback_period'                         => '第几期次回购的',
            'buyback_start_date'                     => '回购开始日',
            'buyback_end_date'                       => '回购截止日',
            'buyback_hold_day'                       => '计息天数',
            'buyback_total_principal_amount'         => '总计回购本金',
            'buyback_total_interest_amount'          => '累计回购利息',
            'buyback_create_at'                      => 'Buyback Create At',
            'buyback_update_at'                      => '更新时间',
            'buyback_category'                       => '回购类别',
            'buyback_type'                           => '回购类型',
            'buyback_serial_no'                      => '回购流水号',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getAsset(): ActiveQuery
    {
        return $this->hasOne(Asset::class, [
            'asset_item_no'      => 'buyback_asset_item_no',
            'asset_loan_channel' => 'buyback_channel',
        ]);
    }
}
