<?php

namespace dcs\models;

use Yii;

/**
 * This is the model class for table "settlement_order".
 *
 * @property int $id 主键
 * @property string $order_no 订单号
 * @property string $loan_channel 通道
 * @property int $amount 交易金额，单位为分
 * @property string $out_deposit 转出存管系统 jining:济宁腾桥存管,miyang:济宁觅杨存管,tengqiao:恒丰腾桥存管
 * @property string $out_account 转出方存管账号
 * @property string $in_account 转入方存管账号
 * @property string $in_deposit 转入存管系统 jining:济宁腾桥存管,miyang:济宁觅杨存管,tengqiao:恒丰腾桥存管
 * @property string $status 状态：init-新建，process-处理中，success-成功，failed-失败，cancelled-已取消
 * @property string $res_code 返回的code
 * @property string $res_message 返回的错误消息
 * @property string $comment 备注
 * @property string $loan_type LOAN-大单 NOLOAN-小单
 * @property string $type 结算单类型 auto:自动结算 manual:财务手动结算
 * @property string $owner 资产所有权者(KN-快牛、STB)
 * @property string $finish_at 转账完成时间
 * @property string $create_at 创建时间
 * @property string $update_at 更新时间
 */
class SettlementOrder extends \yii\db\ActiveRecord
{

    public const TYPE_LOAN = 'LOAN';
    public const TYPE_NO_LOAN = 'NOLOAN';

    public const TYPE_REFUND = 'REFUND';

    public const TYPE_AUTO = 'auto';
    public const TYPE_MANUAL = 'manual';


    public const TYPE_LIST = [
        self::TYPE_AUTO => '自动结算',
        self::TYPE_MANUAL => '财务手动结算',
    ];


    public const LOAN_TYPE_LIST = [
        self::TYPE_LOAN => '大单',
        self::TYPE_NO_LOAN => '小单',
        self::TYPE_REFUND => '退款充值',
    ];

    public const INIT = 'init';
    public const PROCESS = 'process';
    public const SUCCESS = 'success';
    public const FAILED = 'failed';
    public const CANCELLED = 'cancelled';

    public const STATUS_LIST = [
        self::INIT => '新建',
        self::PROCESS => '处理中',
        self::SUCCESS => '成功',
        self::FAILED => '失败',
        self::CANCELLED => '已取消',
    ];


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'settlement_order';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbCapital');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['amount'], 'integer'],
            [['finish_at', 'create_at', 'update_at'], 'safe'],
            [['order_no', 'loan_channel', 'out_account', 'in_account'], 'string', 'max' => 128],
            [['out_deposit', 'in_deposit'], 'string', 'max' => 32],
            [['status', 'res_code'], 'string', 'max' => 64],
            [['res_message'], 'string', 'max' => 1000],
            [['comment'], 'string', 'max' => 100],
            [['loan_type', 'type'], 'string', 'max' => 16],
            [['owner'], 'string', 'max' => 24],
            [['order_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'order_no' => '结算单号',
            'loan_channel' => '资金方',
            'amount' => '金额',
            'out_deposit' => '转出存管',
            'out_account' => '转出账号',
            'in_account' => '转入账号',
            'in_deposit' => '转入存管',
            'status' => '状态',
            'res_code' => '返回code',
            'res_message' => '返回message',
            'comment' => '备注',
            'loan_type' => '资产类型',
            'type' => '结算类型',
            'owner' => '资产所有权者(KN-快牛、STB)',
            'finish_at' => '完成时间',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
        ];
    }
}
