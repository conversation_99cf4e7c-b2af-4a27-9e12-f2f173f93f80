<?php

namespace dcs\models;

use common\models\Asset;
use repay\components\RepayComponent;
use RuntimeException;
use xlerr\desensitise\Desensitise;
use xlerr\desensitise\EncryptEntity;
use xlerr\desensitise\EncryptException;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use function xlerr\adminlte\userFullName;
use function xlerr\desensitise\batchEncrypt;

/**
 * This is the model class for table "{{%manual_refund}}".
 *
 * * @property int $id ID
 * * @property string $asset_item_no 资产编号
 * * @property string $refund_serial_no 退款流水号
 * * @property string $refund_channel_key 代付成功的流水号
 * * @property string $refund_out_account 出款账号
 * * @property string $refund_channel 退款通道
 * * @property int $refund_amount 金额
 * * @property string $refund_status 状态
 * * @property string $refund_limit 退款限制
 * * @property string $receiver_name 收款姓名
 * * @property string $receiver_phone 收款人手机号
 * * @property string $receiver_card_num 收款卡号
 * * @property string $receiver_id_num 收款人身份证
 * * @property string $bank_name 开户行
 * * @property string $source_system 渠道
 * * @property string $operator 创建人
 * * @property string $sign_company 付款主体
 * * @property string|null $refund_channel_code 渠道错误code
 * * @property string|null $refund_channel_message 错误消息
 * * @property string|null $refund_card_type 收款卡来源
 * * @property string $refund_finish_at 代付完成时间
 * * @property string $refund_create_at 创建时间
 * * @property string $refund_update_at 修改时间
 * * @property string $refund_withhold_serial_no 退款序列号
 * * @property int $refund_start_period 开始期次
 * * @property int $refund_end_period 结束期次
 * @property-read Asset $asset
 */
class ManualRefund extends ActiveRecord
{
    public $cardSource = 'specified_card';

    public const CARD_SOURCE_LIST = [
        'grant_card' => '放款卡',
        'last_repay_card' => '最后一次还款成功的卡',
        'specified_card' => '指定卡',
        'origin_repay_card' => '原扣款卡'
    ];


    public const SOURCE_SYSTEM_LIST = [
        'crm' => 'CRM',
        'fox' => 'FOX',
        "biz" => 'BIZ'
    ];

    public const STATUS_NEW = 'ready';
    public const STATUS_PROCESSING = 'process';
    public const STATUS_SUCCESS = 'success';
    public const STATUS_FAILURE = 'fail';
    public const STATUS_LIST = [
        self::STATUS_NEW => '待处理',
        self::STATUS_PROCESSING => '处理中',
        self::STATUS_SUCCESS => '处理成功',
        self::STATUS_FAILURE => '处理失败',
    ];


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%manual_refund}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbRBiz');
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['refund_amount'], 'integer'],
            [
                ['refund_amount'],
                'integer',
                'min' => 1,
                'tooSmall' => '退款金额不能小于0.01元',
                'max' => 999999999,
                'tooBig' => '退款金额不能大于9999999.99元',
            ],
            [
                [
                    'asset_item_no',
                    'refund_amount',
                    'refund_limit',
                    'source_system',
                    'cardSource'
                ],
                'required',
            ],
            [['refund_status', 'refund_channel_message'], 'string'],
            [['refund_finish_at', 'refund_create_at', 'refund_update_at', 'receiver_name', 'receiver_id_num', 'receiver_card_num', 'bank_name', 'receiver_phone', 'refund_withhold_serial_no'], 'safe'],
            [['asset_item_no', 'refund_serial_no', 'refund_channel_key', 'refund_out_account', 'bank_name', 'operator', 'sign_company', 'refund_channel_code', 'refund_card_type'], 'string', 'max' => 64],
            [['refund_channel', 'refund_limit', 'receiver_phone', 'receiver_card_num', 'receiver_id_num', 'source_system'], 'string', 'max' => 32],
            [['receiver_name'], 'string', 'max' => 128],
            [['refund_serial_no', 'asset_item_no'], 'unique', 'targetAttribute' => ['refund_serial_no', 'asset_item_no']],
            [
                'refund_limit',
                'filter',
                'filter' => function ($value) {
                    if ($value === 'refund_after_withhold') {
                        if (empty($this->refund_withhold_serial_no)) {
                            $this->addError('refund_withhold_serial_no', '退款序列号不能为空!');
                        }
                    }
                    return $value;
                },
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'asset_item_no' => '资产编号',
            'refund_serial_no' => '退款流水号',
            'refund_channel_key' => '代付成功的流水号',
            'refund_out_account' => '出款账号',
            'refund_channel' => '退款通道',
            'refund_amount' => '金额',
            'refund_status' => '状态',
            'refund_limit' => '退款限制',
            'receiver_name' => '收款姓名',
            'receiver_phone' => '收款人手机号',
            'receiver_card_num' => '收款卡号',
            'receiver_id_num' => '收款人身份证',
            'bank_name' => '开户行',
            'source_system' => '渠道',
            'operator' => '创建人',
            'sign_company' => '付款主体',
            'refund_channel_code' => '渠道错误code',
            'refund_channel_message' => '错误消息',
            'refund_card_type' => '收款卡来源',
            'refund_finish_at' => '代付完成时间',
            'refund_create_at' => '创建时间',
            'refund_update_at' => '修改时间',
            'cardSource' => '用户收款卡来源',
            'refund_withhold_serial_no' => '退款序列号',
            'refund_start_period' => '开始期次',
            'refund_end_period' => '结束期次',
        ];
    }

    /**
     * @psalm-return bool
     * @throws InvalidConfigException
     */
    public function submit(): bool
    {
        if (!$this->validate()) {
            return false;
        }
        $httpClient = RepayComponent::instance();
        if (!$httpClient->manualRefund($this->toRefundData())) {
            throw new RuntimeException($httpClient->getError());
        }
        return true;
    }

    protected function toRefundData(): array
    {
        $cardInfo = [
            'user_name' => '',
            'user_id_num' => '',
            'user_phone' => '',
            'user_card_num' => '',
            'bank_name' => ''
        ];

        if ($this->cardSource === 'specified_card') {
            $encryptRows = [
                'user_name' => [$this->receiver_name, Desensitise::TYPE_NAME],
                'user_id_num' => [$this->receiver_id_num, Desensitise::TYPE_IDENTITY_NUMBER],
                'user_phone' => [$this->receiver_phone, Desensitise::TYPE_PHONE_NUMBER],
                'user_card_num' => [$this->receiver_card_num, Desensitise::TYPE_BANK_CARD_NUMBER],
            ];

            $cardEncrypt = batchEncrypt($encryptRows, 0, function ($response) {
                throw new EncryptException('脱敏失败: ' . $response['message']);
            });

            $cardEncrypt = array_map(function (EncryptEntity $value) {
                return $value->hash;
            }, $cardEncrypt);
            $cardInfo = array_combine(array_keys($encryptRows), $cardEncrypt) + ['bank_name' => $this->bank_name];
        }


        return [
            'asset_item_no' => $this->asset_item_no,
            'refund_amount' => $this->refund_amount,
            'source_system' => $this->source_system,
            'withhold_serial_no' => $this->refund_withhold_serial_no,
            'operator' => userFullName(),
            'refund_limit' => $this->refund_limit,
            'card_source' => $this->cardSource,
            'card_info' => $cardInfo
        ];
    }


    /**
     * @return ActiveQuery
     */
    public function getAsset(): ActiveQuery
    {
        return $this->hasOne(Asset::class, ['asset_item_no' => 'asset_item_no']);
    }

}
