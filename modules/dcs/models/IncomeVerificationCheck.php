<?php

namespace dcs\models;

use common\components\DcsComponent;
use RuntimeException;
use Yii;
use yii\base\InvalidConfigException;

class IncomeVerificationCheck extends IncomeVerification
{
    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [
                ['verification_type', 'verification_user'],
                'required',
                'on' => [
                    self::VERIFICATION_TYPE_CAPITAL,
                    self::VERIFICATION_TYPE_OTHER,
                ]
            ],
            [
                ['loan_channel', 'settlement_rule_id', 'bill_start_date', 'bill_end_date'],
                'required',
                'on' => self::VERIFICATION_TYPE_CAPITAL
            ],
            [
                ['bill_end_date'],
                'compare',
                'compareAttribute' => 'bill_start_date',
                'operator' => '>=',
                'type' => 'date',
                'enableClientValidation' => false,
            ],
            [
                ['depbank_label'],
                'filter',
                'filter' => function () {
                    $rule = $this->rule;
                    if (!$rule) {
                        $this->addError('settlement_rule_id', '为关联到结算规则: ' . $this->settlement_rule_id);
                    }

                    return $rule->fee_type;
                },
                'on' => self::VERIFICATION_TYPE_CAPITAL
            ],
            [
                ['depbank_label'],
                'required',
                'on' => self::VERIFICATION_TYPE_OTHER
            ],
            [
                ['comment'],
                'string',
                'max' => 255,
                'on' => [
                    self::VERIFICATION_TYPE_CAPITAL,
                    self::VERIFICATION_TYPE_OTHER,
                ]
            ],
        ];
    }

    /**
     * @return bool
     * @throws InvalidConfigException
     */
    public function check(): bool
    {
        if (!$this->validate()) {
            return false;
        }

        if ($this->verification_type === IncomeVerification::VERIFICATION_TYPE_CAPITAL) {
            $data = [
                'incomeVerificationId' => $this->id,
                'incomeVerificationWay' => IncomeVerification::VERIFICATION_WAY_ARTIFICIAL,
                'incomeVerificationType' => $this->verification_type,
                'loanChannel' => $this->loan_channel,
                'settlementRuleId' => $this->settlement_rule_id,
                'billStartDate' => $this->bill_start_date,
                'billEndDate' => $this->bill_end_date,
                'depbankLabel' => $this->depbank_label,
                'comment' => $this->comment,
            ];
        } elseif ($this->verification_type === IncomeVerification::VERIFICATION_TYPE_OTHER) {
            $data = [
                'incomeVerificationId' => $this->id,
                'incomeVerificationWay' => IncomeVerification::VERIFICATION_WAY_ARTIFICIAL,
                'incomeVerificationType' => $this->verification_type,
                'depbankLabel' => $this->depbank_label,
                'comment' => $this->comment,
            ];
        } else {
            throw new RuntimeException('核验类型错误');
        }

        $data['operator'] = $this->verification_user;

        $dcs = DcsComponent::instance();
        if (!$dcs->depbankIncomeVerify($data)) {
            Yii::$app->session->setFlash('error', '接口异常: ' . $dcs->getError());

            return false;
        }

        return true;
    }
}
