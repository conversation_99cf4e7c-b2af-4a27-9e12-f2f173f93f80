<?php

namespace dcs\models;

use Carbon\Carbon;
use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * DepbankOrderSearch represents the model behind the search form of `dcs\models\DepbankOrder`.
 */
class DepbankOrderSearch extends DepbankOrder
{
    public $startDate;
    public $endDate;

    public function formName(): string
    {
        return '';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'order_no',
                    'order_type',
                    'in_acct_no',
                    'status',
                    'out_acct_no',
                    'startDate',
                    'endDate'
                ],
                'safe'
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['create_at'],
                'defaultOrder' => ['create_at' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'order_type' => $this->order_type,
            'status' => $this->status,
        ]);

        $query->andFilterWhere(['like', 'order_no', $this->order_no])
            ->andFilterWhere(['like', 'out_acct_no', $this->out_acct_no])
            ->andFilterWhere(['like', 'in_acct_no', $this->in_acct_no])
            ->andFilterWhere(['>=', 'create_at', $this->startDate])
            ->andFilterWhere([
                '<',
                'create_at',
                Carbon::parse($this->endDate)->addDay()->toDateString(),
            ]);


        return $dataProvider;
    }
}
