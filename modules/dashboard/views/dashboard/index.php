<?php

use xlerr\common\assets\SortableJSAsset;
use dashboard\assets\DashboardAsset;
use kvmanager\models\KeyValue;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use yii\widgets\ListView;

/**
 * @var View $this
 * @var array $models
 */

SortableJSAsset::register($this);
DashboardAsset::register($this);

$this->title = '仪表盘';

$user = Yii::$app->getUser();
?>

<div class="callout callout-danger">
    <h4>警告!</h4>
    内部系统禁止对外提供截图
</div>

<?php

if ($user->can('system_monitor_preview_all')) {
    echo Html::tag('p', Html::a('自动预览', '', [
        'class' => 'btn btn-default preview-all',
    ]));
}

if ($user->can('system_monitor')) {
    echo ListView::widget([
        'dataProvider' => new ArrayDataProvider([
            'allModels' => $models,
        ]),
        'options'      => [
            'id' => 'sortable_system',
        ],
        'itemOptions'  => [
            'class' => 'box box-primary',
        ],
        'itemView'     => '_chart_group',
        'layout'       => '{items}',
    ]);
}
?>
<style>

    /* 用于图表拖拽 */
    .sortable-ghost {
        background-color: #000000;
        opacity: .1;
    }

    .sortable-ghost * {
        opacity: .0;
    }

</style>

<script>
    <?php $this->beginBlock('dashboard') ?>
    const getDashboardDataUrl = '<?= Url::to(['get-data']) ?>';

    $('div.bg-gradient[data-id][data-title]').dashboard({url: getDashboardDataUrl});

    $('button.preview').click(function () {
        let targetBtn = $(this),
            chartSource = targetBtn.parent().prev(),
            newChart = chartSource.clone();

        newChart.removeAttr('_echarts_instance_');
        newChart.css({
            height: '100%',
        });

        layer.closeAll();
        layer.open({
            type: 1,
            title: '统计图',
            area: ['98%', '96%'],
            content: '',
        });

        newChart.appendTo($('div.layui-layer-content'));
        window.setTimeout(function () {
            newChart.dashboard({
                url: getDashboardDataUrl,
                appendToSet: false,
            });
        });
    });

    let timer = null,
        previewConfig = {
            interval: 2 * 60,
            autoEnable: false,
        };

    Object.assign(previewConfig, <?= json_encode((array)KeyValue::takeAsArray('biz_dashboard_config')['preview']) ?>);
    $('a.preview-all').click(function () {
        if (timer) {
            window.clearInterval(timer);
            timer = null;
            $(this).text('自动预览');
        } else {
            let i = 0,
                list = Array.from($('button.preview')),
                preview = function () {
                    list[i % list.length].click();
                    i++;
                };
            preview();
            timer = window.setInterval(preview, previewConfig.interval * 1000);
            $(this).text('关闭自动预览');
        }
        return false;
    });

    if (previewConfig.autoEnable) {
        $('a.preview-all').click();
    }

    const sortableEl = document.getElementById('sortable_system');
    if (sortableEl) {
        new Sortable(sortableEl, {
            animation: 250,
            handle: '.box-header',
            onEnd: function () {
                let keys = [];
                $('#sortable_system > *[data-key]').each(function () {
                    keys.push($(this).data('key'));
                });
                $.post('<?= Url::to(['/dashboard/dashboard/sort'])?>', {
                    data: keys
                });
            }
        });
    }
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['dashboard']) ?>
</script>