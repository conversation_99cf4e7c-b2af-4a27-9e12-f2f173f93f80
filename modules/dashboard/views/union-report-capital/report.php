<?php

use common\models\ReportCapitalSearch;
use dashboard\actions\reports\ReportAction;
use dashboard\actions\reports\UnionReportAction;
use xlerr\common\widgets\GridView;
use yii\data\BaseDataProvider;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View                $this
 * @var UnionReportAction        $action
 * @var ReportCapitalSearch $searchModel
 * @var BaseDataProvider    $dataProvider
 */

$this->title = $action->title;

echo $this->render('_search', [
    'model'  => $searchModel,
    'action' => $action,
]);
echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => (array)$action->columns(),
]);

echo Html::tag('div', nl2br(trim((string)$action->desc())), ['class' => 'text-muted well well-sm no-shadow']);
