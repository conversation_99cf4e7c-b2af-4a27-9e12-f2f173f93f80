<?php

use common\models\ReportCapitalSearch;
use dashboard\actions\reports\UnionReportAction;
use xlerr\common\widgets\DatePicker;
use dashboard\actions\reports\ReportAction;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var ReportCapitalSearch $model
 * @var View                $this
 * @var UnionReportAction   $action
 */

$request = Yii::$app->getRequest();

?>

<div class="box box-default search">
    <div class="box-header with-border">
        <h3 class="box-title">搜索</h3>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => [
                '',
                'channel' => $model->channel,
                'actionId' => $request->get('actionId'),
            ],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= $form->field($model, 'startDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class) ?>

        <?php if ($action->searchCountry()) {
            echo $form->field($model, 'country')->widget(Select2::class, [
                'data' => $action->countries(),
                'hideSearch' => true,
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => '请选择国家',
                ],
            ]);
        } ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', [
            'class' => 'btn btn-primary',
        ]) ?>

        <?= Html::a('重置搜索条件', [
            '',
            'channel' => $model->channel,
            'actionId' => $request->get('actionId'),
        ], [
            'class' => 'btn btn-default',
        ]) ?>

        <?= Html::a('下载', [
            '',
            'channel' => $model->channel,
            'actionId' => $request->get('actionId'),
            'startDate' => $model->startDate,
            'endDate' => $model->endDate,
            'period_count' => $model->period_count,
            'download' => true,
            'sort' => Yii::$app->getRequest()->get('sort'),
        ], [
            'class' => 'btn btn-success download-btn',
        ]) ?>

        <?php ActiveForm::end() ?>
    </div>
</div>

<script>
    <?php $this->beginBlock('download')?>
    $('a.download-btn').on('click', function () {
        window.open($(this).attr('href'), '_blank');
        return false;
    });
    <?php $this->endBlock()?>
    <?php $this->registerJs($this->blocks['download'])?>
</script>
