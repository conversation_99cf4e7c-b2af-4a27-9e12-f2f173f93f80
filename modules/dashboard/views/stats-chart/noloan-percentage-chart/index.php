<?php

use dashboard\assets\DashboardAsset;
use dashboard\models\NoloanStatSearch;
use yii\helpers\Json;

/**
 * @var NoloanStatSearch $searchModel
 * @var array $date
 * @var array $series
 * @var string $type
 */

$title = $this->title = NoloanStatSearch::$titles[$type] ?? '';

$this->params['breadcrumbs'][] = $this->title;
DashboardAsset::register($this);
echo $this->render('_search', ['model' => $searchModel]);

$date = Json::encode($date);
$series = Json::encode($series);

?>
<div class="box box-primary">
    <div class="box-body">
        <div id="chart" style="width: 100%; height: 400px;"></div>
        <div class="chart-description"
             style="margin-top: 15px; padding: 10px; background-color: #f9f9f9; border-radius: 4px; color: #666;">
            <i class="fa fa-info-circle"></i>
            <strong>图表说明：</strong> <?= $searchModel->getDescription($type) ?? '此图表展示了各渠道的小单占比数据，帮助您分析业务表现。' ?>
        </div>
    </div>
</div>

<?php
$this->registerJs(<<<JS
    const chartEl = echarts.init(document.getElementById('chart')),
          date = JSON.parse(`$date`),
          series = JSON.parse(`$series`);

    var option = {
        title: {
            text: `{$title}`
        },
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                let tooltipText = params[0].axisValue + '<br/>';
                params.forEach(function (item) {
                    const seriesIndex = item.seriesIndex;
                    const dataIndex = item.dataIndex;
                    const seriesData = series[seriesIndex];
                    const numerator = seriesData.numerators[dataIndex];
                    const denominator = seriesData.denominators[dataIndex];
                    tooltipText += item.marker + '<span style="font-weight:bold;color:' + item.color + '">' + item.seriesName + '</span>: ' +
                        '<span style="font-size:14px">' + item.value + '%</span> ' +
                        '<span style="color:#666;font-size:12px">[' + numerator + '/' + denominator + ']</span><br/>';
                });
                return tooltipText;
            }
        },
        legend: {
            data: series.map(item => item.name),
            top: '5%'
        },
        xAxis: {
            type: 'category',
            data: date
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: 100,
            interval: 25,
            axisLabel: {
                formatter: function (value) {
                    return value.toFixed(0) + '%';
                }
            }
        },
        series: series.map(function(item) {
            return {
                name: item.name,
                type: 'line',
                smooth: true,
                data: item.data.map(v => v / 100)
           };
       })
    };

    chartEl.setOption(option);
JS
);
?>
