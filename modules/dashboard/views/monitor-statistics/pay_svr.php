<?php

use dashboard\consts\MonitorSearchConst;
use dashboard\services\ReportMonitorPaySvrService;
use kartik\daterange\DateRangePicker;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var array                      $subject
 * @var array                      $channel
 * @var array                      $product
 * @var array                      $bank
 * @var View                       $this
 * @var ReportMonitorPaySvrService $model
 * @var ActiveDataProvider         $dataProvider
 */

$this->title                   = '支付系统统计';
$this->params['breadcrumbs'][] = $this->title;

$dateBtnList = Html::activeRadioList($model, 'date_group', MonitorSearchConst::GROUP_DATE_LIST, [
    'class'       => 'pull-left btn-group btn-group-sm',
    'data-toggle' => 'buttons',
    'item'        => function ($index, $label, $name, $checked, $value) {
        $labelOptions = [
            'class' => 'btn btn-default',
        ];

        Html::addCssClass($labelOptions, $checked ? 'active' : '');

        return Html::radio($name, $checked, [
            'label'        => $label,
            'value'        => $value,
            'labelOptions' => $labelOptions,
        ]);
    },
]);

$groupBtnList = Html::activeCheckboxList($model, 'group', ReportMonitorPaySvrService::GROUP_LIST, [
    'class'       => 'pull-left btn-group btn-group-sm',
    'data-toggle' => 'buttons',
    'style'       => 'margin-left: 5px;',
    'item'        => function ($index, $label, $name, $checked, $value) {
        $labelOptions = [
            'class' => 'btn btn-default',
        ];

        Html::addCssClass($labelOptions, $checked ? 'active' : '');

        return Html::checkbox($name, $checked, [
            'label'        => $label,
            'value'        => $value,
            'labelOptions' => $labelOptions,
        ]);
    },
]);

$form = ActiveForm::begin([
    'action'        => [''],
    'method'        => 'get',
    'type'          => ActiveForm::TYPE_INLINE,
    'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
]);
?>
<style>
    html {
        font-size: 100%;
    }

    input.range-value {
        min-width: 236px;
    }
</style>
<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">搜索</div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>
    <div class="box-body">
        <?= $form->field($model, 'date_range')->widget(DateRangePicker::class, [
            'presetDropdown' => true,
            'pluginOptions'  => [
                'ranges' => [
                    '今日'    => ["moment().startOf('day')", "moment().endOf('day')"],
                    '昨日'    => [
                        "moment().startOf('day').subtract(1,'days')",
                        "moment().endOf('day').subtract(1,'days')",
                    ],
                    '最近7天'  => ["moment().startOf('day').subtract(6, 'days')", "moment().endOf('day')"],
                    '最近30天' => ["moment().startOf('day').subtract(29, 'days')", "moment().endOf('day')"],
                    '最近90天' => ["moment().startOf('day').subtract(89, 'days')", "moment().endOf('day')"],
                    '本周'    => ["moment().startOf('week')", "moment().endOf('week')"],
                    '上周'    => [
                        "moment().subtract(1, 'week').startOf('week')",
                        "moment().subtract(1, 'week').endOf('week')",
                    ],
                    '本月'    => ["moment().startOf('month')", "moment().endOf('month')"],
                    '上月'    => [
                        "moment().subtract(1, 'month').startOf('month')",
                        "moment().subtract(1, 'month').endOf('month')",
                    ],
                    '今年'    => ["moment().startOf('year')", "moment().endOf('year')"],
                    '去年'    => [
                        "moment().subtract(1, 'year').startOf('year')",
                        "moment().subtract(1, 'year').endOf('year')",
                    ],
                ],
            ],
        ]) ?>

        <?= $form->field($model, 'subject', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 100px',
            ],
        ])->widget(Select2::class, [
            'data'          => $subject,
            'options'       => [
                'multiple'     => true,
                'placeholder'  => '主体',
                'autocomplete' => 'off',
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'tags'       => true,
            ],
        ]) ?>

        <?= $form->field($model, 'channel', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 100px',
            ],
        ])->widget(Select2::class, [
            'data'          => $channel,
            'options'       => [
                'multiple'     => true,
                'placeholder'  => '通道',
                'autocomplete' => 'off',
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'tags'       => true,
            ],
        ]) ?>

        <?= $form->field($model, 'product', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 100px',
            ],
        ])->widget(Select2::class, [
            'data'          => $product,
            'options'       => [
                'multiple'     => true,
                'placeholder'  => '产品',
                'autocomplete' => 'off',
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'tags'       => true,
            ],
        ]) ?>

        <?= $form->field($model, 'bank', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 100px',
            ],
        ])->widget(Select2::class, [
            'data'          => $bank,
            'options'       => [
                'multiple'     => true,
                'placeholder'  => '银行',
                'autocomplete' => 'off',
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'tags'       => true,
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary search-btn']) ?>

        <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']); ?>
    </div>
</div>

<?php
$layout = <<<HTML
<div class="box-header with-border">
    {$dateBtnList}
    {$groupBtnList}
</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer"><div class="pull-left">{summary}</div>{pager}</div>
HTML;

echo GridView::widget([
    'layout'       => $layout,
    'dataProvider' => $dataProvider,
    'columns'      => $columns,
]);

ActiveForm::end();
?>

<script>
    <?php $this->beginBlock('js') ?>
    const searchSubmitBtn = $('.search-btn');
    $('#date_group, #group').change(function () {
        searchSubmitBtn.click();
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>
