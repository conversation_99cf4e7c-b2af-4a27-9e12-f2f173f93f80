<?php

use xlerr\CodeEditor\CodeEditor;
use yii\helpers\Html;
use yii\widgets\DetailView;

/**
 * @var yii\web\View                  $this
 * @var dashboard\models\DashboardTpl $model
 */

$this->title                   = $model->name;
$this->params['breadcrumbs'][] = ['label' => '模板', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('修改', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
    <?= Html::a('删除', ['delete', 'id' => $model->id], [
        'class' => 'btn btn-danger',
        'data'  => [
            'confirm' => '确定要删除吗?',
            'method'  => 'post',
        ],
    ]) ?>
</p>
<div class="box box-primary">
    <div class="box-header with-border">
        <div class="title text-bold">详情</div>
    </div>

    <dvi class="box-body no-padding">

        <?= DetailView::widget([
            'model'      => $model,
            'attributes' => [
                'name',
                [
                    'attribute'      => 'config',
                    'captionOptions' => [
                        'style' => 'width:100px',
                    ],
                    'format'         => 'raw',
                    'value'          => CodeEditor::widget([
                        'name'          => 'config_view',
                        'value'         => $model->config,
                        'clientOptions' => [
                            'mode'     => CodeEditor::MODE_JSON,
                            'readOnly' => true,
                            'maxLines' => 40,
                        ],
                    ]),
                ],
            ],
        ]) ?>
    </dvi>

</div>
