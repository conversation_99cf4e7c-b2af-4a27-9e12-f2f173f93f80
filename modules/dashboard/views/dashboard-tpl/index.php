<?php

use dashboard\assets\DashboardAsset;
use dashboard\assets\EchartsAsset;
use yii\data\ArrayDataProvider;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\widgets\ListView;

/**
 * @var yii\web\View                $this
 * @var yii\data\ActiveDataProvider $dataProvider
 */

EchartsAsset::register($this);
DashboardAsset::register($this);

$this->title                   = '模板';
$this->params['breadcrumbs'][] = $this->title;

$models = $dataProvider->getModels();

$options = ArrayHelper::map($models, 'id', 'config');

echo ListView::widget([
    'dataProvider' => new ArrayDataProvider([
        'allModels' => ArrayHelper::index($models, null, 'type'),
    ]),
    'itemView'     => '_chart_group',
    'layout'       => '{items}',
]);
?>

<script>
    <?php $this->beginBlock('js') ?>
    let chartSet = [],
        options = <?= Json::encode($options) ?>,
        resizing = false,
        resize = function () {
            Array.from(chartSet).forEach((chart) => {
                chart && chart.resize();
            });
        };

    window.onresize = () => {
        resizing && window.clearTimeout(resizing);
        resizing = window.setTimeout(resize, 300);
    };

    $('div.bg-gradient[data-id][data-title]').each(function () {
        let self = $(this),
            chart = echarts.init(self.get(0), 'echarts4'),
            code = 'var DATA = [];' + options[self.data('id')],
            option;

        chartSet.push(chart);

        try {
            option = (new Function(code))();

            chart.setOption(option);
        } catch (e) {
            console.log(e);
        }
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>
