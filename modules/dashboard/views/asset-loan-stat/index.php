<?php

use dashboard\models\AssetLoanStatSearch;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;

/**
 * @var ActiveDataProvider  $dataProvider
 * @var ActiveDataProvider  $summaryDataProvider
 * @var AssetLoanStatSearch $searchModel
 * @var array               $dataColumns
 * @var array               $statColumns
 * @var array               $ruleList
 * @var string              $desc
 */

$this->title = '资金方放款统计';

$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', [
    'model'    => $searchModel,
    'ruleList' => $ruleList,
]);

$layout = <<<HTML
<div class="box-header with-border">
    <h3 class="box-title">数据列表</h3>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer">{summary}</div>
HTML;
echo GridView::widget([
    'layout'       => $layout,
    'dataProvider' => $dataProvider,
    'columns'      => $dataColumns,
    //    'dataTableOptions' => [
    //        'fixedHeader'  => [
    //            'header' => true,
    ////            'footer' => true,
    //        ],
    //        'fixedColumns' => [
    //            'leftColumns' => 3,
    //        ],
    //    ],
]);

$layout = <<<HTML
<div class="box-header with-border">
    <h3 class="box-title">统计值</h3>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer">{summary}</div>
HTML;
echo GridView::widget([
    'layout'       => $layout,
    'dataProvider' => $summaryDataProvider,
    'columns'      => $statColumns,
]) ?>

<div>
    <?php echo $desc; ?>
</div>

