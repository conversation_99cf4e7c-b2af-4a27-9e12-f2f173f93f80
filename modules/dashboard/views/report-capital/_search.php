<?php

use common\models\ReportCapitalSearch;
use dashboard\actions\reports\ReportAction;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var ReportCapitalSearch $model
 * @var View $this
 * @var ReportAction $action
 */

$request = Yii::$app->getRequest();

?>

<div class="box box-default search">
    <div class="box-header with-border">
        <h3 class="box-title">搜索</h3>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => [
                '',
                'channel' => $model->channel,
                'actionId' => $request->get('actionId'),
            ],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= $form->field($model, 'startDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class) ?>

        <?php if ($action->searchPeriodCount()) {
            echo $form->field($model, 'period_count')->widget(Select2::class, [
                'data' => [
                    '6' => '6期',
                    '12' => '12期',
                ],
                'hideSearch' => true,
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => $model->getAttributeLabel('period_count'),
                ],
            ]);
        } ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', [
            'class' => 'btn btn-primary',
        ]) ?>

        <?= Html::a('重置搜索条件', [
            '',
            'channel' => $model->channel,
            'actionId' => $request->get('actionId'),
        ], [
            'class' => 'btn btn-default',
        ]) ?>

        <?= Html::a('下载', [
            '',
            'channel' => $model->channel,
            'actionId' => $request->get('actionId'),
            'startDate' => $model->startDate,
            'endDate' => $model->endDate,
            'period_count' => $model->period_count,
            'download' => true,
            'sort' => Yii::$app->getRequest()->get('sort'),
        ], [
            'class' => 'btn btn-success download-btn',
        ]) ?>

        <?php
        if (Yii::$app->getUser()->can('dev_support')) {
            echo Html::a('查看配置', [
                '',
                'channel' => $model->channel,
                'actionId' => $request->get('actionId'),
                'startDate' => $model->startDate,
                'endDate' => $model->endDate,
                'showConfig' => true
            ], [
                'class' => 'btn btn-info layer-dialog',
            ]);
        }
        ?>

        <?php ActiveForm::end() ?>
    </div>
</div>

<script>
    <?php $this->beginBlock('download')?>
    $('a.download-btn').on('click', function () {
        window.open($(this).attr('href'), '_blank');
        return false;
    });
    <?php $this->endBlock()?>
    <?php $this->registerJs($this->blocks['download'])?>
</script>
