<?php

namespace dashboard\models;

use common\web\User;
use datasource\models\DataSource;
use datasource\models\DataSourceCacheModel;
use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "dashboard_chart".
 *
 * @property int          $id
 * @property string       $title
 * @property int          $owner_system
 * @property int          $sort
 * @property int          $tpl_id
 * @property string       $tpl_config
 * @property string       $data_source
 * @property int          $time_interval
 * @property string       $created_at
 * @property string       $updated_at
 * @property DashboardTpl $tpl
 * @property DataSource[] $dataSet
 */
class DashboardChart extends DataSourceCacheModel
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'dashboard_chart';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                ['title', 'owner_system', 'tpl_id', 'tpl_config', 'data_source', 'dataSource', 'time_interval', 'sort'],
                'required',
            ],
            [['dataSource'], 'each', 'rule' => ['integer']],
            [['time_interval', 'sort', 'tpl_id'], 'integer'],
            [['owner_system'], 'in', 'range' => array_keys(User::allGroups())],
            [['created_at', 'updated_at'], 'safe'],
            [['title'], 'string', 'max' => 32],
            [['tpl_config'], 'string'],
            [['data_source'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'title' => '标题',
            'owner_system' => '所属系统',
            'sort' => '排序',
            'tpl_id' => '模板',
            'tpl_config' => '模板配置',
            'data_source' => '数据源',
            'dataSource' => '数据源',
            'time_interval' => '刷新间隔(秒)',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    public function getDataSource()
    {
        return json_decode($this->data_source, true);
    }

    public function setDataSource($val): void
    {
        $this->data_source = json_encode($val);
    }

    public function getTpl(): ActiveQuery
    {
        return $this->hasOne(DashboardTpl::class, ['id' => 'tpl_id']);
    }

    public function getDataSet(): ActiveQuery
    {
        return $this->hasMany(DataSource::class, ['id' => 'dataSource']);
    }

    /**
     * @param $id
     *
     * @return string
     */
    public static function buildKey($id): string
    {
        return 'S' . crc32((string)$id);
    }
}
