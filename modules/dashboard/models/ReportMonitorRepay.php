<?php

namespace dashboard\models;

use datasource\interfaces\ReportMonitorInterface;
use dashboard\traits\ReportModelTrait;

/**
 * This is the model class for table "report_monitor_paysvr".
 *
 * @property int $id
 * @property string  $date
 * @property string  $capital
 * @property string  $type
 * @property string  $source
 * @property string  $product
 * @property string  $period
 * @property string  $scene
 * @property string  $values
 * @property string  $create_at
 */
class ReportMonitorRepay extends \yii\db\ActiveRecord implements ReportMonitorInterface
{
    use ReportModelTrait;

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'report_monitor_repay';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['date', 'capital', 'type', 'source', 'product', 'period','scene', 'values'], 'required'],
            [['date', 'create_at'], 'safe'],
            [['capital', 'source'], 'string', 'max' => 32],
            [[ 'type', 'product', 'period','scene'], 'string', 'max' => 64],
            [['values'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id'         => '主键ID',
            'date'       => '日期',
            'capital'    => '资金',
            'source'    => '通道',
            'date_group' => '分组',
            'product'    => '产品',
            'period'       => '期次',
            'scene'       => '场景',
            'type'       => '类型',
            'values'     => '值',
            'create_at'  => '创建时间',
        ];
    }
}
