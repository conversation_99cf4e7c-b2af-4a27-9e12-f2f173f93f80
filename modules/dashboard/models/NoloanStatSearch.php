<?php

namespace dashboard\models;

use Carbon\Carbon;
use common\models\ReportCapital;
use yii\base\Model;
use yii\db\Exception;
use yii\db\Expression;

class NoloanStatSearch extends Model
{
    public static array $titles = [
        'unRepayPercent' => '小单件数占比折线图',
        'repayPercent' => '小单还款率折线图'
    ];


    public static array $calculation = [
        'unRepayPercent' => [
            'numerator' => 'noloan_unrepaid_count_daily',
            'denominator' => 'noloan_total_count_daily'
        ],
        'repayPercent' => [
            'numerator' => 'noloan_repaid_count_daily',
            'denominator' => 'noloan_total_count_daily'
        ]
    ];

    public static array $descriptions = [
        'unRepayPercent' => '分子：当日到期的大单已还款，小单未还款的笔数（剔除上一期逾期的资产）
分母：当日到期的小单未还款的笔数（剔除上一期逾期的资产）',
        'repayPercent' => '分子：当日到期的小单已还款的笔数
分母：当日到期的小单笔数（剔除上一期逾期的资产）。'
    ];

    public ?string $startDate = null;
    public ?string $endDate = null;
    public string $channel = '';
    public ?string $type = 'unRepayPercent';

    public function formName(): string
    {
        return '';
    }

    public function attributeLabels(): array
    {
        return [
            'startDate' => '开始日期',
            'endDate' => '结束日期',
            'channel' => '主体',
        ];
    }

    public function rules(): array
    {
        return [
            [['channel', 'type', 'startDate', 'endDate'], 'safe'],
            [['startDate'], 'default', 'value' => Carbon::now()->subMonth()->toDateString()],
            [['type'], 'required'],
        ];
    }

    /**
     * 获取图表描述
     * @param string $type 图表类型
     * @return string|null 返回对应类型的描述，如果类型不存在则返回null
     */
    public function getDescription(string $type): ?string
    {
        return self::$descriptions[$type] ?? null;
    }

    /**
     * @param array $params
     * @return array
     * @throws Exception
     */
    public function search(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $types = array_merge(array_values(self::$calculation[$this->type] ?? []), (array)$this->type);

        $selects = ['date' => new Expression('DATE(`date`)'), 'channel'];

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0))', $type));
        }

        $query = ReportCapital::find()->select($selects)->andFilterWhere([
            'channel' => $this->channel,
            'type' => $types,
        ])->andFilterWhere(
            ['>=', 'date', $this->startDate])
            ->andFilterWhere([
                '<',
                'date',
                Carbon::parse($this->endDate)->addDay()->toDateString(),
            ])->groupBy(['date', 'channel']);

        $data = $query->createCommand()->queryAll();
        $date = array_unique(array_column($data, 'date'));

        usort($date, function ($a, $b) {
            return $a > $b;
        });

        $numeratorField = self::$calculation[$this->type]['numerator'] ?? '';
        $denominatorField = self::$calculation[$this->type]['denominator'] ?? '';

        $indexedData = [];
        foreach ($data as $item) {
            $indexedData[$item['channel']][$item['date']] = $item;
        }

        $series = [];
        foreach ($indexedData as $channel => $channelData) {
            $seriesData = [];
            $numeratorValues = [];
            $denominatorValues = [];

            foreach ($date as $d) {
                $item = $channelData[$d] ?? [];
                $seriesData[] = $item[$this->type] ?? 0;
                $numeratorValues[] = $item[$numeratorField] ?? 0;
                $denominatorValues[] = $item[$denominatorField] ?? 0;
            }

            $series[] = [
                'name' => $channel,
                'data' => $seriesData,
                'numerators' => $numeratorValues,
                'denominators' => $denominatorValues
            ];
        }

        return [$date, $series];
    }
}