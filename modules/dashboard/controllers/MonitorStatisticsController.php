<?php

namespace dashboard\controllers;

use dashboard\services\ReportMonitorGrantService;
use dashboard\services\ReportMonitorPaySvrService;
use dashboard\services\ReportMonitorRepayService;
use xlerr\common\grid\MoneyDataColumn;
use Yii;
use yii\data\SqlDataProvider;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\web\Controller;

/**
 * Class MonitorStatisticsController
 *
 * @package dashboard\controllers
 */
class MonitorStatisticsController extends Controller
{
    /**
     * 支付系统监控统计
     *
     * @return string
     * @throws \yii\base\UserException
     */
    public function actionPaySvr()
    {
        $searchModel = new ReportMonitorPaySvrService();
        $query       = $searchModel->searchReportData(Yii::$app->request->get(), [
            'withhold_total_num', // 代扣总数（成功和失败）
            'withhold_total_num_success', // 代扣成功总笔数,包括（主动,自动,手动）
            'withhold_total_amount_success', // 代扣成功总金额
            'withhold_total_cost', // 代扣总成本
            'bind_card_num_success', // 绑卡成功笔数
            'bind_card_num_total', // 绑卡总笔数
            'withdraw_num_success', // 代付成功笔数
            'withdraw_amount_success', // 代付成功金额
        ]);

        $columns       = $searchModel->buildGridColumns($query->select);
        $selectColumns = array_column($columns, 'attribute');

        $query = (new Query())->from(['a' => $query])
            ->select($selectColumns)
            ->addSelect([
                'withhold_total_num_success',
                'withhold_total_amount_success',
                'withhold_success_rate'  => new Expression('withhold_total_num_success/withhold_total_num'),
                'withhold_cost_10000'    => new Expression('withhold_total_cost/withhold_total_amount_success*1000000'),
                'withhold_cost'          => new Expression('withhold_total_cost/withhold_total_num_success'),
                'bind_card_num_total',
                'bind_card_success_rate' => new Expression('bind_card_num_success/bind_card_num_total'),
                'withdraw_num_success',
                'withdraw_amount_success',
            ]);

        $query = (new Query())->from(['a' => $query])
            ->where([
                'or',
                ['>', 'withhold_total_num_success', 0],
                ['>', 'withhold_total_amount_success', 0],
                ['>', 'withhold_success_rate', 0],
                ['>', 'withhold_cost_10000', 0],
                ['>', 'withhold_cost', 0],
                ['>', 'bind_card_num_total', 0],
                ['>', 'bind_card_success_rate', 0],
                ['>', 'withdraw_num_success', 0],
                ['>', 'withdraw_amount_success', 0],
            ]);

        $dataProvider = new SqlDataProvider([
            'sql'        => $query->createCommand()->rawSql,
            'sort'       => [
                'attributes'   => array_merge($selectColumns, [
                    'withhold_total_num_success',
                    'withhold_total_amount_success',
                    'withhold_success_rate',
                    'withhold_cost_10000',
                    'withhold_cost',
                    'bind_card_num_total',
                    'bind_card_success_rate',
                    'withdraw_num_success',
                    'withdraw_amount_success',
                ]),
                'defaultOrder' => ['dateF' => SORT_DESC],
            ],
        ]);

        $config = $searchModel->config();

        $subject = ArrayHelper::getValue($config, 'paySvr.subject', []);
        $channel = ArrayHelper::getValue($config, 'paySvr.channel', []);
        $product = ArrayHelper::getValue($config, 'paySvr.product', []);
        $bank    = ArrayHelper::getValue($config, 'paySvr.bank', []);

        return $this->render('pay_svr', [
            'model'        => $searchModel,
            'dataProvider' => $dataProvider,
            'subject'      => $subject,
            'channel'      => $channel,
            'product'      => $product,
            'bank'         => $bank,
            'columns'      => array_merge($columns, [
                [
                    'label'     => '代扣笔数',
                    'attribute' => 'withhold_total_num_success',
                ],
                [
                    'label'     => '代扣金额',
                    'attribute' => 'withhold_total_amount_success',
                    'class'     => MoneyDataColumn::class,
                ],
                [
                    'label'     => '代扣成功率',
                    'attribute' => 'withhold_success_rate',
                    'value'     => function ($row) {
                        return number_format($row['withhold_success_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '代扣成本/万元',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'withhold_cost_10000',
                ],
                [
                    'label'     => '代扣成本/笔',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'withhold_cost',
                ],
                [
                    'label'     => '绑卡笔数',
                    'attribute' => 'bind_card_num_total',
                ],
                [
                    'label'     => '绑卡成功率',
                    'attribute' => 'bind_card_success_rate',
                    'value'     => function ($row) {
                        return number_format($row['bind_card_success_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '代付笔数',
                    'attribute' => 'withdraw_num_success',
                ],
                [
                    'label'     => '代付金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'withdraw_amount_success',
                ],
            ]),
        ]);
    }

    /**
     * 还款系统监控统计
     *
     * @return string
     * @throws \yii\base\UserException
     */
    public function actionRepay()
    {
        $searchModel = new ReportMonitorRepayService();
        $query       = $searchModel->searchReportData(Yii::$app->request->get(), [
            'total_count', // 到期总数
            'advance_rate', // 提前还款率
            'd0_rate', // D0还款率
            'd1_rate', // D1还款率
            'd7_rate', // D7还款率
            'auto_withhold_count', // 自动代扣成功笔数
            'overdue_no_withhold_count', // 逾期资产中未发起主动还款的笔数
            'cancel_count', // 取消资产比例
            'change_card_count', // 还款卡与申请卡不一致的比例
            'self_channel_count', // 走我方通道还本金资产笔数
            'capital_channel_count', // 走资方通道还本金资产笔数
            'self_channel_count', // 用户主动还款成功笔数
            'user_repay_count', // 用户主动还款成功笔数
            'user_repay_fail_count', // 主动还款非成功和余额不足的笔数
            'repay_report_count', // 代付成功金额
            'report_fk_count',//还款上报风控笔数
            'report_fox_overdue_count',//还款推送贷后逾期资产笔数
            'overdue_asset_count', // 大单逾期资产笔数
            'penalty_interest_count', // 大单计算罚息笔数
            'penalty_interest_amount', // 大单计算罚息金额
            'small_overdue_asset_count', // 小单逾期资产笔数
            'small_penalty_interest_count', // 小单计算罚息笔数
            'small_penalty_interest_amount', // 小单计算罚息金额
            'user_auto_repay_count', //当日用户主动还款总笔数
            'user_auto_withhold_count', //当日主动代扣笔数
            'dsq_repay_request',//DSQ主动还款请求数
            'dsq_repay_request_agree',//DSQ主动还款受理数
        ]);

        $columns       = $searchModel->buildGridColumns($query->select);
        $selectColumns = array_column($columns, 'attribute');

        $query = (new Query())->from(['a' => $query])
            ->select($selectColumns)
            ->addSelect([
                'total_count',
                'advance_rate',
                'd0_rate',
                'd1_rate',
                'd7_rate',
                'auto_withhold_rate'   => new Expression('auto_withhold_count/total_count'),
                'overdue_asset'        => new Expression('overdue_no_withhold_count/total_count'),
                'cancel_rate'          => new Expression('cancel_count/total_count'),
                'change_rate'          => new Expression('change_card_count/total_count'),
                'capital_channel_rate' => new Expression('capital_channel_count/total_count'),
                'self_channel_rate'    => new Expression('self_channel_count/total_count'),
                'user_repay_rate'      => new Expression('user_repay_count/user_auto_withhold_count'),
                'user_repay_fail_rate' => new Expression('user_repay_fail_count/user_auto_repay_count'),
                'report_fk_count',
                'report_fox_overdue_count',
                'overdue_asset_count',
                'penalty_interest_count',
                'penalty_interest_amount',
                'small_overdue_asset_count',
                'small_penalty_interest_count',
                'small_penalty_interest_amount',
                'dsq_repay_request',
                'dsq_repay_request_agree',
            ]);

        $query = (new Query())->from(['a' => $query])
            ->where([
                'or',
                ['>', 'total_count', 0],
                ['>', 'advance_rate', 0],
                ['>', 'd0_rate', 0],
                ['>', 'd1_rate', 0],
                ['>', 'd7_rate', 0],
                ['>', 'auto_withhold_rate', 0],
                ['>', 'overdue_asset', 0],
                ['>', 'cancel_rate', 0],
                ['>', 'change_rate', 0],
                ['>', 'capital_channel_rate', 0],
                ['>', 'self_channel_rate', 0],
                ['>', 'user_repay_rate', 0],
                ['>', 'user_repay_fail_rate', 0],
                ['>', 'report_fk_count', 0],
                ['>', 'report_fox_overdue_count', 0],
                ['>', 'overdue_asset_count', 0],
                ['>', 'penalty_interest_count', 0],
                ['>', 'penalty_interest_amount', 0],
                ['>', 'small_overdue_asset_count', 0],
                ['>', 'small_penalty_interest_count', 0],
                ['>', 'small_penalty_interest_amount', 0],
                ['>', 'dsq_repay_request', 0],
                ['>', 'dsq_repay_request_agree', 0],
            ]);

        $dataProvider = new SqlDataProvider([
            'sql'        => $query->createCommand()->rawSql,
            'sort'       => [
                'attributes'   => array_merge($selectColumns, [
                    'total_count',
                    'advance_rate',
                    'd0_rate',
                    'd1_rate',
                    'd7_rate',
                    'auto_withhold_rate',
                    'overdue_asset',
                    'cancel_rate',
                    'change_rate',
                    'capital_channel_rate',
                    'self_channel_rate',
                    'user_repay_rate',
                    'user_repay_fail_rate',
                    'report_fk_count',
                    'report_fox_overdue_count',
                    'change_card_rate',
                    'capital_channel_rate',
                    'self_channel_rate',
                    'user_repay_rate',
                    'user_repay_fail_rate',
                    'report_fk_count',
                    'report_fox_overdue_count',
                    'overdue_asset_count',
                    'penalty_interest_count',
                    'penalty_interest_amount',
                    'small_overdue_asset_count',
                    'small_penalty_interest_count',
                    'small_penalty_interest_amount',
                    'dsq_repay_request',
                    'dsq_repay_request_agree',
                ]),
                'defaultOrder' => ['dateF' => SORT_DESC],
            ],
        ]);

        $config = $searchModel->config();

        $capital = ArrayHelper::getValue($config, 'repay.capital', []);
        $source  = ArrayHelper::getValue($config, 'repay.source', []);
        $product = ArrayHelper::getValue($config, 'repay.product', []);
        $period  = ArrayHelper::getValue($config, 'repay.period', []);
        $scene   = ArrayHelper::getValue($config, 'repay.scene', []);

        return $this->render('repay', [
            'model'        => $searchModel,
            'dataProvider' => $dataProvider,
            'capital'      => $capital,
            'source'       => $source,
            'product'      => $product,
            'period'       => $period,
            'scene'        => $scene,
            'columns'      => array_merge($columns, [
                [
                    'label'     => '到期总数',
                    'attribute' => 'total_count',
                ],
                [
                    'label'     => '提前还款率',
                    'attribute' => 'advance_rate',
                    'value'     => function ($row) {
                        return number_format($row['advance_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => 'D0还款率',
                    'attribute' => 'd0_rate',
                    'value'     => function ($row) {
                        return number_format($row['d0_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => 'D1还款率',
                    'attribute' => 'd1_rate',
                    'value'     => function ($row) {
                        return number_format($row['d1_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => 'D7还款率',
                    'attribute' => 'd7_rate',
                    'value'     => function ($row) {
                        return number_format($row['d7_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '自动代扣成功率',
                    'attribute' => 'auto_withhold_rate',
                    'value'     => function ($row) {
                        return number_format($row['auto_withhold_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '未主动还款率',
                    'attribute' => 'overdue_asset',
                    'value'     => function ($row) {
                        return number_format($row['overdue_asset'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '资产取消比例',
                    'attribute' => 'cancel_rate',
                    'value'     => function ($row) {
                        return number_format($row['cancel_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '换卡比例',
                    'attribute' => 'change_rate',
                    'value'     => function ($row) {
                        return number_format($row['change_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '资方通道比例',
                    'attribute' => 'capital_channel_rate',
                    'value'     => function ($row) {
                        return number_format($row['capital_channel_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '我方通道比例',
                    'attribute' => 'self_channel_rate',
                    'value'     => function ($row) {
                        return number_format($row['self_channel_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '主动还款成功率',
                    'attribute' => 'user_repay_rate',
                    'value'     => function ($row) {
                        return number_format($row['user_repay_rate'] * 100, 2) . '%';
                    },
                ],
                [
                    'label'     => '主动还款失败率',
                    'attribute' => 'user_repay_fail_rate',
                ],
                [
                    'label'     => '还款上报风控笔数',
                    'attribute' => 'report_fk_count',
                ],
                [
                    'label'     => '还款推送贷后逾期资产笔数',
                    'attribute' => 'report_fox_overdue_count',
                ],
                [
                    'label'     => '大单逾期资产笔数',
                    'attribute' => 'overdue_asset_count',
                ],
                [
                    'label'     => '大单计算罚息笔数',
                    'attribute' => 'penalty_interest_count',
                ],
                [
                    'label'     => '大单罚息金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'penalty_interest_amount',
                ],
                [
                    'label'     => '小单逾期资产笔数',
                    'attribute' => 'small_overdue_asset_count',
                ],
                [
                    'label'     => '小单计算罚息笔数',
                    'attribute' => 'small_penalty_interest_count',
                ],
                [
                    'label'     => '小单罚息金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'small_penalty_interest_amount',
                ],
                [
                    'label'     => 'DSQ主动还款请求数',
                    'attribute' => 'dsq_repay_request',
                ],
                [
                    'label'     => 'DSQ主动还款请求受理数',
                    'attribute' => 'dsq_repay_request_agree',
                ],

            ]),
        ]);
    }

    /**
     * 放款系统监控统计
     *
     * @return string
     * @throws \yii\base\UserException
     */
    public function actionGrant()
    {
        $searchModel = new ReportMonitorGrantService();
        $query       = $searchModel->searchReportData(Yii::$app->request->get(), [
            'plan_amount',
            'route_amount',
            'route_count_1',
            'route_count_2',
            'changed_amount',
            'importing_amount',
            'importing_count',
            'wait_grant_amount',
            'wait_grant_count',
            'withdraw_amount',
            'withdraw_count',
            'repay_amount',
            'repay_count',
            'grant_rate',
            'grant_hour',
        ]);

        $columns       = $searchModel->buildGridColumns($query->select);
        $selectColumns = array_column($columns, 'attribute');
        $query         = (new Query())->from(['a' => $query])
            ->select($selectColumns)
            ->addSelect([
                'plan_amount',
                'route_amount',
                'route_count_1',
                'route_count_2',
                'changed_amount',
                'importing_amount',
                'importing_count',
                'wait_grant_amount',
                'wait_grant_count',
                'withdraw_amount',
                'withdraw_count',
                'repay_amount',
                'repay_count',
                'grant_rate',
                'grant_hour',
                'count',
            ]);

        $query = (new Query())->from(['a' => $query])
            ->where([
                'or',
                ['>', 'plan_amount', 0],
                ['>', 'route_amount', 0],
                ['>', 'route_count_1', 0],
                ['>', 'route_count_2', 0],
                ['>', 'changed_amount', 0],
                ['>', 'importing_amount', 0],
                ['>', 'importing_count', 0],
                ['>', 'wait_grant_amount', 0],
                ['>', 'wait_grant_count', 0],
                ['>', 'withdraw_amount', 0],
                ['>', 'withdraw_count', 0],
                ['>', 'repay_amount', 0],
                ['>', 'repay_count', 0],
                ['>', 'grant_rate', 0],
                ['>', 'grant_hour', 0],
            ]);

        $dataProvider = new SqlDataProvider([
            'sql'        => $query->createCommand()->rawSql,
            'sort'       => [
                'attributes'   => array_merge($selectColumns, [
                    'plan_amount',
                    'route_amount',
                    'route_count_1',
                    'route_count_2',
                    'changed_amount',
                    'importing_amount',
                    'importing_count',
                    'wait_grant_amount',
                    'wait_grant_count',
                    'withdraw_amount',
                    'withdraw_count',
                    'repay_amount',
                    'repay_count',
                    'grant_rate',
                    'grant_hour',
                ]),
                'defaultOrder' => ['dateF' => SORT_DESC],
            ],
        ]);
        $config       = $searchModel->config();

        $capital = ArrayHelper::getValue($config, 'grant.capital', []);
        $source  = ArrayHelper::getValue($config, 'grant.source', []);
        $product = ArrayHelper::getValue($config, 'grant.product', []);
        $period  = ArrayHelper::getValue($config, 'grant.period', []);
        $scene   = ArrayHelper::getValue($config, 'grant.scene', []);

        return $this->render('grant', [
            'model'        => $searchModel,
            'dataProvider' => $dataProvider,
            'capital'      => $capital,
            'source'       => $source,
            'product'      => $product,
            'period'       => $period,
            'scene'        => $scene,
            'columns'      => array_merge($columns, [
                [
                    'label'     => '预计金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'plan_amount',
                ],
                [
                    'label'     => '路由金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'route_amount',

                ],
                [
                    'label'     => '一次路由笔数',
                    'attribute' => 'route_count_1',
                ],
                [
                    'label'     => '二次路由笔数',
                    'attribute' => 'route_count_2',
                ],
                [
                    'label'     => '切资金方金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'changed_amount',
                ],
                [
                    'label'     => '未进件金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'importing_amount',
                ],
                [
                    'label'     => '未进件笔数',
                    'attribute' => 'importing_count',
                ],
                [
                    'label'     => '待放款金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'wait_grant_amount',
                ],
                [
                    'label'     => '待放款笔数',
                    'attribute' => 'wait_grant_count',
                ],
                [
                    'label'     => '待提现金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'withdraw_amount',
                ],
                [
                    'label'     => '待提现笔数',
                    'attribute' => 'withdraw_count',
                ],
                [
                    'label'     => '已放款金额',
                    'class'     => MoneyDataColumn::class,
                    'attribute' => 'repay_amount',
                ],
                [
                    'label'     => '已放款笔数',
                    'attribute' => 'repay_count',
                ],
                [
                    'label'     => '放款成功率',
                    'attribute' => 'grant_rate',
                    'value'     => function ($row) {
                        return number_format($row['grant_rate'] > 0 ? ($row['grant_rate'] / $row['count'] * 100) :
                                0, 2) . '%';
                    },
                ],
                [
                    'label'     => '放款时长',
                    'attribute' => 'grant_hour',
                    'value'     => function ($row) {
                        return round($row['grant_hour'], 2);
                    },
                ],
            ]),
        ]);
    }
}
