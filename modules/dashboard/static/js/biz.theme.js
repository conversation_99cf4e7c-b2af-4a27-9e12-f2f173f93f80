(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['exports', 'echarts'], factory);
    } else if (typeof exports === 'object' && typeof exports.nodeName !== 'string') {
        // CommonJS
        factory(exports, require('echarts'));
    } else {
        // Browser globals
        factory({}, root.echarts);
    }
}(this, function (exports, echarts) {
    var log = function (msg) {
        if (typeof console !== 'undefined') {
            console && console.error && console.error(msg);
        }
    };
    if (!echarts) {
        log('ECharts is not Loaded');
        return;
    }
    var colorPalette = ['#3c8dbc', '#00c0ef', '#00a65a', '#f39c12', '#d33724','#b5bbc8', '#001F3F', '#605ca8', '#D81B60', '#111111'];
    echarts.registerTheme('biz', {
        color: colorPalette,
        graph: {
            color: colorPalette
        }
    });
}));