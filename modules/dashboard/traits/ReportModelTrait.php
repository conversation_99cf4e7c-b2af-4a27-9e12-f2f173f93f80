<?php

namespace dashboard\traits;

use datasource\DataSourceContext;
use yii\base\UserException;
use yii\helpers\ArrayHelper;

trait ReportModelTrait
{
    /**
     * @inheritDoc
     */
    public static function clean(array $data): void
    {
        static::deleteAll([
            'date' => array_values(array_unique(array_column($data, 'date'))),
            'type' => array_values(array_unique(array_column($data, 'type'))),
        ]);
    }

    /**
     * @param array $data
     * @param array $config
     * @param DataSourceContext $dataSourceContext
     *
     * @return int
     * @throws UserException
     */
    public static function storage(array $data, array $config, DataSourceContext $dataSourceContext): int
    {
        $records = self::transform($data, $config);
        if (!empty($records)) {
            self::clean($records);
            $columns = array_keys(current($records));

            return static::getDb()->createCommand()->batchInsert(self::tableName(), $columns, $records)->execute();
        }

        return 0;
    }

    /**
     * @param $data
     * @param $config
     * @return array
     * @throws UserException
     */
    public static function transform($data, $config): array
    {
        $mapping = ArrayHelper::remove($config, 'mapping');

        if (empty($mapping)) {
            throw new UserException('映射的键值不能为空!');
        }

        $records = [];
        foreach ($data as $row) {
            $record = [];
            foreach ($config as $fieldName => $valueFieldName) {
                if (strpos($valueFieldName, ':') === 0) {
                    $record[$fieldName] = ltrim($valueFieldName, ':');
                } else {
                    $record[$fieldName] = $row[$valueFieldName] ?? '';
                }
            }

            foreach ($mapping as $type => $valueColumnName) {
                $rule = array_filter(explode(':', $type), 'trim');
                $type = array_shift($rule);
                $value = $row[$valueColumnName] ?? null;
                if ($value || in_array('allowZero', $rule, true)) {
                    $record['type'] = $type;
                    $record['values'] = $value;
                    $records[] = $record;
                }
            }
        }

        return $records;
    }
}
