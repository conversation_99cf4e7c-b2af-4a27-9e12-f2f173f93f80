<?php

namespace dashboard\traits;

/**
 * 列合计
 */
trait TotalDataColumnTrait
{
    public int $counter = 0;

    /**
     * @var mixed
     */
    public $footerFormat;

    public function init(): void
    {
        parent::init();

        $this->footerFormat  = $this->format;
        $this->footerOptions = $this->headerOptions;
    }

    /**
     * @return mixed
     */
    protected function renderFooterCellContent()
    {
        return $this->grid->formatter->format($this->footer, $this->footerFormat);
    }

    /**
     * @param $model
     * @param $key
     * @param $index
     *
     * @return mixed
     */
    protected function renderDataCellContent($model, $key, $index)
    {
        $this->counter++;
        $this->grid->showFooter = true;

        if ($this->content === null) {
            $value        = (float)$this->getDataCellValue($model, $key, $index);
            $this->footer += $value;

            return $this->grid->formatter->format($value, [...array_values((array)$this->format), (array)$model]);
        }

        $value        = (float)call_user_func($this->content, $model, $key, $index, $this);
        $this->footer += $value;

        return $value;
    }
}
