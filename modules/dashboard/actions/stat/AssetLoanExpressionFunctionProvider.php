<?php

namespace dashboard\actions\stat;

use dashboard\helper\HtmlHelper;
use kvmanager\models\KeyValue;
use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionFunctionProviderInterface;
use xlerr\common\helpers\MoneyHelper;

class AssetLoanExpressionFunctionProvider implements ExpressionFunctionProviderInterface
{
    /**
     * @return array
     */
    public static function methods(): array
    {
        return [
            'renderAssetType'      => [AssetLoanAction::class, 'renderAssetType'],
            'loanedAmount'         => [AssetLoanAction::class, 'loanedAmount'],
            'MoneyF2y'             => [MoneyHelper::class, 'f2y'],
            'link'                 => [HtmlHelper::class, 'link'],
            'KeyValueTake'         => [KeyValue::class, 'take'],
            'KeyValueTakeAsArray'  => [KeyValue::class, 'takeAsArray'],
            'KeyValueTakeAsObject' => [KeyValue::class, 'takeAsObject'],
            'KeyValueTakeAsRaw'    => [KeyValue::class, 'takeAsRaw'],
        ];
    }

    /**
     * @return array|ExpressionFunction[]
     */
    public function getFunctions(): array
    {
        $methods = [];
        foreach (self::methods() as $name => $method) {
            $methods[] = new ExpressionFunction($name, function () use ($method) {
                return vsprintf('\%s::%s(%s)', [
                    $method[0],
                    $method[1],
                    implode(', ', func_get_args()),
                ]);
            }, function () use ($method) {
                return call_user_func_array($method, array_slice(func_get_args(), 1));
            });
        }

        return $methods;
    }
}
