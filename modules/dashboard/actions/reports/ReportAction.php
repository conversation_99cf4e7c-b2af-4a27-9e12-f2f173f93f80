<?php

namespace dashboard\actions\reports;

use Carbon\Carbon;
use common\components\ExpressionComponent;
use common\consts\Consts;
use common\models\Asset;
use common\models\ReportCapitalSearch;
use cpm\models\CapitalChannel;
use datasource\models\DataSource;
use Exception;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use xlerr\common\helpers\CsvHelper;
use Yii;
use yii\base\Action;
use yii\base\InvalidConfigException;
use yii\data\SqlDataProvider;
use yii\db\ActiveQuery;
use yii\grid\DataColumn;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\NotFoundHttpException;
use yii\web\RangeNotSatisfiableHttpException;
use yii\web\Response;

/**
 * Class ReportAction
 * @method string title()
 * @method string desc()
 * @method array columns()
 * @method array queryTypes()
 * @method bool searchPeriodCount()
 * @method string|array defaultSearchParams($node = null)
 * @method string|array pagination()
 * @method string timezone()
 * @method string country()
 *
 * @package dashboard\actions\reports
 */
class ReportAction extends Action
{
    public string $channel = '';
    public string $actionId = '';

    public string $title = '';

    public string $view = 'report';

    /**
     * @return void
     * @throws KVException
     * @throws InvalidConfigException
     */
    public function init()
    {
        $request = $this->controller->request;

        $this->channel = $request->get('channel');
        $this->actionId = $request->get('actionId');

        $request->setQueryParams($request->get() + (array)$this->defaultSearchParams());

        $this->title = vsprintf('%s - %s', [
            $this->title() ?? 'unknown',
            CapitalChannel::name($this->channel),
        ]);

        ExpressionComponent::instance()->registerProvider(new ReportExpressionFunctionProvider());
    }

    /**
     * @param string $key
     *
     * @return array
     * @throws KVException
     */
    public static function loadConfig(string $key): array
    {
        static $configList = [];
        if (isset($configList[$key])) {
            return $configList[$key];
        }

        $configRaw = KeyValue::takeAsRaw($key);

        $depth = 0;
        do {
            $depth += 1;
            $matched = false;
            $config = (array)json_decode($configRaw, true);

            $configRaw = preg_replace_callback(
                '/"\{\{\s*(([\w_]+)\s*:)?\s*([\w\._]+)\s*\}\}"/',
                function ($match) use ($config, &$matched, $key) {
                    $reference = $match[3];
                    $externalKey = $match[2];
                    if ($externalKey && $externalKey !== $key) {
                        $val = ArrayHelper::getValue(self::loadConfig($externalKey), $reference);
                    } else {
                        $val = ArrayHelper::getValue($config, $reference);
                    }

                    $matched = true;

                    return json_encode($val);
                },
                $configRaw
            );
        } while ($matched && $depth <= 10);

        return $configList[$key] = $matched ? (array)json_decode($configRaw, true) : $config;
    }

    /**
     * @param $name
     * @param $params
     *
     * @return mixed|null
     * @throws KVException|NotFoundHttpException|Exception
     */
    public function __call($name, $params)
    {
        static $config = null;
        if (null === $config) {
            $config = self::loadConfig('report_view_'.$this->channel);
            if (!isset($config[$this->actionId])) {
                throw new NotFoundHttpException('unknown report');
            }
            $config = $config[$this->actionId];
        }

        array_unshift($params, $name);

        return ArrayHelper::getValue($config, $params);
    }

    /**
     * @return string|Response
     * @throws InvalidConfigException
     * @throws RangeNotSatisfiableHttpException|KVException
     */
    public function run()
    {
        $searchModel = new ReportCapitalSearch();
        $queryParams = $this->controller->request->get();
        $queryParams['timezone'] = $this->timezone() ?? 'GMT+8';
        $queryParams['country'] = $this->country() ?? 'china';
        $query = $searchModel->search((array)$this->queryTypes(), $queryParams);

        return $this->do($query, $searchModel);
    }

    /**
     * @param $query
     * @param $searchModel
     *
     * @return string|Response
     * @throws InvalidConfigException
     * @throws KVException
     * @throws RangeNotSatisfiableHttpException
     */
    protected function do($query, $searchModel)
    {
        if ($this->searchPeriodCount()) {
            $query->addSelect('period_count')->addGroupBy('period_count');
        }

        $command = $query->createCommand();

        $dataProvider = new SqlDataProvider([
            'sql' => $command->getSql(),
            'params' => $command->params,
            'sort' => [
                'attributes' => ['date'],
                'defaultOrder' => ['date' => SORT_DESC],
            ],
        ]);

        if (null !== $pagination = $this->pagination()) {
            $dataProvider->setPagination($pagination);
        }

        if ($this->controller->request->get('download')) {
            $stream = CsvHelper::build($query->addOrderBy($dataProvider->sort->getOrders()), (array)$this->columns());

            return $this->controller->response->sendStreamAsFile($stream, vsprintf('%s_%s.csv', [
                $this->title,
                Carbon::now()->toDateString(),
            ]), [
                'mimeType' => 'application/csv',
            ]);
        }

        if ($this->controller->request->get('showConfig')) {
            return $this->showConfig(clone $query);
        }

        return $this->controller->render($this->view, [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'action' => $this,
        ]);
    }

    /**
     * @param ActiveQuery $query
     *
     * @return string
     * @throws KVException
     */
    private function showConfig(ActiveQuery $query): string
    {
        $selectSql = $query->createCommand()->rawSql;

        $dataSource = DataSource::find()
            ->where([
                'id' => $query->groupBy(null)->select('data_source_id')->distinct()->column(),
            ])
            ->select(['id', 'name'])
            ->asArray()
            ->all();

        $config = self::loadConfig('report_view_'.$this->channel);

        return $this->controller->render('show-config', [
            'columnConfig' => Json::encode($config[$this->actionId], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
            'selectSql' => $selectSql,
            'dataSource' => $dataSource,
        ]);
    }

    /**
     * @param mixed $data
     * @param string|int $key
     * @param int $index
     * @param DataColumn $column
     *
     * @return mixed
     * @throws InvalidConfigException
     * @example 解决需要计算只能配置闭包，导致不能存储问题
     * ```php
     * 'columns' => [
     *         [
     *              'label' => '本息合计',
     *              'attribute' => 'principal+interest',
     *              'value' => ['\\dashboard\\actions\\reports\\ReportAction', 'expression'],
     *              'class' => '\\xlerr\\common\\grid\\MoneyDataColumn',
     *         ]
     * ]
     * ```
     */
    public static function expression($data, $key, int $index, DataColumn $column)
    {
        return ExpressionComponent::instance()->evaluate($column->attribute, $data);
    }

    /**
     * 获取保证金比例
     *
     * @param string $channel
     * @param string $curDate
     *
     * @return float
     * @throws KVException
     */
    public static function depositRate(string $channel, string $curDate): float
    {
        return self::offlineFeeRate($channel, $curDate, 'deposit');
    }

    /**
     * 获取代偿金比例
     *
     * @param string $channel
     * @param string $curDate
     *
     * @return float
     * @throws KVException
     */
    public static function compensationRate(string $channel, string $curDate): float
    {
        return self::offlineFeeRate($channel, $curDate, 'compensation');
    }

    /**
     * 获取线下费用费率
     *
     * @param string $channel
     * @param string $curDate
     * @param string $type
     * @param int|null $periodCount
     *
     * @return float
     * @throws KVException
     */
    public static function feeRate(string $channel, string $curDate, string $type, int $periodCount = null): float
    {
        return self::offlineFeeRate($channel, $curDate, $type, $periodCount, Consts::CHANNEL_OFFLINE_CONFIG_CONFIG_KEY);
    }

    /**
     * @param string $channel
     * @param string $curDate
     * @param string $type
     * @param int|null $periodCount
     * @param string $configKey
     *
     * @return float
     * @throws KVException
     */
    protected static function offlineFeeRate(
        string $channel,
        string $curDate,
        string $type,
        int $periodCount = null,
        string $configKey = 'biz_offline_rate_map'
    ): float {
        $rateConfig = KeyValue::takeAsArray($configKey);
        if ($configKey === 'biz_offline_rate_map') {
            $rateConfig = $rateConfig[$type][$channel] ?? [];
        } else {
            $rateConfig = $rateConfig[$channel][$type] ?? [];
        }

        krsort($rateConfig);
        foreach ($rateConfig as $date => $rate) {
            if ($date <= $curDate) {
                if ($periodCount) {
                    return (float)($rate[$periodCount] ?? 0);
                }

                return (float)$rate;
            }
        }

        return 0;
    }

    /**
     * 时区转换
     *
     * @param string $date
     * @param string $timezone
     * @param string $format
     *
     * @return string
     */
    public static function timeZoneConversion(string $date, string $timezone, string $format = 'Y-m-d'): string
    {
        return Carbon::parse($date, Yii::$app->getTimeZone())->setTimezone($timezone)->format($format);
    }
}
