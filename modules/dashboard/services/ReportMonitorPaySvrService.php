<?php

namespace dashboard\services;

use Carbon\Carbon;
use dashboard\models\ReportMonitorPaysvr;
use dashboard\traits\ReportSearchTrait;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class ReportMonitorPaySvrService extends ReportMonitorPaysvr
{
    use ReportSearchTrait;

    public const GROUP_SUBJECT = 'subject';
    public const GROUP_CHANNEL = 'channel';
    public const GROUP_PRODUCT = 'product';
    public const GROUP_BANK    = 'bank';
    public const GROUP_LIST    = [
        self::GROUP_SUBJECT => '主体',
        self::GROUP_CHANNEL => '通道',
        self::GROUP_PRODUCT => '产品',
        self::GROUP_BANK    => '银行',
    ];

    public function formName()
    {
        return '';
    }

    public function rules(): array
    {
        return array_merge([
            [['channel', 'subject', 'product', 'bank'], 'safe'],

        ], $this->dateGroupRules());
    }

    /**
     * @param array $params
     * @param array $types
     *
     * @return \yii\db\ActiveQuery
     */
    public function searchReportData($params, $types)
    {
        $this->load($params);
        $this->validate();

        [$startDate, $endDate] = explode(' - ', $this->date_range);
        $selects = array_filter((array)$this->group);

        $query = self::find()
            ->andFilterWhere([
                'and',
                ['>=', 'date', $startDate],
                ['<=', 'date', $endDate],
            ])
            ->andWhere(['type' => $types])
            ->andFilterWhere([
                'channel' => $this->channel,
                'subject' => $this->subject,
                'product' => $this->product,
                'bank'    => $this->bank,
            ])
            ->select($this->convertDateGroup($this->date_group))
            ->groupBy(['dateF'])
            ->addSelect($selects)
            ->addGroupBy($selects);

        $selects = [];
        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type` = \'%s\', `values`, 0))', $type));
        }
        $query->addSelect($selects);

        return $query;
    }

    /**
     * @param array $selects
     *
     * @return array
     * @throws \yii\base\UserException
     */
    public function buildGridColumns($selects)
    {
        $config      = $this->config();
        $columnLists = [
            'dateF'   => [
                'attribute' => 'dateF',
                'label'     => '日期',
            ],
            'subject' => [
                'attribute' => 'subject',
                'label'     => '主体',
                'format'    => ['in', ArrayHelper::getValue($config, 'paySvr.subject', [])],
            ],
            'channel' => [
                'attribute' => 'channel',
                'label'     => '通道',
                'format'    => ['in', ArrayHelper::getValue($config, 'paySvr.channel', [])],
            ],
            'product' => [
                'attribute' => 'product',
                'label'     => '产品',
                'format'    => ['in', ArrayHelper::getValue($config, 'paySvr.product', [])],
            ],
            'bank'    => [
                'attribute' => 'bank',
                'label'     => '银行',
                'format'    => ['in', ArrayHelper::getValue($config, 'paySvr.bank', [])],
            ],
        ];

        $columns = [];
        foreach ($selects as $column => $exp) {
            if (!is_string($column)) {
                $column = $exp;
            }

            if (isset($columnLists[$column])) {
                $columns[] = $columnLists[$column];
            }
        }

        return $columns;
    }

    /**
     * @return array
     * @throws \yii\base\UserException
     */
    public function config()
    {
        static $options;

        if ($options) {
            return $options;
        }

        $config = self::find()->where([
            '>=',
            'date',
            Carbon::parse(Carbon::now()->toDateTimeString())->subDays(10)->toDateString(),
        ])->select([
            'subject',
            'channel',
            'product',
            'bank',
        ])->distinct()->asArray()->all();

        $subject = array_filter(array_unique(array_column($config, 'subject')));
        $channel = array_filter(array_unique(array_column($config, 'channel')));
        $product = array_filter(array_unique(array_column($config, 'product')));
        $bank    = array_filter(array_unique(array_column($config, 'bank')));

        $options = [
            'paySvr' => [
                'subject' => array_combine($subject, $subject),
                'channel' => array_combine($channel, $channel),
                'product' => array_combine($product, $product),
                'bank'    => array_combine($bank, $bank),
            ],
        ];

        return $options;
        //        return (array)KeyValue::take('biz_monitor_statistics');
    }
}
