<?php

namespace dashboard\services;

use dashboard\models\ReportMonitorRepay;
use dashboard\traits\ReportSearchTrait;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class ReportMonitorRepayService extends ReportMonitorRepay
{
    use ReportSearchTrait;

    public const GROUP_CAPITAL = 'capital';
    public const GROUP_PERIOD  = 'period';
    public const GROUP_SOURCE  = 'source';
    public const GROUP_PRODUCT = 'product';
    public const GROUP_SCENE   = 'scene';
    public const GROUP_LIST    = [
        self::GROUP_CAPITAL => '资金',
        self::GROUP_PERIOD  => '期次',
        self::GROUP_SOURCE  => '来源',
        self::GROUP_PRODUCT => '产品',
        self::GROUP_SCENE   => '场景',
    ];

    public function formName()
    {
        return '';
    }

    public function rules(): array
    {
        return array_merge([
            [['capital', 'period', 'source', 'product', 'scene'], 'safe'],

        ], $this->dateGroupRules());
    }

    /**
     * @param array $params
     * @param array $types
     *
     * @return \yii\db\ActiveQuery
     */
    public function searchReportData($params, $types)
    {
        $this->load($params);
        $this->validate();

        [$startDate, $endDate] = explode(' - ', $this->date_range);
        $selects = array_filter((array)$this->group);

        $query = self::find()
            ->andFilterWhere([
                'and',
                ['>=', 'date', $startDate],
                ['<=', 'date', $endDate],
            ])
            ->andWhere(['type' => $types])
            ->andFilterWhere([
                'capital' => $this->capital,
                'source' => $this->source,
                'product' => $this->product,
                'period'    => $this->period,
                'scene'    => $this->scene,
            ])
            ->select($this->convertDateGroup($this->date_group))
            ->groupBy(['dateF'])
            ->addSelect($selects)
            ->addGroupBy($selects);

        $selects = [];
        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type` = \'%s\', `values`, 0))', $type));
        }
        $query->addSelect($selects);

        return $query;
    }



    /**
     * @param array $selects
     *
     * @return array
     * @throws \yii\base\UserException
     */
    public function buildGridColumns($selects)
    {
        $config      = $this->config();
        $columnLists = [
            'dateF'   => [
                'attribute' => 'dateF',
                'label'     => '日期',
            ],
            'capital' => [
                'attribute' => 'capital',
                'label'     => '资金方',
                'format'    => ['in', ArrayHelper::getValue($config, 'repay.capital', [])],
            ],
            'source'  => [
                'attribute' => 'source',
                'label'     => '来源',
                'format'    => ['in', ArrayHelper::getValue($config, 'repay.source', [])],
            ],
            'product' => [
                'attribute' => 'product',
                'label'     => '产品',
                'format'    => ['in', ArrayHelper::getValue($config, 'repay.product', [])],
            ],
            'period'  => [
                'attribute' => 'period',
                'label'     => '期次',
                'format'    => ['in', ArrayHelper::getValue($config, 'repay.period', [])],
            ],
            'scene'   => [
                'attribute' => 'scene',
                'label'     => '场景',
                'format'    => ['in', ArrayHelper::getValue($config, 'repay.scene', [])],
            ],
        ];

        $columns = [];
        foreach ($selects as $column => $exp) {
            if (!is_string($column)) {
                $column = $exp;
            }

            if (isset($columnLists[$column])) {
                $columns[] = $columnLists[$column];
            }
        }

        return $columns;
    }
}
