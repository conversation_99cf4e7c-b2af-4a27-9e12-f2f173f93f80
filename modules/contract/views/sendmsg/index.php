<?php

use contract\models\Sendmsg;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $searchModel grant\models\GbizSendmsg */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title                   = '消息管理';
$this->params['breadcrumbs'][] = $this->title;
?>


<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            条件筛选
        </div>
    </div>
    <div class="box-body">
        <?php

        $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'get',
            'id'     => 'asset-search-form',
            'type'   => ActiveForm::TYPE_INLINE,
        ]) ?>
        <div class="form-group">
            <?= DatePicker::widget([
                'name'    => 'startTime',
                'value'   => Yii::$app->request->get('startTime'),
                'options' => [
                    'placeholder'  => 'Sendmsg下次运行开始时间',
                    'autocomplete' => 'off',
                ],
            ]) ?>
        </div>
        <div class="form-group">
            <?= DatePicker::widget([
                'name'    => 'endTime',
                'value'   => Yii::$app->request->get('endTime'),
                'options' => [
                    'placeholder'  => 'Sendmsg下次运行终止时间',
                    'autocomplete' => 'off',
                ],
            ]) ?>
        </div>
        <div class="form-group" style="min-width: 50px">
            <?= Html::textInput('msgId', Yii::$app->request->get('msgId'), [
                'class'       => 'form-control',
                'placeholder' => '消息ID',
            ]) ?>
        </div>
        <div class="form-group" style="min-width: 50px">
            <?= Html::textInput('msgType', Yii::$app->request->get('msgType'), [
                'class'       => 'form-control',
                'placeholder' => '消息类型',
            ]) ?>
        </div>
        <div class="form-group" style="min-width: 100px">
            <?= Html::textInput('msgOrderNo', Yii::$app->request->get('msgOrderNo'), [
                'class'       => 'form-control',
                'placeholder' => '消息编号',
            ]) ?>
        </div>
        <div class="form-group" style="min-width: 100px">
            <?= Select2::widget([
                'name'          => 'msgStatus',
                'value'         => Yii::$app->getRequest()->get('msgStatus'),
                'data'          => Sendmsg::$MSG_STATUS,
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'hideSearch'    => true,
                'options'       => [
                    'prompt' => '任务状态',
                ],
            ]) ?>
        </div>
        <div class="form-group" style="min-width: 100px">
            <?= Select2::widget([
                'name'          => 'msgPriority',
                'value'         => Yii::$app->getRequest()->get('msgPriority'),
                'data'          => [1 => 1, 2 => 2],
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'hideSearch'    => true,
                'options'       => [
                    'prompt' => '消息优先级',
                ],
            ]) ?>
        </div>
        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']) ?>
        <?php
        ActiveForm::end() ?>
    </div>
</div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'class'          => 'xlerr\common\grid\ActionColumn',
            'template'       => '{view} {update}',
            'header'         => '操作',
            'visibleButtons' => [
                'update' => function ($model) {
                    return $model->sendmsg_status != Sendmsg::STATUS_RUNNING;
                },
            ],
        ],
        'sendmsg_id',
        'sendmsg_order_no',
        'sendmsg_type',
        'sendmsg_content:NJson',
        [
            'label'     => '消息结果',
            'attribute' => 'sendmsg_memo',
            'format'    => 'truncate',
        ],
        'sendmsg_tosystem',
        'sendmsg_status',
        'sendmsg_next_run_at',
        'sendmsg_create_at',
        'sendmsg_update_at',
        'sendmsg_version',
        'sendmsg_priority',
        'sendmsg_retrytimes',
    ],
]); ?>
