<?php

use contract\models\ContractResign;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/** @var $this View */
/** @var $model ContractResign */

$this->title                   = '重新签署';
$this->params['breadcrumbs'][] = '合同';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">上传数据文件</h3>
    </div>

    <?php
    $form = ActiveForm::begin([]) ?>

    <div class="box-body">

        <?= $form->field($model, 'file')->fileInput() ?>

        <?= $form->field($model, 'itemNo')->widget(CodeEditor::class, [
            'clientOptions' => [
                'mode' => CodeEditor::MODE_Text,
            ],
        ]) ?>

        <?= $form->field($model, 'cover')->widget(Select2::class, [
            'data'       => [
                '0' => '否',
                '1' => '是',
            ],
            'hideSearch' => true,
        ]) ?>

        <?= $form->field($model, 'type') ?>

        <?= $form->field($model, 'contractConfigId') ?>

        <?= $form->field($model, 'priority') ?>

        <?= $form->field($model, 'signOpportunity') ?>

        <?= $form->field($model, 'constMap')->widget(CodeEditor::class, [
            'clientOptions' => [
                'mode' => CodeEditor::MODE_JSON,
            ],
        ]) ?>
    </div>

    <div class="box-footer">
        <?= Html::submitButton('提交', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>

    <?php
    ActiveForm::end() ?>
</div>
