<?php

use contract\models\CompanySearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel CompanySearch */

$this->title = '公司列表';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{update}',
        ],
        'company_full_name',
        'company_name',
        'company_type',
        'company_sign',
        'company_platform',
        'company_platform_name',
        'company_platform_subject',
        'company_update_at',
        'company_create_at',
    ],
]);
