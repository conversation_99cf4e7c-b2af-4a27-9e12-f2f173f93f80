<?php

use contract\models\Company;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model Company */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'company_type')->widget(Select2::class, [
                'data' => Company::typeList(),
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('company_type'),
                ],
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'company_full_name')->textInput(['maxlength' => true]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'company_name')->textInput(['maxlength' => true]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'company_sign')->textInput(['maxlength' => true]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'company_platform')->textInput(['maxlength' => true]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'company_platform_name')->textInput(['maxlength' => true]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'company_platform_subject')->textInput(['maxlength' => true]) ?>
        </div>
    </div>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
