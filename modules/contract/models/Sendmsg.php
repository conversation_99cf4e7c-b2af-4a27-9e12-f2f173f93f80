<?php

namespace contract\models;

use Carbon\Carbon;
use contract\components\ContractComponent;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\ActiveDataProvider;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "sendmsg".
 *
 * @property int    $sendmsg_id
 * @property string $sendmsg_order_no
 * @property string $sendmsg_type
 * @property string $sendmsg_content
 * @property string $sendmsg_memo
 * @property string $sendmsg_tosystem
 * @property string $sendmsg_status
 * @property string $sendmsg_next_run_at
 * @property string $sendmsg_create_at
 * @property string $sendmsg_update_at
 * @property int    $sendmsg_version
 * @property int    $sendmsg_priority
 * @property int    $sendmsg_retrytimes
 */
class Sendmsg extends ActiveRecord
{
    public const STATUS_OPEN = 'open';
    public const STATUS_RUNNING = 'running';
    public const STATUS_CLOSE = 'close';
    public const STATUS_TERMINATED = 'terminated';
    public const STATUS_ERROR = 'error';

    public static $MSG_STATUS = [
        'open' => '待处理',
        'running' => '处理中',
        'close' => '成功',
        'terminated' => '终止',
        'error' => '处理失败',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'sendmsg';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbContract');
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['sendmsg_type', 'sendmsg_content', 'sendmsg_status'], 'required'],
            [['sendmsg_content', 'sendmsg_status'], 'string'],
            [['sendmsg_next_run_at', 'sendmsg_create_at', 'sendmsg_update_at'], 'safe'],
            [['sendmsg_version', 'sendmsg_priority', 'sendmsg_retrytimes'], 'integer'],
            [['sendmsg_order_no', 'sendmsg_tosystem'], 'string', 'max' => 64],
            [['sendmsg_type'], 'string', 'max' => 45],
            [['sendmsg_memo'], 'string', 'max' => 2048],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'sendmsg_id' => '消息Id',
            'sendmsg_order_no' => '消息编号',
            'sendmsg_type' => '消息类型',
            'sendmsg_content' => '消息体',
            'sendmsg_memo' => '备注',
            'sendmsg_tosystem' => '消息接收系统',
            'sendmsg_status' => '状态',
            'sendmsg_next_run_at' => '下次发送时间',
            'sendmsg_create_at' => '创建时间',
            'sendmsg_update_at' => '更新时间',
            'sendmsg_version' => '版本',
            'sendmsg_priority' => '优先级',
            'sendmsg_retrytimes' => '重试次数',
        ];
    }

    public function search($params): ActiveDataProvider
    {
        $query = self::find();
        if (!empty($params['msgId'])) {
            $query->andWhere(['sendmsg_id' => $params['msgId']]);
        }
        if (!empty($params['msgOrderNo'])) {
            $query->andWhere(['sendmsg_order_no' => $params['msgOrderNo']]);
        }

        if (!empty($params['startTime'])) {
            $query->andWhere(['>=', 'sendmsg_next_run_at', $params['startTime']]);
        }
        if (!empty($params['endTime'])) {
            $query->andWhere([
                '<',
                'sendmsg_next_run_at',
                Carbon::parse($params['endTime'])->addDay()->floorDay()->toDateTimeString(),
            ]);
        }
        if (!empty($params['msgStatus'])) {
            $query->andWhere(['sendmsg_status' => $params['msgStatus']]);
        }

        if (!empty($params['msgPriority'])) {
            $query->andWhere(['=', 'sendmsg_priority', $params['msgPriority']]);
        }

        if (!empty($params['msgType'])) {
            $query->andWhere(['=', 'sendmsg_type', $params['msgType']]);
        }
        $query->select(['*']);
        $query->orderBy(['sendmsg_id' => SORT_DESC]);

        return new ActiveDataProvider(['query' => $query]);
    }

    /**
     * @param $data
     *
     * @return bool
     * @throws UserException
     * @throws InvalidConfigException
     */
    public function updateSendmsg($data): bool
    {
        $data = $data[$this->formName()];
        $reqData = [
            'id' => $data['sendmsg_id'],
            'content' => $data['sendmsg_content'],
            'memo' => $data['sendmsg_memo'],
            'status' => $data['sendmsg_status'],
            'nextRunAt' => $data['sendmsg_next_run_at'],
            'retryTimes' => $data['sendmsg_retrytimes'],
            'version' => $data['sendmsg_version'],
            'priority' => $data['sendmsg_priority'],
        ];

        $client = ContractComponent::instance();
        if (!$client->msgUpdate($reqData)) {
            throw  new UserException($client->getError());
        }

        return true;
    }
}
