<?php

namespace cpm\controllers;

use backend\controllers\VueViewTrait;
use common\models\User;
use cpm\models\CapitalChannel;
use cpm\models\CapitalChannelAttachment;
use cpm\models\CapitalChannelModule;
use cpm\models\CapitalChannelModuleFunction;
use cpm\models\CapitalChannelModuleIntroduction;
use cpm\models\CapitalChannelSearch;
use dcs\models\CapitalSettlementRuleConfig;
use kvmanager\models\KeyValue;
use Throwable;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\rest\Controller;
use yii\rest\Serializer;
use yii\web\Request;
use yii\web\Response;

/**
 * @property-read Request $request
 */
class ChannelController extends Controller
{
    use VueViewTrait;

    public $serializer = [
        'class' => Serializer::class,
        'collectionEnvelope' => 'items',
    ];

    /**
     * @return ActiveDataProvider|Response
     * @throws InvalidConfigException
     */
    public function actionGrid()
    {
        if (!$this->request->get('per-page')) {
            return $this->asVue('cpm-grid');
        }

        $searchModel = new CapitalChannelSearch();

        return $searchModel->search($this->request->get());
    }

    /**
     * @return Response
     * @throws InvalidConfigException
     */
    public function actionIndex(): Response
    {
        return $this->asVue('cpm-index');
    }

    public function actionList(): array
    {
        $query = CapitalChannel::find()
            ->where(['archive' => 0])
            ->orderBy(['id' => SORT_DESC]);

        $data = [];
        /** @var CapitalChannel $model */
        foreach ($query->each() as $model) {
            $data[] = $this->genChannelInfo($model);
        }

        return $data;
    }

    /**
     * @param CapitalChannel $model
     *
     * @return array
     */
    protected function genChannelInfo(CapitalChannel $model): array
    {
        $user = static function (?User $user) {
            if ($user) {
                return vsprintf('%s %s', [
                    $user->username,
                    explode('@', $user->email)[0] ?? '',
                ]);
            }

            return '';
        };

        return [
            'key' => $model->code,
            'channel_code' => $model->code,
            'channel_name' => $model->name,
            'product_code' => $model->product_code,
            'label' => vsprintf('%s - %s', [
                $model->name,
                $model->code,
            ]),
            'user' => trim(implode(' ', [
                $user($model->bdUser),
                $user($model->omUser),
                $user($model->pmUser),
                $user($model->rdUser),
                $user($model->qeUser),
            ])),
        ];
    }

    /**
     * @param string $code
     *
     * @return array
     */
    public function actionInfo(string $code): array
    {
        /** @var CapitalChannel $channel */
        $channel = CapitalChannel::findOne(['code' => $code]);

        $settlementRule = $channel->settlementRule;

        $data = $settlementRule ? $settlementRule->toArray() : [];

        return array_merge($data, [
            'channel_name' => $channel->name,
            'channel_code' => $channel->code,
            'product_code' => $channel->product_code,
            'release_at' => $channel->release_at,
            'bd' => $channel->bdUser->username ?? '',
            'om' => $channel->omUser->username ?? '',
            'pm' => $channel->pmUser->username ?? '',
            'rd' => $channel->rdUser->username ?? '',
            'qe' => $channel->qeUser->username ?? '',
            'channel_status' => $settlementRule->channel_status ?? $channel->status,
            '_origin' => $channel->toArray(),
        ]);
    }

    public function actionArchive(): array
    {
        $result = CapitalChannel::updateAll([
            'archive' => 1,
        ], [
            'code' => $this->request->post('code'),
        ]);

        return [
            'code' => $result === 1 ? 0 : 1,
            'msg' => $result === 1 ? 'ok' : '归档失败, 请重试',
        ];
    }

    public function actionAttachments($code): array
    {
        return CapitalChannelAttachment::find()
            ->where([
                'channel_code' => $code,
            ])
            ->asArray()
            ->all();
    }

    public function actionSaveAttachment($id = null): array
    {
        if ($id) {
            $model = CapitalChannelAttachment::findOne($id);
            if (!$model) {
                return [
                    'code' => 1,
                    'msg' => '附件不存在',
                ];
            }
        } else {
            $model = new CapitalChannelAttachment();
        }

        $model->load($this->request->post(), '');
        if ($model->save()) {
            return [
                'code' => 0,
                'data' => $model->toArray(),
                'msg' => 'ok',
            ];
        }

        return [
            'code' => 1,
            'msg' => '保存失败, 请重试',
        ];
    }

    public function actionDelAttachment($id): array
    {
        $result = CapitalChannelAttachment::deleteAll([
            'id' => $id,
        ]);

        if ($result === 1) {
            return [
                'code' => 0,
                'msg' => 'ok',
            ];
        }

        return [
            'code' => 1,
            'msg' => '删除失败, 请重试',
        ];
    }

    public function actionModules($code): array
    {
        $modules = CapitalChannelModule::find()
            ->where(['channel_code' => $code])
            ->select([
                'key' => 'CONCAT(channel_code, \'-\', code)',
                'channel_code',
                'code',
                'name',
                'label' => 'name',
            ])
            ->orderBy(['id' => SORT_DESC])
            ->asArray()
            ->all();

        return array_map(static function ($item) {
            $item['leaf'] = true;

            return $item;
        }, $modules);
    }

    public function actionModuleInfo(): array
    {
        /** @var CapitalChannelModule $module */
        $module = CapitalChannelModule::findOne([
            'channel_code' => $this->request->get('channelCode'),
            'code' => $this->request->get('code'),
        ]);

        return $this->buildModuleData($module);
    }

    protected function buildModuleData(CapitalChannelModule $module): array
    {
        return [
            'channel_code' => $module->channel_code,
            'code' => $module->code,
            'name' => $module->name,
            'introductions' => $module->getIntroductions()
                ->select(['id', 'label', 'desc', 'status'])
                ->asArray()
                ->all(),
            'functions' => $module->getFunctions()
                ->select(['id', 'label', 'url', 'display_mode', 'desc', 'status'])
                ->asArray()
                ->all(),
        ];
    }

    /**
     * @param CapitalChannel $capitalChannel
     *
     * @return array<int, CapitalChannelModule>
     * @throws Throwable
     */
    public function genModules(CapitalChannel $capitalChannel): array
    {
        $moduleDataList = KeyValue::take('cpm_template');

        return array_map(function ($moduleData) use ($capitalChannel) {
            return $this->saveModule($capitalChannel->code, $moduleData);
        }, array_reverse($moduleDataList));
    }

    /**
     * @param string $channelCode
     * @param array $data
     *
     * @return CapitalChannelModule
     * @throws Throwable
     */
    protected function saveModule(string $channelCode, array $data): CapitalChannelModule
    {
        $introductions = (array)ArrayHelper::remove($data, 'introductions');
        $functions = (array)ArrayHelper::remove($data, 'functions');

        $module = new CapitalChannelModule($data);
        $module->status = 1;
        $module->channel_code = $channelCode;
        if (!$module->insert()) {
            throw new UserException('Module: ' . Json::encode($module->errors));
        }

        foreach ($introductions as $introduction) {
            $introductionModel = new CapitalChannelModuleIntroduction($introduction);
            $introductionModel->channel_code = $channelCode;
            $introductionModel->module_code = $module->code;
            if (!$introductionModel->insert()) {
                throw new UserException('Introduction: ' . Json::encode($introductionModel->errors));
            }
        }

        foreach ($functions as $function) {
            $functionModel = new CapitalChannelModuleFunction($function);
            $functionModel->channel_code = $channelCode;
            $functionModel->module_code = $module->code;
            if (!$functionModel->insert()) {
                throw new UserException('Function: ' . Json::encode($functionModel->errors));
            }
        }

        return $module;
    }

    /**
     * @param string $code
     *
     * @return array
     */
    public function actionSave(string $code = ''): array
    {
        try {
            return CapitalChannel::getDb()->transaction(function () use ($code) {
                $model = CapitalChannel::findOne(['code' => $code]) ?? new CapitalChannel();
                $isNewRecord = $model->isNewRecord;
                $model->load($this->request->post(), '');
                if (!$model->save()) {
                    throw new UserException(implode('<br/>', array_merge(...array_values($model->errors))));
                }

                if ($isNewRecord) {
                    $this->genModules($model);
                }

                return [
                    'code' => 0,
                    'data' => $this->genChannelInfo($model),
                    'msg' => 'ok',
                ];
            });
        } catch (Throwable $e) {
            return [
                'code' => 1,
                'data' => null,
                'msg' => $e->getMessage(),
            ];
        }
    }

    public function actionSaveFunction(int $id): array
    {
        $post = $this->request->post();

        $model = CapitalChannelModuleFunction::findOne($id) ?? new CapitalChannelModuleFunction();

        $model->load($post, '');
        $model->status = 1;
        if (!$model->save()) {
            return [
                'code' => 1,
                'msg' => '保存出错:' . Json::encode($model->errors),
                'data' => null,
            ];
        }

        return [
            'code' => 0,
            'msg' => 'ok',
            'data' => [
                'id' => $model->id,
            ],
        ];
    }

    public function actionSaveIntroduction(int $id): array
    {
        $post = $this->request->post();

        $model = CapitalChannelModuleIntroduction::findOne($id) ?? new CapitalChannelModuleIntroduction();

        $model->load($post, '');
        $model->status = 1;
        if (!$model->save()) {
            return [
                'code' => 1,
                'msg' => '保存出错:' . Json::encode($model->errors),
                'data' => null,
            ];
        }

        return [
            'code' => 0,
            'msg' => 'ok',
            'data' => [
                'id' => $model->id,
            ],
        ];
    }

    public function actionDelIntroduction(): array
    {
        $affected = CapitalChannelModuleIntroduction::deleteAll([
            'id' => $this->request->post('id'),
        ]);

        return [
            'code' => $affected === 1 ? 0 : 1,
            'msg' => '',
            'data' => null,
        ];
    }

    public function actionDelFunction(): array
    {
        $affected = CapitalChannelModuleFunction::deleteAll([
            'id' => $this->request->post('id'),
        ]);

        return [
            'code' => $affected === 1 ? 0 : 1,
            'msg' => '',
            'data' => null,
        ];
    }

    /**
     * 为select2组件提供列表值
     *
     * @param string $term
     *
     * @return array
     */
    public function actionAjaxChannel(string $term = ''): array
    {
        $condition = [];
        if ($term !== '') {
            $condition = [
                'or',
                ['like', 'code', $term],
                ['like', 'name', $term]
            ];
        }

        $channels = CapitalChannel::list(true, 'CONCAT(name, \' \', code)', $condition);

        $dropdownList = [];
        foreach ($channels as $id => $channel) {
            $dropdownList[] = ['id' => $id, 'text' => $channel];
        }

        return [
            'results' => $dropdownList
        ];
    }

    public function actionAjaxBlChannel(string $term = ''): array
    {
        $channels = CapitalSettlementRuleConfig::find()
            ->where(['funding_nature' => CapitalSettlementRuleConfig::NATURE_BAOLI])
            ->select('asset_loan_channel')
            ->indexBy('asset_loan_channel')
            ->column();

        $channels = CapitalChannel::list(true, 'CONCAT(name, \' \', code)', [
            'code' => array_values($channels),
        ]);

        $dropdownList = array_filter($channels, function($channel) use ($term) {
            return $term === '' || stripos($channel, $term) !== false;
        });

        $dropdownList = array_map(function($id, $channel) {
            return ['id' => $id, 'text' => $channel];
        }, array_keys($dropdownList), $dropdownList);

        return [
            'results' => $dropdownList,
        ];
    }
}
