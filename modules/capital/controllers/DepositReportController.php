<?php

namespace capital\controllers;

use capital\models\ClearingAccountReportSearch;
use capital\models\DepositAccount;
use Exception;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\Request;
use yii\web\Response;

/**
 * 济宁存管统计报表
 * Class DepositReportController
 *
 * @package backend\controllers
 * @property-read Request $request
 * @property-read Response $response
 */
class DepositReportController extends Controller
{
    /**
     * @param string $deposit
     *
     * @return string
     * @throws Exception
     */
    public function actionAccountBalance(string $deposit): string
    {
        $config = ClearingAccountReportSearch::config();

        $query = DepositAccount::find()
            ->where([
                'deposit' => $deposit,
            ]);

        $provisionBalanceTotalQuery = clone $query;
        $totalAmountQuery = clone $query;
        $totalAmount = $totalAmountQuery->sum('balance_available');

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => [
                    'balance_available',
                ],
                'defaultOrder' => [
                    'balance_available' => SORT_DESC,
                ],
            ],
        ]);

        $provisionList = ArrayHelper::getValue($config, ['provision', $deposit]);
        if (empty($provisionList)) {
            $provisionBalanceTotal = 0;
        } else {
            $provisionBalanceTotal = $provisionBalanceTotalQuery->andWhere([
                'number' => $provisionList,
            ])->sum('balance_available');
        }

        return $this->render('account-balance', [
            'deposit' => $deposit,
            'dataProvider' => $dataProvider,
            'provisionTotalAmount' => (int)$provisionBalanceTotal,
            'totalAmount' => (int)$totalAmount,
        ]);
    }

    /**
     * @return string
     */
    public function actionAccountStatistics(): string
    {
        $searchModel = new ClearingAccountReportSearch();
        $dataProvider = $searchModel->search($this->request->get());

        return $this->render('account-statistics', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
}
