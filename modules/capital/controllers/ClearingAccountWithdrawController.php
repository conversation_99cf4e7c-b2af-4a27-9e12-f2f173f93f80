<?php

namespace capital\controllers;

use capital\models\ClearingAccountWithdraw;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * ClearingAccountWithdrawController implements the CRUD actions for ClearingAccountWithdraw model.
 */
class ClearingAccountWithdrawController extends Controller
{
    /**
     * Displays a single ClearingAccountWithdraw model.
     *
     * @param int $id 主键
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the ClearingAccountWithdraw model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id 主键
     *
     * @return ClearingAccountWithdraw the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): ?ClearingAccountWithdraw
    {
        if (($model = ClearingAccountWithdraw::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
