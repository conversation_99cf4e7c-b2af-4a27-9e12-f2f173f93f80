<?php

namespace capital\console;

use capital\models\FlowEverydayAccountBalance;
use common\components\CapitalComponent;
use dcs\models\FlowBankBiz;
use RuntimeException;
use yii\base\InvalidConfigException;
use yii\console\Controller;
use yii\console\ExitCode;
use yii\db\Exception;
use yii\helpers\Json;

class CapitalController extends Controller
{
    /**
     * 同步biz流水数据
     *
     * @param string $startTime
     * @param string $endTime
     *
     * @return int
     * @throws Exception
     * @throws InvalidConfigException
     */
    public function actionSyncFlowBankBiz(string $startTime = '1 hours ago', string $endTime = 'now'): int
    {
        $client = CapitalComponent::instance();
        if (!$client->syncFlowBankBiz($startTime, $endTime)) {
            throw new RuntimeException(Json::encode($client->getResponse()));
        }

        $command = FlowBankBiz::getDb()->createCommand();

        $data = $client->getData();
        foreach ($data as $flowBank) {
            try {
                $flowBank['flow_bank_biz_deleted_at'] ??= '1970-01-01 08:00:00';
                $command->upsert(FlowBankBiz::tableName(), $flowBank)->execute();
            } finally {
                // nobody
            }
        }

        return ExitCode::OK;
    }

    /**
     * 同步biz流水数据
     *
     * @param string $startTime
     * @param string $endTime
     *
     * @return int
     * @throws Exception
     * @throws InvalidConfigException
     */
    public function actionSyncFlowEverydayAccountBalance(
        string $startTime = '1 hours ago',
        string $endTime = 'now'
    ): int {
        $client = CapitalComponent::instance();
        if (!$client->syncFlowEverydayAccountBalance($startTime, $endTime)) {
            throw new RuntimeException(Json::encode($client->getResponse()));
        }

        $command = FlowEverydayAccountBalance::getDb()->createCommand();

        $data = $client->getData();
        foreach ($data as $flow) {
            try {
                $command->upsert(FlowEverydayAccountBalance::tableName(), $flow)->execute();
            } finally {
                // nobody
            }
        }

        return ExitCode::OK;
    }
}
