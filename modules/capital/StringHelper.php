<?php

namespace capital;

use Carbon\Carbon;
use Yii;
use yii\base\Exception;

/**
 * 字符处理类
 *
 * <AUTHOR>
 */
class StringHelper
{
    /**
     * 生成一个22位字符串(不包含前缀)
     *
     * @param string $prefix
     *
     * @return string
     * @throws Exception
     */
    public static function genUniqueString(string $prefix = ''): string
    {
        $now = Carbon::now();

        return trim($prefix)
            . ($now->year - 2016)
            . dechex($now->month)
            . $now->day
            . substr($now->getTimestampMs(), 5)
            . substr(md5(Yii::$app->security->generateRandomKey()), 8, 7);
    }
}
