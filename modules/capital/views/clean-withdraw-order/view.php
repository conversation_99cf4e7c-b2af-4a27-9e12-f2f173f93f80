<?php

use capital\Consts;
use capital\models\CleanWithdrawOrder;
use Carbon\Carbon;
use xlerr\common\helpers\MoneyHelper;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model CleanWithdrawOrder */

$this->title = '资金归集明细详情：'.$model->id;
$this->params['breadcrumbs'][] = ['label' => '代付交易明细', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="clean-withdraw-trade-view">

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'order_no',
            'payment_channel',
            'deposit',
            'channel_code',
            [
                'attribute' => 'operate_type',
                'value' => function ($model) {
                    return CleanWithdrawOrder::$operateTypeStatus[$model->operate_type];
                },
            ],
            [
                'attribute' => 'status',
                'format' => 'raw',
                'value' => function ($model) {
                    return Consts::CLEAN_WITHDRAW_STATUS_MAP[$model->status];
                },
                'captionOptions' => [
                    'style' => 'width: 10%',
                ],
                'contentOptions' => [
                    'style' => 'width:90%',
                ],
            ],
            [
                'attribute' => 'amount',
                'value' => MoneyHelper::f2y($model->amount, true),
            ],
            'memo',
            'create_at',
            [
                'attribute' => 'update_at',
                'format' => function ($date) {
                    return Carbon::parse($date)->toDateTimeString();
                },
            ],
            'finish_at',
        ],
    ]) ?>

</div>
