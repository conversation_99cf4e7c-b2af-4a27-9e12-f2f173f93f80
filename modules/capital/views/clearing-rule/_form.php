<?php

use capital\models\ClearingRule;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ClearingRule */
/* @var $form ActiveForm */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'no')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'value')->widget(CodeEditor::class) ?>
    <p><code>Ctrl+Shift+F</code>或<code>Command+Shift+F</code>可以格式化<code>JSON</code>类型值.</p>

    <?= $form->field($model, 'status')->widget(Select2::class, [
        'data' => ClearingRule::STATUS_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'product_code') ?>

    <?= $form->field($model, 'memo')->textarea(['rows' => 3]) ?>

    <?= $form->field($model, 'support_rule_no')->textarea(['rows' => 3]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton(
        $model->isNewRecord ? '创建' : '保存',
        ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']
    ) ?>
</div>

<?php ActiveForm::end(); ?>

<script>
    <?php $this->beginBlock('js') ?>
    const editor = aceInstance['<?= Html::getInputId($model, 'value') ?>']
    if (editor) {
        editor.commands.addCommand({
            name: 'Format',
            bindKey: {win: 'Ctrl-Shift-F', mac: 'Command-Shift-F'},
            exec: function (editor) {
                if (editor.getSession().getMode().$id === 'ace/mode/json') {
                    try {
                        editor.getSession().setValue(JSON.stringify(JSON.parse(editor.getSession().getValue()), null, "\t"));
                    } catch (e) {
                        console.warn(e);
                    }
                }
            },
        });
    }
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>