<?php

use capital\models\ClearingRule;
use capital\models\ClearingRuleSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ClearingRuleSearch */
/* @var $form ActiveForm */
?>

<div class="box box-default">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'no') ?>

        <?= $form->field($model, 'value') ?>

        <?= $form->field($model, 'status')->widget(Select2::class, [
            'data' => ClearingRule::STATUS_LIST,
            'options' => [
                'placeholder' => '激活状态',
            ],
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置', ['index'], ['class' => 'btn btn-default']) ?>
        <?= Html::a('添加', ['create'], ['class' => 'btn btn-success layer-dialog']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>