<?php

use capital\models\ClearingRule;
use xlerr\CodeEditor\CodeEditor;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\DetailView;

use function xlerr\adminlte\userFullName;

/* @var $this View */
/* @var $model ClearingRule */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => '清分规则', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('修改', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
    <?= Html::a('删除', ['delete', 'id' => $model->id], [
        'class' => 'btn btn-danger',
        'data' => [
            'confirm' => '确定要删除吗?',
            'method' => 'post',
        ],
    ]) ?>
</p>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'options' => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                'no',
                [
                    'attribute' => 'value',
                    'captionOptions' => [
                        'style' => 'width: 10%',
                    ],
                    'format' => 'raw',
                    'value' => CodeEditor::widget([
                        'name' => '_value_view',
                        'value' => $model->value,
                    ]),
                ],
                'memo:ntext',
                [
                    'attribute' => 'status',
                    'format' => ['in', ClearingRule::STATUS_LIST],
                ],
                'product_code',
                [
                    'attribute' => 'create_user',
                    'value' => $model->creator ? userFullName($model->creator) : '-',
                ],
                [
                    'attribute' => 'update_user',
                    'value' => $model->updater ? userFullName($model->updater) : '-',
                ],
                'support_rule_no:ntext',
                'create_at',
                'update_at',
            ],
        ]) ?>
    </div>
</div>
