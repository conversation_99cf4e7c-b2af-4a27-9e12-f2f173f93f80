<?php

use capital\models\CleanSettlement;
use capital\models\CleanSettlementAudit;
use capital\models\CleanSettlementTrade;
use capital\models\Deposit;
use capital\services\CleanClearingTransService;
use capital\services\CleanSettlementAuditService;
use cpm\models\CapitalChannel;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ActiveDataProvider;
use yii\grid\CheckboxColumn;
use yii\helpers\Html;
use yii\helpers\StringHelper;
use yii\web\View;

/** @var View $this */
/** @var ActiveDataProvider $dataProvider */
/** @var CleanSettlementAuditService $searchModel */

$this->title = '大单手动结算记录 - '.Deposit::depositName($searchModel->deposit);

?>

<div class="box search">
    <div class="box-header with-border">
        <h3 class="box-title">搜索</h3>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= Html::activeHiddenInput($searchModel, 'deposit') ?>

        <?= $form->field($searchModel, 'loan_channel', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 204px',
            ],
        ])->widget(Select2::class, [
            'data' => CapitalChannel::list(),
            'options' => [
                'placeholder' => $searchModel->getAttributeLabel('loan_channel'),
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($searchModel, 'status', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 204px',
            ],
        ])->widget(Select2::class, [
            'data' => CleanSettlementAudit::STATUS_LIST,
            'options' => [
                'placeholder' => '状态',
            ],
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($searchModel, 'batch_no') ?>

        <?= $form->field($searchModel, 'create_user') ?>

        <?= $form->field($searchModel, 'audit_user') ?>

        <?= $form->field($searchModel, 'createStartDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建开始日期',
            ],
        ]) ?>

        <?= $form->field($searchModel, 'createEndDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建结束日期',
            ],
        ]) ?>

        <?= $form->field($searchModel, 'auditStartDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '审核开始日期',
            ],
        ]) ?>

        <?= $form->field($searchModel, 'auditEndDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '审核结束日期',
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', [
            'class' => 'btn btn-primary',
        ]) ?>

        <?= Html::a('重置搜索条件', [
            'index',
            'deposit' => $searchModel->deposit,
        ], [
            'class' => 'btn btn-default',
        ]) ?>

        <?= Html::a('结算操作', ['gather'], [
            'class' => 'btn btn-success layer-dialog',
        ]) ?>

        <?php ActiveForm::end() ?>
    </div>
</div>

<?php

$form = ActiveForm::begin([
    'action' => ['adopts'],
    'method' => 'post',
]);

$batchAuditBtn = Html::submitButton('批量审核', [
    'class' => 'btn btn-sm btn-success',
    'data' => [
        'confirm' => '确定要批量审核选中数据吗?',
    ],
]);

$layout = <<<HTML
<div class="box-header with-border">
    $batchAuditBtn
</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer">
    {summary}
    {pager}
</div>
HTML;

echo GridView::widget([
    'layout' => $layout,
    'summaryOptions' => [
        'class' => 'pull-left',
    ],
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => CheckboxColumn::class,
            'checkboxOptions' => function (CleanSettlementAuditService $model) {
                $flag = $model->status !== CleanSettlementAuditService::STATUS_NEW;

                return [
                    'disabled' => $flag,
                    //                    'style'    => 'display:' . ($flag ? 'none' : 'block'),
                ];
            },
        ],
        [
            'header' => '操作',
            'class' => DialogActionColumn::class,
            'template' => '{view}',
        ],
        'batch_no',
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'amount',
        ],
        [
            'attribute' => 'loan_channel',
            'format' => ['in', CapitalChannel::list()],
        ],
        [
            'label' => '交易状态',
            'value' => 'settlement.status',
            'format' => ['in', CleanSettlement::STATUS_LIST, '-'],
        ],
        [
            'label' => '转账流水号',
            'value' => function (CleanSettlementAudit $model) {
                if ($model->settlement && $model->settlement->transferTrade) {
                    return $model->settlement->transferTrade->order_no;
                }

                return '-';
            },
        ],
        [
            'label' => '提现流水号',
            'format' => 'raw',
            'value' => function (CleanSettlementAudit $model) {
                $settlement = $model->settlement;

                if (!$settlement) {
                    return '-';
                }

                $withdraw = $settlement->withdrawalTrade;

                if (!$withdraw) {
                    return '-';
                }

                $data = [];

                $formatter = Yii::$app->getFormatter();

                foreach ($withdraw as $item) {
                    $data[] = [
                        'order_no' => $item->order_no,
                        'amount' => $formatter->format($item->amount, ['f2y', true]),
                        'status' => $formatter->format($item->status, ['in', CleanSettlementTrade::STATUS_LIST, '-']),
                        'comment' => $item->comment,
                    ];
                }

                return Html::a(StringHelper::truncate(current($withdraw)->order_no, 10), 'javascript:;', [
                    'class' => 'withdraw-grid',
                    'data' => [
                        'grid' => $data,
                    ],
                ]);
            },
        ],
        [
            'attribute' => 'transfer_out',
            'value' => 'outAccount.name',
        ],
        [
            'attribute' => 'transfer_in',
            'value' => 'inAccount.name',
        ],
        [
            'label' => '审核状态',
            'attribute' => 'status',
            'format' => ['in', CleanSettlementAudit::STATUS_LIST],
        ],
        [
            'label' => '存管',
            'value' => function (CleanSettlementAudit $model) {
                return Deposit::depositName($model->deposit);
            },
        ],
        'create_user',
        'audit_user',
        'create_at',
        'audit_at',
    ],
]);

ActiveForm::end();
?>

<script>
    <?php $this->beginBlock('js') ?>
    $('a.withdraw-grid').click(function () {
        const data = $(this).data('grid');
        console.log(data);
        let rows = '';
        for (let i = 0; i < data.length; i++) {
            let row = data[i];
            rows += '<tr>' +
                '<td>' + row['order_no'] + '</td>' +
                '<td>' + row['amount'] + '</td>' +
                '<td>' + row['status'] + '</td>' +
                '<td>' + row['comment'] + '</td>' +
                '</tr>';
        }

        layer.open({
            type: 1,
            title: false,
            anim: 1,
            area: ['700px', 'auto'],
            shadeClose: true,
            content: '<div class=""><table class="table">' +
                '<thead><tr>' +
                '<th>提现流水号</th>' +
                '<th>金额</th>' +
                '<th>状态</th>' +
                '<th>备注</th>' +
                '</tr></thead><tbody>' + rows +
                '</tbody></table></div>',
        });
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>


</script>