<?php

use capital\models\CleanClearingTrans;
use capital\models\CleanFinal;
use capital\models\Deposit;
use capital\services\CleanClearingTransService;
use cpm\models\CapitalChannel;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\DataProviderInterface;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;

/** @var CleanClearingTransService $searchModel */
/** @var DataProviderInterface $dataProvider */

$model = $searchModel;

$this->title = '大单手动结算';
$this->params['breadcrumbs'][] = ['label' => $this->title];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="box search">
    <div class="box-header with-border">
        <h3 class="box-title">搜索</h3>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'id' => 'settleSearch',
            'action' => [''],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= Html::activeHiddenInput($model, 'deposit') ?>

        <?= $form->field($model, 'channel', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 200px',
            ],
        ])->widget(Select2::class, [
            'data' => CapitalChannel::list(),
        ]) ?>

        <?= $form->field($model, 'expectSettlementDate')->widget(DatePicker::class, [
            'options' => [
                'autocomplete' => 'off',
                'placeholder' => '预计结算日期',
            ],
        ]) ?>

        <?= $form->field($model, 'type', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 200px',
                'autocomplete' => 'off',
            ],
        ])->widget(Select2::class, [
            'data' => CleanFinal::REPAY_TYPE_LIST,
            'options' => [
                'placeholder' => '还款方式',
            ],
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', [
            'class' => 'btn btn-primary',
        ]) ?>

        <?= Html::a('重置搜索条件', [
            'gather',
            'deposit' => $model->deposit,
        ], [
            'class' => 'btn btn-default',
        ]) ?>

        <?php ActiveForm::end() ?>
    </div>
</div>

<?php
$settleBtn = Html::a('结算', ['create'], [
    'class' => 'btn btn-sm btn-success settlement-btn',
]);
$layout = <<<HTML
<div class="box-header with-border">
    <h3 class="box-title">待结算信息</h3>
    <div class="box-tools">$settleBtn</div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer">
    <div class="pull-left">{summary}</div>
    {pager}
</div>
HTML;

$columns = [
    [
        'label' => '业务类型',
        'attribute' => 'biz_sub_type',
        'format' => ['in', CleanFinal::BIZ_SUB_TYPE_LIST],
    ],
    [
        'label' => '总额',
        'class' => MoneyDataColumn::class,
        'value' => function ($row) {
            return array_sum(array_intersect_key($row, CleanClearingTrans::AMOUNT_TYPE_LIST));
        },
    ],
];

foreach (CleanClearingTransService::getChannelAmountList($searchModel->channel) as $type => $label) {
    $columns[] = [
        'label' => $label,
        'attribute' => $type,
        'contentOptions' => ['class' => ['text-right', $type]],
        'headerOptions' => ['class' => 'text-right'],
        'format' => ['f2y', true],
    ];
}

$columns = array_merge($columns, [
    [
        'label' => '是否能结算',
        'format' => 'boolean',
        'value' => function ($row) {
            return ArrayHelper::getValue($row, 'can_settlement') === 'Y';
        },
    ],
    [
        'label' => '存管',
        'attribute' => 'deposit',
        'format' => ['in', Deposit::deposits()],
    ],
    [
        'label' => '状态',
        'attribute' => 'status',
        'format' => ['in', CleanClearingTrans::STATUS_LIST],
    ],
]);
echo GridView::widget([
    'layout' => $layout,
    'dataProvider' => $dataProvider,
    'columns' => $columns,
]) ?>

<script>
    <?php $this->beginBlock('searchBtn') ?>
    /***
     * 参数说明：
     * number：要格式化的数字
     * decimals：保留几位小数
     * dec_point：小数点符号
     * thousands_sep：千分位符号
     **/
    function number_format(number, decimals, dec_point, thousands_sep) {
        number = (number + '').replace(/[^0-9+-Ee.]/g, '');
        var n = !isFinite(+number) ? 0 : +number, prec = !isFinite(+decimals) ? 2 : Math.abs(decimals),
            sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
            dec = (typeof dec_point === 'undefined') ? '.' : dec_point, s = '', toFixedFix = function (n, prec) {
                var k = Math.pow(10, prec);
                return '' + Math.ceil(n * k) / k;
            };
        s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
        var re = /(-?\d+)(\d{3})/;
        while (re.test(s[0])) {
            s[0] = s[0].replace(re, "$1" + sep + "$2");
        }
        if ((s[1] || '').length < prec) {
            s[1] = s[1] || '';
            s[1] += new Array(prec - s[1].length + 1).join('0');
        }
        return s.join(dec);
    }

    function rmoney(e) {
        return parseFloat(e.replace(/[^\d\.-]/g, ""));
    }

    $('.settlement-btn').click(function () {
        let total = 0, principal = 0, interest = 0, merchantService = 0, technicalService = 0, creditFee = 0,
            afterLoanManage = 0, guaranteeService = 0, guarantee = 0;
        $('#w0 table tbody tr').each(function () {
            const self = $(this);
            total += rmoney(self.find('td:eq(1)').text());
            principal += rmoney(self.find('td.principal').text());
            interest += rmoney(self.find('td.interest').text());
            merchantService += rmoney(self.find('td.merchant_service').text());
            technicalService += rmoney(self.find('td.technical_service').text());
            creditFee += rmoney(self.find('td.credit_fee').text());
            afterLoanManage += rmoney(self.find('td.after_loan_manage').text());
            guaranteeService += rmoney(self.find('td.guarantee_service').text());
            guarantee += rmoney(self.find('td.guarantee').text());
        });
        if (!total) {
            layer.alert('没有可结算数据!', {
                type: 0,
                title: '提示',
                resize: false,
                shadeClose: true,
            });
            return false;
        }
        const url = $(this).attr('href'),
            channel = '<?= $model->channel ?>',
            expectSettlementDate = '<?= $model->expectSettlementDate ?>',
            deposit = '<?= $model->deposit ?>',
            title = $('#cleanclearingtransservice-channel > option:selected').text(),
            type = '<?= $model->type ?>';
        layer.open({
            type: 1,
            title: '提示',
            resize: false, // skin: 'layui-layer-rim', //样式类名
            shadeClose: true, //开启遮罩关闭
            area: ['420px', '380px'], //宽高
            content: `<div class="box-body">
    <h3>确定要发起\`${title}\`结算申请吗?</h3>
    <p>
        本金：${number_format(principal)}
        ${interest ? `<br/>利息：${number_format(interest)}` : ''}
        ${merchantService ? `<br/>平台服务费：${number_format(merchantService)}` : ''}
        ${technicalService ? `<br/>技术服务费：${number_format(technicalService)}` : ''}
        ${creditFee ? `<br/>授信费：${number_format(creditFee)}` : ''}
        ${afterLoanManage ? `<br/>贷后管理费：${number_format(afterLoanManage)}` : ''}
        ${guaranteeService ? `<br/>保障金服务费：${number_format(guaranteeService)}` : ''}
        ${guarantee ? `<br/>保障金：${number_format(guarantee)}` : ''}
        <br/>
        总额：${number_format(total)}
    </p>
    <p>备注：<br/><textarea id="comment" class="form-control"></textarea></p>
</div>`,
            btn: ['确认', '取消'],
            yes: async function (index) {
                const comment = $('#comment').val();
                if (!comment) {
                    layer.alert('备注不能为空!', {
                        type: 0,
                        title: '提示',
                        resize: false,
                        shadeClose: true,
                    });
                    return false;
                }
                layer.close(index);
                const loading = layer.load(1, {shadeClose: false, title: false, shade: 0.5})
                try {
                    const res = await $.post(url, {channel, expectSettlementDate, deposit, type, comment});
                    layer.alert(res?.code === 0 ? '操作成功' : res.message)
                } catch (e) {
                    layer.alert('操作错误：' + e.message)
                } finally {
                    layer.close(loading)
                }
            }
        });
        return false;
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['searchBtn']) ?>
</script>
