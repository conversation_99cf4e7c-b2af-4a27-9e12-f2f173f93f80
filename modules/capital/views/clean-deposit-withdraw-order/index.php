<?php

use capital\models\CleanDepositWithdrawOrder;
use capital\models\CleanDepositWithdrawOrderSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $searchModel CleanDepositWithdrawOrderSearch */
/* @var $dataProvider ActiveDataProvider */

$this->title = '放款通道手动充值列表';
$this->params['breadcrumbs'][] = $this->title;
echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{view}',
        ],
        [
            'attribute' => 'status',
            'format' => ['in', CleanDepositWithdrawOrder::$statusList],
        ],
        'order_no',
        [
            'attribute' => 'amount',
            'label' => '金额（元）',
            'class' => MoneyDataColumn::class,
        ],
        'payment_channel',
        'deposit',
        [
            'attribute' => 'channel_code',
            'value' => function ($model) {
                return CleanDepositWithdrawOrder::getChannelName($model->channel_code);
            },
        ],
        'memo',
        'message',
        'create_at',
    ],
]);
