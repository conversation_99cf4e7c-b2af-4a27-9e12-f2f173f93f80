<?php

use capital\Consts;
use capital\models\DepositManual;
use capital\models\DepositManualSearch;
use common\models\BizLabel;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model DepositManualSearch */
/* @var $form ActiveForm */

$depositAccountMaps = $model->depositAccounts()->select('name')->indexBy('number')->column();
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <i class="fa fa-search"></i>
        <h3 class="box-title">搜索</h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>
        <?= $form->field($model, 'clearing_manual_merchant_key')->textInput() ?>

        <?= $form->field($model, 'clearing_manual_transfer_out')->widget(Select2::class, [
            'data' => $depositAccountMaps,
            'options' => [
                'placeholder' => '转出账户',
            ],
            'pluginOptions' => [
                'width' => '240px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'clearing_manual_transfer_in')->widget(Select2::class, [
            'data' => $depositAccountMaps,
            'options' => [
                'placeholder' => '转入账户',
            ],
            'pluginOptions' => [
                'width' => '240px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'clearing_manual_type')->widget(Select2::class, [
            'data' => Consts::CLEARING_MANUAL_TYPE,
            'options' => [
                'placeholder' => '交易类型',
            ],
            'hideSearch' => true,
            'pluginOptions' => [
                'width' => '100px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'clearing_manual_status')->widget(Select2::class, [
            'data' => Consts::STATUS,
            'options' => [
                'placeholder' => '交易状态',
            ],
            'hideSearch' => true,
            'pluginOptions' => [
                'width' => '100px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'clearing_manual_operator_name')->textInput() ?>

        <?= $form->field($model, 'clearing_manual_comment')->textInput() ?>

        <?= $form->field($model, 'create_start')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建开始日期',
            ],
        ]) ?>

        <?= $form->field($model, 'create_end')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建截止日期',
            ],
        ]) ?>

        <?= $form->field($model, 'finish_start')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '完成开始日期',
            ],
        ]) ?>

        <?= $form->field($model, 'finish_end')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '完成截止日期',
            ],
        ]) ?>

        <?= Html::activeHiddenInput($model, 'clearing_manual_transfer_deposit') ?>

        <div class="form-group">
            <?= Select2::widget([
                'name' => 'reserve_prefix',
                'value' => Yii::$app->getRequest()->get('reserve_prefix'),
                'data' => DepositManual::RESERVE_PREFIX,
                'options' => [
                    'placeholder' => '拨备金流入/流出',
                    'class' => 'reserve_prefix',
                ],
                'hideSearch' => true,
                'pluginOptions' => [
                    'width' => '150px',
                    'allowClear' => true,
                ],
            ]) ?>
        </div>
        <?= $form->field($model, 'clearing_manual_reserve_type')->widget(Select2::class, [
//            'data' => DepositManual::reserveInTypes($model->clearing_manual_transfer_deposit, false),
            'data' => BizLabel::inTypes(),
            'options' => [
                'id' => 'reserve_in',
                //                'disabled'    => true,
                'placeholder' => '全部流入类型',
            ],
            'pluginOptions' => [
                'width' => '200px',
                'allowClear' => true,
            ],
        ]) ?>
        <?= $form->field($model, 'clearing_manual_reserve_type')->widget(Select2::class, [
//            'data' => DepositManual::reserveOutTypes($model->clearing_manual_transfer_deposit, false),
            'data' => BizLabel::outTypes(),
            'options' => [
                'id' => 'reserve_out',
                //                'disabled'    => true,
                'placeholder' => '全部流出类型',
            ],
            'pluginOptions' => [
                'width' => '200px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', [
            'index',
            'clearing_manual_transfer_deposit' => $model->clearing_manual_transfer_deposit,
        ], ['class' => 'btn btn-default']) ?>

        <?= Html::a('补录转账', [
            'transfer-recoup',
            'clearing_manual_transfer_deposit' => $model->clearing_manual_transfer_deposit,
        ], ['class' => 'btn btn-warning layer-dialog']) ?>

        <?= Html::a('转账', [
            'bulk-transfer',
            'clearing_manual_transfer_deposit' => $model->clearing_manual_transfer_deposit,
        ], ['class' => 'btn btn-info layer-dialog']) ?>

        <?= Html::a('提现', [
            'bulk-withdraw',
            'clearing_manual_transfer_deposit' => $model->clearing_manual_transfer_deposit,
        ], ['class' => 'btn btn-success layer-dialog']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>

<script>
    <?php $this->beginBlock('js_block')?>
    $('.reserve_prefix').change(function () {
        let type = $(this).val();
        let elIn = $('#reserve_in'),
            elOut = $('#reserve_out');
        if (type === 'in') {
            elIn.removeAttr('disabled').parent('div.form-group').show();
            elOut.attr('disabled', 'disabled').parent('div.form-group').hide();
        } else if (type === 'out') {
            elOut.removeAttr('disabled').parent('div.form-group').show();
            elIn.attr('disabled', 'disabled').parent('div.form-group').hide();
        } else {
            elIn.attr('disabled', 'disabled').parent('div.form-group').hide();
            elOut.attr('disabled', 'disabled').parent('div.form-group').hide();
        }
    }).change();
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js_block']) ?>
</script>
