<?php

use capital\models\DepositManual;
use datasource\assets\VueComponentsAsset;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\View;

/** @var $this View */
/** @var DepositManual[] $models */
/** @var array $depositAccountMaps */
/** @var array $depositAccountOpts */


$model = $models[0];
$deposit = $model->clearing_manual_transfer_deposit;

$this->title = '手动转账';
$this->params['breadcrumbs'][] = [
    'label' => sprintf('%s - 手动转账/提现', $models[0]->getDepositName()),
    'url' => [
        'index',
        'clearing_manual_transfer_deposit' => $deposit,
    ],
];
$this->params['breadcrumbs'][] = $this->title;

VueComponentsAsset::register($this);

?>
<style>
    .box-solid > .box-body {
        padding-bottom: 0;
    }
</style>

<div id="app">
    <div v-for="item in data" class="box box-solid item">
        <div class="box-header with-border">
            <div class="box-title">转账</div>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" @click="del(item)">
                    <i class="fa fa-fw fa-times"></i>
                </button>
            </div>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group required" :class="{'has-error': !item.out}">
                        <label class="control-label has-star">转出方</label>
                        <select2 v-model="item.out" :options="depositAccountMaps"></select2>
                        <div v-if="!item.out" class="help-block">转出方不能为空</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group required" :class="{'has-error': !item.in}">
                        <label class="control-label has-star">转入方</label>
                        <select2 v-model="item.in" :options="depositAccountMaps"></select2>
                        <div v-if="!item.in" class="help-block">转入方不能为空</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group required" :class="{'has-error': item.error}">
                        <label class="control-label has-star">
                            交易金额
                            <span class="text-red">出款账户余额: {{ item.balance_available ?? '0.00'}}, 交易金额不能超过出口账户余额!</span>
                        </label>
                        <money-input type="text" name="amount" class="form-control" v-model="item.amount"></money-input>
                        {{ item.amount }}
                        <!--                        <input type="text" name="amount" class="form-control" v-model="item.amount">-->
                        <div class="help-block help-block-error">{{ item.error }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>备注</label>
                        <textarea class="form-control" name="comment" v-model="item.comment"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="box box-solid">
        <div class="box-footer">
            <?= Html::button('创建转账', ['class' => 'btn btn-success submit-btn']) ?>
            <button class="btn btn-default" @click="add">添加转账</button>
        </div>
    </div>
</div>
<script>
    <?php $this->beginBlock('js') ?>

    const {createApp, ref, watch} = Vue,
        componentLists = components(),
        app = createApp({
            setup() {
                const
                    depositAccountOpts = <?= Json::htmlEncode($depositAccountOpts) ?>,
                    depositAccountMaps = ref({
                        allowClear: false,
                        data: Object.entries(<?= Json::htmlEncode($depositAccountMaps) ?>)
                            .map(([id, text]) => ({id, text}))
                    }),
                    data = ref([]),
                    add = () => {
                        data.value.push({
                            out: '',
                            in: '',
                            amount: 0,
                            comment: '',
                            reserveType: '',
                        })
                    },
                    del = (item) => {
                        data.value.splice(data.value.indexOf(item), 1)
                    }

                watch(() => data.value.map(item => item.out), (n, o) => {
                    n.forEach((v, i) => {
                        if (v !== o[i]) {
                            data.value[i] = {
                                ...data.value[i],
                                ...(depositAccountOpts[v]?.data ?? {}),
                            }
                        }
                    })
                })

                // let amountTimer
                // watch(() => data.value.map(item => item.amount), (n, o) => {
                //     amountTimer && clearTimeout(amountTimer)
                //     amountTimer = setTimeout(() => {
                //         n.forEach((v, i) => {
                //             const amount = y2f(v)
                //             if (v !== o[i]) {
                //                 const balance = y2f(data.value[i]?.balance_available ?? '0')
                //
                //                 if (amount > balance) {
                //                     data.value[i].error = '交易金额不能超过出款账户余额!'
                //                 } else {
                //                     data.value[i].error = ''
                //                 }
                //             }
                //
                //             data.value[i].amount = f2y(amount)
                //         })
                //     }, 300)
                // })

                add()

                return {
                    depositAccountMaps,
                    data,
                    add,
                    del,
                }
            }
        })

    app.component('select2', componentLists.select2)
    app.component('money-input', {
        props: {
            modelValue: [String, Number],
        },
        emit: ['update:modelValue'],
        setup(props, {emit}) {
            const
                y2f = (amount) => {
                    try {
                        return Number((amount + '').replace(/,/g, '')).toFixed(2) * 100
                    } catch (e) {
                        return 0
                    }
                },
                f2y = (amount) => (amount / 100).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
                onKeypress = (e) => {
                    if (!(e.keyCode === 46 || e.keyCode >= 48 && e.keyCode <= 57) || e.target.value.length > 20) {
                        e.preventDefault()
                    }
                },
                onInput = (e) => {
                    if (e.target.value.length > 20) {
                        e.preventDefault()
                        return
                    }

                    let raw = e.target.value.replace(/\.+/g, '.'),
                        point = e.target.selectionStart,
                        fen = y2f(e.target.value),
                        yuan = f2y(fen)

                    if (point % 4 === 0) {
                        point += 1
                    }

                    e.target.value = yuan
                    e.target.setSelectionRange(point, point)
                    emit('update:modelValue', fen)
                }

            return {
                onInput,
                onKeypress
            }
        },
        template: `<input @keypress="onKeypress($event)" @input="onInput($event)"/>`
    })

    app.mount('#app')

    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>