<?php

use capital\models\DepositManual;
use common\models\BizLabel;
use kartik\widgets\DateTimePicker;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model DepositManual */
/** @var array $depositAccountOutMaps */
/** @var array $depositAccountInMaps */

$this->title = '补录转账';
$this->params['breadcrumbs'][] = [
    'label' => sprintf('%s - 手动转账/提现', $model->getDepositName()),
    'url' => [
        'index',
        'clearing_manual_transfer_deposit' => $model->clearing_manual_transfer_deposit,
    ],
];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="box box-primary">
    <div class="box-header with-border">补录转账</div>
    <?php $form = ActiveForm::begin([
        'options' => ['class' => 'create-form'],
    ]); ?>
    <div class="box-body">
        <?= Html::activeHiddenInput($model, 'clearing_manual_transfer_deposit') ?>

        <?= $form->field($model, 'clearing_manual_transfer_out')->widget(Select2::class, [
            'data' => $depositAccountOutMaps,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'placeholder' => '请选择账户',
            ],
        ]) ?>

        <?= $form->field($model, 'clearing_manual_transfer_in')->widget(Select2::class, [
            'data' => $depositAccountInMaps,
            'options' => [
                'placeholder' => '请选择账户',
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'clearing_manual_amount')->widget(MoneyInput::class) ?>

        <?= $form->field($model, 'clearing_manual_finish_at')->widget(DateTimePicker::class, [
            'type' => DateTimePicker::TYPE_INPUT,
            'pluginOptions' => [
                'todayBtn' => 'linked',
                'todayHighlight' => true,
                'autoclose' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'clearing_manual_comment')->textarea([
            'rows' => 6,
            'placeholder' => '请输入转账备注',
        ]) ?>

        <?= $form->field($model, 'clearing_manual_reserve_type')->widget(Select2::class, [
//            'data' => DepositManual::reserveInTypes($model->clearing_manual_transfer_deposit),
            'data' => BizLabel::inTypes(),
            'options' => [
                'id' => 'reserve_in',
                //                    'placeholder' => '请选择流入类型',
            ],
        ])->label('流入类型') ?>

        <?= $form->field($model, 'clearing_manual_reserve_type')->widget(Select2::class, [
//            'data' => DepositManual::reserveOutTypes($model->clearing_manual_transfer_deposit),
            'data' => BizLabel::outTypes(),
            'options' => [
                'id' => 'reserve_out',
                //                    'placeholder' => '请选择流出类型',
            ],
        ])->label('流出类型') ?>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('保存', [
            'class' => 'btn btn-success',
        ]) ?>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<script>
    <?php $this->beginBlock('js') ?>
    let reserveAccounts = <?= json_encode(DepositManual::reserveAccounts($model->clearing_manual_transfer_deposit)) ?>;
    let outAccountId = '#<?= Html::getInputId($model, 'clearing_manual_transfer_out') ?>';
    let inAccountId = '#<?= Html::getInputId($model, 'clearing_manual_transfer_in') ?>';
    let elInReserve = $('#reserve_in');
    let elOutReserve = $('#reserve_out');
    $(outAccountId + ', ' + inAccountId).change(function () {
        if (reserveAccounts.indexOf($(outAccountId).val()) >= 0) {
            elOutReserve.removeAttr('disabled').parent('div.form-group').show();
            elInReserve.attr('disabled', true).parent('div.form-group').hide();
        } else if (reserveAccounts.indexOf($(inAccountId).val()) >= 0) {
            elInReserve.removeAttr('disabled').parent('div.form-group').show();
            elOutReserve.attr('disabled', true).parent('div.form-group').hide();
        } else {
            elInReserve.attr('disabled', true).parent('div.form-group').hide();
            elOutReserve.attr('disabled', true).parent('div.form-group').hide();
        }
    });
    $(outAccountId).trigger('change');
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>