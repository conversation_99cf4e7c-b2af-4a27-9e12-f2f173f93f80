<?php

use capital\Consts;
use capital\models\ClearingAccountReport;
use capital\models\ClearingAccountReportSearch;
use capital\models\Deposit;
use capital\models\DepositAccount;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $totalAmount int */
/* @var $searchModel ClearingAccountReportSearch */

$this->title = '账户统计 - '.Deposit::depositName($searchModel->clearing_account_report_deposit);
$this->params['breadcrumbs'][] = $this->title;

$accountList = DepositAccount::find()
    ->where([
        'deposit' => $searchModel->clearing_account_report_deposit,
    ])
    ->select('name')
    ->indexBy('number')
    ->column();
?>

<div class="box box-default">
    <div class="box-header with-border">
        <h1 class="box-title">搜索</h1>
    </div>
    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => ['account-statistics'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= Html::activeHiddenInput($searchModel, 'clearing_account_report_deposit') ?>

        <?= $form->field($searchModel, 'clearing_account_report_account')->widget(Select2::class, [
            'data' => $accountList,
            'pluginOptions' => [
                'allowClear' => true,
                'width' => '200px',
            ],
            'options' => [
                'placeholder' => '账户',
            ],
        ]) ?>

        <?= $form->field($searchModel, 'clearing_account_report_account_type')->widget(Select2::class, [
            'data' => Consts::CLEARING_ACCOUNT_TYPE,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
                'width' => '100px',
            ],
            'options' => [
                'placeholder' => '类型',
            ],
        ]) ?>

        <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '开始时间',
            ],
        ]) ?>

        <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '结束时间',
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', [
            'class' => 'submitForm btn btn-primary',
        ]) ?>

        <?= Html::a('重置搜索条件', [
            'account-statistics',
            'clearing_account_report_deposit' => $searchModel->clearing_account_report_deposit,
        ], [
            'class' => 'btn btn-default',
        ]) ?>

        <?php ActiveForm::end() ?>
    </div>
</div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => '交易日期',
            'attribute' => 'clearing_account_report_date',
            'value' => 'clearing_account_report_date',
        ],
        [
            'label' => '账户主体',
            'value' => 'account.owner',
        ],
        [
            'label' => '账户名称',
            'value' => 'account.name',
        ],
        [
            'label' => '账户类型',
            'attribute' => 'clearing_account_report_account_type',
            'format' => ['in', Consts::CLEARING_ACCOUNT_TYPE],
        ],
        [
            'label' => '期初余额',
            'attribute' => 'clearing_account_report_amount_init',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '当日发生额（入金）',
            'attribute' => 'clearing_account_report_amount_in',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '当日发生额（出金）',
            'attribute' => 'clearing_account_report_amount_out',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '期末余额',
            'attribute' => 'clearing_account_report_amount_settle',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '差额',
            'class' => MoneyDataColumn::class,
            'value' => static function (ClearingAccountReport $model) {
                return $model->clearing_account_report_amount_init + $model->clearing_account_report_amount_in
                    - $model->clearing_account_report_amount_out - $model->clearing_account_report_amount_settle;
            },
        ],
    ],
]) ?>
