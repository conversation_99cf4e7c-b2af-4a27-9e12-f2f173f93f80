<?php

namespace capital\services;

use capital\Consts;
use capital\models\CleanPrechargeClearingTran;
use Carbon\Carbon;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use yii\db\ActiveQuery;

class SettlementManualPrechargeService extends CleanPrechargeClearingTran
{
    public const SETTLEMENT_STATUS_NEW = 'N:new';

    public $startDate;
    public $endDate;

    public function formName(): string
    {
        return '';
    }

    /**
     * @return array
     * @throws KVException
     */
    public static function config(): array
    {
        return (array)KeyValue::take('precharge_settlement_config', 'biz-dcs');
    }

    public static function settlementStatus(): array
    {
        return [
            self::SETTLEMENT_STATUS_NEW => '待结算',
            'Y:process' => '结算中',
            'Y:finished' => '已结算',
        ];
    }

    /**
     * @return array
     * @throws KVException
     */
    public static function getDepositListByUser(): array
    {
        $config = self::config();

        return $config['deposit'] ?? [];
    }

    /**
     * @return array
     * @throws KVException
     */
    public function rules(): array
    {
        $depositList = array_keys(self::getDepositListByUser());

        return [
            [['startDate', 'endDate'], 'default', 'value' => Carbon::yesterday()->toDateString()],
            [['deposit'], 'default', 'value' => $depositList],
            [['scenes_type', 'many_scenes', 'transfer_in',], 'safe'],
            [['settlementStatus'], 'string'],
        ];
    }

    public function getSettlementStatus(): string
    {
        return $this->can_settlement.':'.$this->status;
    }

    public function setSettlementStatus($val): void
    {
        [$this->can_settlement, $this->status] = explode(':', empty($val) ? ':' : $val);
    }

    /**
     * @param array $params
     *
     * @return ActiveQuery
     * @throws KVException
     */
    public function search(array $params): ActiveQuery
    {
        $query = self::find()->alias('tran')
            ->where([
                'tran.accrual_type' => 'scenes',
                'tran.is_need_settlement' => 'Y',
                'tran.many_scenes' => ['Y', 'N'],
            ])
            ->andWhere([
                'not in',
                'tran.transfer_in',
                [
                    Consts::CLEARING_ACCOUNT_ID_NUM_V_PINGXIANG_WEIDU_GUARANTEE,
                    Consts::CLEARING_ACCOUNT_ID_NUM_V_HEFEI_WEIDU,
                ],
            ])
            ->andWhere([
                'or',
                [
                    'and',
                    ['=', 'tran.can_settlement', 'Y'],
                    ['=', 'tran.status', 'process'],
                ],
                [
                    'and',
                    ['=', 'tran.can_settlement', 'Y'],
                    ['=', 'tran.status', 'finished'],
                ],
                [
                    'and',
                    ['=', 'tran.can_settlement', 'N'],
                    ['=', 'tran.status', 'new'],
                ],
            ])
            ->select([
                'date' => 'DATE(tran.expect_settlement_at)',
                'tran.scenes_type',
                'tran.many_scenes',
                'tran.deposit',
                'tran.transfer_in',
                'total' => 'SUM(if(tran.amount < 0, -1, 1))',
                'total_amount' => 'SUM(tran.amount)',
                'total_principal_amount' => 'SUM(tran.origin_amount * if(tran.amount < 0, -1, 1))',
                'settlement_status' => 'CONCAT(tran.can_settlement, \':\', tran.status)',
            ])
            ->groupBy([
                'date',
                'tran.scenes_type',
                'tran.many_scenes',
                'tran.deposit',
                'tran.transfer_in',
                'settlement_status',
            ]);

        $this->load($params);
        if (!$this->validate() || empty(self::getDepositListByUser())) {
            return $query->andWhere(['tran.deposit' => 'false']);
        }

        $query->andWhere([
            'and',
            ['>=', 'tran.expect_settlement_at', $this->startDate],
            ['<', 'tran.expect_settlement_at', Carbon::parse($this->endDate)->addDay()->toDateString()],
        ])->andFilterWhere([
            'tran.scenes_type' => $this->scenes_type,
            'tran.many_scenes' => $this->many_scenes,
            'tran.deposit' => $this->deposit,
            'tran.transfer_in' => $this->transfer_in,
            'tran.can_settlement' => $this->can_settlement,
            'tran.status' => $this->status,
        ]);

        return $query;
    }
}
