<?php

namespace capital\services;

use capital\models\CleanClearingTrans;
use capital\models\CleanSettlementAudit;
use Carbon\Carbon;
use common\models\Asset;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Yii;
use yii\base\UserException;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class CleanClearingTransService extends CleanClearingTrans
{
    public $expectSettlementDate;
    public $channel;
    public $type;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            //            [['deposit'], 'default', 'value' => Deposit::DEPOSIT_JN],
            //            [['deposit'], 'required'],
            [['channel'], 'default', 'value' => 'hainan'],
            [['type'], 'string'],
            [['channel', 'expectSettlementDate'], 'string'],
        ];
    }

    /**
     * 获取资方需要在大单手动结算页面处理的费用类型
     *
     * @param string $grantChannel
     *
     * @return array
     * @throws KVException
     */
    public static function getChannelAmountList(string $grantChannel): array
    {
        $config = KeyValue::take('dcs_channel_manual_amount_type', 'biz-dcs');

        $amountList = $config[$grantChannel] ?? $config[CleanClearingTrans::DEFAULT];

        return ArrayHelper::filter(CleanClearingTrans::AMOUNT_TYPE_LIST, $amountList);
    }

    /**
     * @param array $params
     *
     * @return ActiveQuery
     * @throws KVException
     */
    public function expectationSettlementQuery(array $params): ActiveQuery
    {
        $this->load($params);

        if (!$this->validate()) {
            Yii::error($this->errors, __METHOD__);
        }

        $query = self::find()
            ->where([
                'clean_clearing_trans.is_need_settlement' => self::NEED_SETTLEMENT_YES,
                'clean_clearing_trans.can_settlement' => self::CAN_SETTLEMENT_NO,
                'clean_clearing_trans.status' => self::STATUS_NEW,
            ])
            ->andFilterWhere([
//                'clean_clearing_trans.deposit' => $this->deposit,
                'clean_clearing_trans.loan_channel' => $this->channel,
                'clean_clearing_trans.repay_type' => $this->type,
            ])
            ->andWhere(
                "NOT exists(SELECT clean_settlement_audit.id
                     FROM clean_settlement_audit_clearing_log,
                          clean_settlement_audit
                    WHERE clean_clearing_trans.id = clean_settlement_audit_clearing_log.clean_clearing_trans_id
                      AND clean_settlement_audit_clearing_log.clean_settlement_audit_id =
                          clean_settlement_audit.id
                      AND clean_settlement_audit.status <> :fail )",
                ['fail' => CleanSettlementAudit::STATUS_FAIL]
            )
            ->andWhere(['clean_clearing_trans.amount_type' => array_keys(self::getChannelAmountList($this->channel))])
            ->select([
                'clean_clearing_trans.can_settlement',
                'clean_clearing_trans.status',
                'clean_clearing_trans.deposit',
            ]);

        if (empty($this->expectSettlementDate)) {
            $this->expectSettlementDate = Carbon::yesterday()->toDateString();
        }
        $expectSettlementStartDate = $this->expectSettlementDate;
        $expectSettlementEndDate = Carbon::parse($this->expectSettlementDate)->addDay()->toDateString();
        $query
            ->addSelect([
                'biz_sub_type' => new Expression(
                    'if(clean_clearing_trans.repay_type=\'buyback\', \'compensate\', clean_clearing_trans.repay_type)'
                ),
            ])
            ->andWhere([
                'and',
                ['>=', 'clean_clearing_trans.expect_settlement_at', $expectSettlementStartDate],
                ['<', 'clean_clearing_trans.expect_settlement_at', $expectSettlementEndDate],
            ]);

        return $query;
    }

    /**
     * @param string|array|null $types
     *
     * @return array
     */
    public static function buildAmountSelects($types = null): array
    {
        if (null === $types) {
            $types = array_keys(self::AMOUNT_TYPE_LIST);
        } else {
            $types = (array)$types;
        }

        $selects = [];
        foreach ($types as $type) {
            $exp = vsprintf('SUM(IF(clean_clearing_trans.amount_type = \'%s\', clean_clearing_trans.amount, 0))', [
                $type,
            ]);

            $selects[$type] = new Expression($exp);
        }

        return $selects;
    }
}
