<?php

namespace capital\models;

use capital\deposit\QueryInterface;
use capital\deposit\TransferInterface;
use yii\base\UserException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\helpers\Json;

/**
 * This is the model class for table "clearing_account_transfer".
 *
 * @property int $clearing_account_transfer_id
 * @property string $clearing_account_transfer_merchant_key
 * @property string $clearing_account_transfer_amount
 * @property string $clearing_account_transfer_out
 * @property string $clearing_account_transfer_in
 * @property string $clearing_account_transfer_status
 * @property string $clearing_account_transfer_start_at
 * @property string $clearing_account_transfer_finish_at
 * @property string $clearing_account_transfer_channel_key
 * @property string $clearing_account_transfer_channel_code
 * @property string $clearing_account_transfer_channel_message
 * @property string $clearing_account_transfer_comment
 * @property string $clearing_account_transfer_create_at
 * @property string $clearing_account_transfer_update_at
 * @property string $clearing_account_transfer_deposit
 * @property string $clearing_account_transfer_mode
 * @property DepositAccount $outAccount
 * @property DepositAccount $inAccount
 */
class ClearingAccountTransfer extends ActiveRecord implements TransferInterface, QueryInterface
{
    use GetDbTrait;

    /**
     * 转账临时密码
     */
    public $passwordNo;

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'clearing_account_transfer';
    }

    public function getDeposit(): string
    {
        return $this->clearing_account_transfer_deposit;
    }

    public function getTransferArgs(): array
    {
        $outAccount = DepositAccount::findByNumberAndDeposit(
            $this->clearing_account_transfer_out,
            $this->clearing_account_transfer_deposit
        );

        $inAccount = DepositAccount::findByNumberAndDeposit(
            $this->clearing_account_transfer_in,
            $this->clearing_account_transfer_deposit
        );

        return [
            $this->clearing_account_transfer_merchant_key,
            $this->clearing_account_transfer_channel_key,
            $outAccount->identity,
            $inAccount->identity,
            $this->clearing_account_transfer_amount,
            $this->clearing_account_transfer_comment,
            $this->passwordNo,
        ];
    }

    public function getQueryArgs(): array
    {
        return [
            $this->clearing_account_transfer_merchant_key,
            $this->clearing_account_transfer_amount,
        ];
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'clearing_account_transfer_merchant_key',
                    'clearing_account_transfer_amount',
                    'clearing_account_transfer_out',
                    'clearing_account_transfer_in',
                ],
                'required',
            ],
            [['clearing_account_transfer_amount'], 'integer'],
            [
                [
                    'clearing_account_transfer_status',
                    'clearing_account_transfer_comment',
                    'clearing_account_transfer_deposit',
                    'clearing_account_transfer_mode',
                ],
                'string',
            ],
            [
                [
                    'clearing_account_transfer_start_at',
                    'clearing_account_transfer_create_at',
                    'clearing_account_transfer_update_at',
                ],
                'safe',
            ],
            [
                [
                    'clearing_account_transfer_merchant_key',
                    'clearing_account_transfer_out',
                    'clearing_account_transfer_in',
                    'clearing_account_transfer_channel_key',
                ],
                'string',
                'max' => 64,
            ],
            [['clearing_account_transfer_finish_at', 'clearing_account_transfer_channel_code'], 'string', 'max' => 45],
            [['clearing_account_transfer_channel_message'], 'string', 'max' => 255],
            [['clearing_account_transfer_merchant_key'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'clearing_account_transfer_id' => '主键',
            'clearing_account_transfer_merchant_key' => '交易流水号',
            'clearing_account_transfer_amount' => '交易金额',
            'clearing_account_transfer_out' => '转出方',
            'clearing_account_transfer_in' => '转入方',
            'clearing_account_transfer_status' => '交易状态',
            'clearing_account_transfer_start_at' => '请求发起时间',
            'clearing_account_transfer_finish_at' => '请求完成时间',
            'clearing_account_transfer_channel_key' => '渠道流水号',
            'clearing_account_transfer_channel_code' => '渠道响应码',
            'clearing_account_transfer_channel_message' => '渠道响应结果',
            'clearing_account_transfer_comment' => '备注',
            'clearing_account_transfer_create_at' => '创建时间',
            'clearing_account_transfer_update_at' => '更新时间',
            'clearing_account_transfer_deposit' => '存管系统',
            'clearing_account_transfer_mode' => '转账模式',
        ];
    }

    public function getOutAccount(): ActiveQuery
    {
        return $this->hasOne(DepositAccount::class, [
            'number' => 'clearing_account_transfer_out',
            'deposit' => 'clearing_account_transfer_deposit',
        ]);
    }

    public function getInAccount(): ActiveQuery
    {
        return $this->hasOne(DepositAccount::class, [
            'number' => 'clearing_account_transfer_in',
            'deposit' => 'clearing_account_transfer_deposit',
        ]);
    }

    /**
     * @param array $params
     *
     * @return ClearingAccountTransfer
     * @throws UserException
     */
    public static function import(array $params): ClearingAccountTransfer
    {
        $transfer = new self();
        if (!($transfer->load($params, '') && $transfer->save())) {
            throw new UserException(Json::encode($transfer->getErrors()));
        }

        return $transfer;
    }
}
