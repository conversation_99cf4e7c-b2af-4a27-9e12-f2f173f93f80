<?php

namespace capital\models;

use Carbon\Carbon;
use common\models\BizLabel;
use kvmanager\KVException;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;

/**
 * DepositManualSearch represents the model behind the search form about `common\models\ClearingManual`.
 */
class DepositManualSearch extends ClearingManual
{
    public $create_start;
    public $create_end;
    public $finish_start;
    public $finish_end;

    public function formName(): string
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['clearing_manual_transfer_deposit'], 'default', 'value' => Deposit::DEPOSIT_JN],
            [['create_start'], 'default', 'value' => Carbon::parse('6 month ago')->toDateString()],
            [['create_start', 'create_end', 'finish_start', 'finish_end'], 'string'],
            [['clearing_manual_id', 'clearing_manual_amount', 'clearing_manual_operator_id'], 'integer'],
            [
                [
                    'clearing_manual_merchant_key',
                    'clearing_manual_type',
                    'clearing_manual_transfer_out',
                    'clearing_manual_transfer_in',
                    'clearing_manual_operator_name',
                    'clearing_manual_operator_email',
                    'clearing_manual_status',
                    'clearing_manual_comment',
                    'clearing_manual_finish_at',
                    'clearing_manual_create_at',
                    'clearing_manual_update_at',
                    'clearing_manual_reserve_type',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     * @throws KVException
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = self::find()
            ->with([
                'inAccount',
                'outAccount',
                'updateOperator',
            ]);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => [
                    'clearing_manual_finish_at',
                    'clearing_manual_create_at',
                ],
                'defaultOrder' => [
                    'clearing_manual_create_at' => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $reservePrefix = ArrayHelper::getValue($params, 'reserve_prefix');
        if (array_key_exists($reservePrefix, self::RESERVE_PREFIX)) {
            if (empty($this->clearing_manual_reserve_type)) {
                if ($reservePrefix === 'in') {
//                    $types = self::reserveInTypes($this->clearing_manual_transfer_deposit, false);
                    $types = BizLabel::inTypes();
                } else {
//                    $types = self::reserveOutTypes($this->clearing_manual_transfer_deposit, false);
                    $types = BizLabel::outTypes();
                }
                $query->andWhere([
                    'clearing_manual_reserve_type' => array_keys($types),
                ]);
            } else {
                $query->andFilterWhere([
                    'clearing_manual_reserve_type' => $this->clearing_manual_reserve_type,
                ]);
            }
        }

        // grid filtering conditions
        $query
            ->andWhere([
                'clearing_manual_transfer_deposit' => $this->clearing_manual_transfer_deposit,
            ])
            ->andFilterWhere([
                'and',
                ['>=', 'clearing_manual_create_at', $this->create_start],
                [
                    '<',
                    'clearing_manual_create_at',
                    $this->create_end ? Carbon::parse($this->create_end)->addDay()->toDateString() : null,
                ],
                ['>=', 'clearing_manual_finish_at', $this->finish_start],
                [
                    '<',
                    'clearing_manual_finish_at',
                    $this->finish_end ? Carbon::parse($this->finish_end)->addDay()->toDateString() : null,
                ],
            ])
            ->andFilterWhere([
                'clearing_manual_merchant_key' => $this->clearing_manual_merchant_key,
                'clearing_manual_type' => $this->clearing_manual_type,
                'clearing_manual_transfer_out' => $this->clearing_manual_transfer_out,
                'clearing_manual_transfer_in' => $this->clearing_manual_transfer_in,
                'clearing_manual_status' => $this->clearing_manual_status,
            ])
            ->andFilterWhere(['like', 'clearing_manual_operator_name', $this->clearing_manual_operator_name])
            ->andFilterWhere(['like', 'clearing_manual_comment', $this->clearing_manual_comment]);

        return $dataProvider;
    }
}
