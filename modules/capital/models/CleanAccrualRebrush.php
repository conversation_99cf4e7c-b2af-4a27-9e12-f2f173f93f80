<?php

namespace capital\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%clean_accrual_rebrush}}".
 *
 * @property int $id
 * @property string $batch_no
 * @property string $asset_item_no
 * @property int $period
 * @property string $old_tech_account
 * @property string $new_tech_account
 * @property string $new_rate
 * @property string $old_rate
 * @property int $new_allocated_amount
 * @property int $old_allocated_amount
 * @property string $status
 * @property string $comment
 * @property string $operator
 * @property string $create_at
 * @property string $update_at
 */
class CleanAccrualRebrush extends ActiveRecord
{
    use GetDbTrait;

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'clean_accrual_rebrush';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                ['batch_no', 'asset_item_no', 'period', 'new_tech_account', 'new_rate', 'new_allocated_amount'],
                'required',
            ],
            [['period', 'new_allocated_amount', 'old_allocated_amount'], 'integer'],
            [['new_rate', 'old_rate'], 'number'],
            [['create_at', 'update_at'], 'safe'],
            [['batch_no'], 'string', 'max' => 128],
            [['asset_item_no', 'old_tech_account', 'new_tech_account'], 'string', 'max' => 64],
            [['status'], 'string', 'max' => 16],
            [['comment'], 'string', 'max' => 512],
            [['operator'], 'string', 'max' => 32],
            [
                ['batch_no', 'asset_item_no', 'period'],
                'unique',
                'targetAttribute' => ['batch_no', 'asset_item_no', 'period'],
                'message' => 'The combination of 处理批次号, 资产编号 and 期次 has already been taken.',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id' => '主键',
            'batch_no' => '处理批次号',
            'asset_item_no' => '资产编号',
            'period' => '期次',
            'old_tech_account' => '老科技公司账户',
            'new_tech_account' => '新科技公司账户',
            'new_rate' => '新分配比例',
            'old_rate' => '老分配比例',
            'new_allocated_amount' => '新分配金额',
            'old_allocated_amount' => '老分配金额',
            'status' => 'new：新建 process：处理中 success：处理成功 fail:失败',
            'comment' => '备注',
            'operator' => '操作人',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
        ];
    }
}
