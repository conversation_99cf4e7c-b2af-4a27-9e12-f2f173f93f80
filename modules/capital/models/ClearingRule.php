<?php

namespace capital\models;

use common\models\User;
use yii\behaviors\BlameableBehavior;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%clean_clearing_rule}}".
 *
 * @property int $id
 * @property string $no
 * @property string $value
 * @property string $memo
 * @property string $status
 * @property int $create_user
 * @property int $update_user
 * @property string $create_at
 * @property string $update_at
 * @property string $support_rule_no
 * @property string $product_code
 * @property-read User $creator
 * @property-read User $updater
 */
class ClearingRule extends ActiveRecord
{
    use GetDbTrait;

    public const STATUS_ACTIVE = 'active';
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_LIST = [
        self::STATUS_ACTIVE => '激活',
        self::STATUS_INACTIVE => '未激活',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'clean_clearing_rule';
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'create_user',
                'updatedByAttribute' => 'update_user',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['no', 'status'], 'required'],
            [['no', 'product_code'], 'string', 'max' => 100],
            [['status'], 'in', 'range' => array_keys(self::STATUS_LIST)],
            [['value', 'memo', 'support_rule_no'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id' => '主键',
            'no' => '键',
            'value' => '值',
            'memo' => '备注',
            'status' => '状态',
            'create_user' => '创建人员',
            'update_user' => '更新人员',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
            'support_rule_no' => '支持的费率编号',
            'product_code' => '产品编码',
        ];
    }

    public function getCreator(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'create_user']);
    }

    public function getUpdater(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'update_user']);
    }
}
