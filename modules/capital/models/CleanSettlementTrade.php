<?php

namespace capital\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "clean_settlement_trade".
 *
 * @property int $id
 * @property int $settlement_id
 * @property string $batch_no
 * @property string $type
 * @property string $transfer_out_channel_code
 * @property string $transfer_in_channel_code
 * @property string $deposit
 * @property string $amount
 * @property string $status
 * @property string $order_no
 * @property string $trade_no
 * @property string $comment
 * @property string $finish_at
 * @property string $create_at
 * @property string $update_at
 */
class CleanSettlementTrade extends ActiveRecord
{
    use GetDbTrait;

    public const TYPE_WITHDRAWAL = 'withdrawal';
    public const TYPE_TRANSFER = 'transfer';

    public const STATUS_READY = 'ready';
    public const STATUS_PROCESS = 'process';
    public const STATUS_FAIL = 'fail';
    public const STATUS_SUCCESS = 'success';
    public const STATUS_LIST = [
        self::STATUS_READY => '待处理',
        self::STATUS_PROCESS => '处理中',
        self::STATUS_FAIL => '失败',
        self::STATUS_SUCCESS => '完成',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'clean_settlement_trade';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'settlement_id',
                    'batch_no',
                    'type',
                    'transfer_out_channel_code',
                    'transfer_in_channel_code',
                    'amount',
                    'order_no',
                    'trade_no',
                ],
                'required',
            ],
            [['settlement_id', 'amount'], 'integer'],
            [['finish_at', 'create_at', 'update_at'], 'safe'],
            [['batch_no', 'transfer_out_channel_code', 'transfer_in_channel_code'], 'string', 'max' => 128],
            [['type', 'status'], 'string', 'max' => 16],
            [['deposit'], 'string', 'max' => 32],
            [['order_no', 'trade_no'], 'string', 'max' => 64],
            [['comment'], 'string', 'max' => 100],
            [['trade_no'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id' => '主键',
            'settlement_id' => '结算单id',
            'batch_no' => '批次号',
            'type' => '交易类型 transfer:转账  withdrawal:提现',
            'transfer_out_channel_code' => '转出方存管账号',
            'transfer_in_channel_code' => '转入方存管账号',
            'deposit' => '存管系统  jining:济宁腾桥存管,miyang:济宁觅杨存管,tengqiao:恒丰腾桥存管',
            'amount' => '交易金额，单位为分',
            'status' => '状态：ready process fail success',
            'order_no' => '订单号 转账对应转账订单号，提现对应提现订单号',
            'trade_no' => '交易流水号',
            'comment' => '备注',
            'finish_at' => '完成时间',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
        ];
    }
}
