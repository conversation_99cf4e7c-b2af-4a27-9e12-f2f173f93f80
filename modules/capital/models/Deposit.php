<?php

namespace capital\models;

use kvmanager\models\KeyValue;
use yii\base\Model;

class Deposit extends Model
{
    public const DEPOSIT_TQ = 'tengqiao';
    public const DEPOSIT_JN = 'jining';

    /**
     * @return array
     */
    public static function config(): array
    {
        return (array)KeyValue::take('deposit_config', 'biz-dcs');
    }

    /**
     * 获取存管名称
     *
     * @param $deposit
     *
     * @return string
     */
    public static function depositName($deposit): string
    {
        $config = self::config();

        return $config['names'][$deposit] ?? $deposit;
    }

    public static function deposits(): array
    {
        $config = self::config();

        return $config['names'] ?? [];
    }
}
