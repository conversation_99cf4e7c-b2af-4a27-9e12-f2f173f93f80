<?php

namespace capital\models;

use kvmanager\models\KeyValue;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "clean_withdraw_order".
 *
 * @property int $id
 * @property string $order_no
 * @property string $tally_no
 * @property int $status
 * @property int $amount
 * @property string $loan_channel
 * @property string $withhold_channel
 * @property string $payment_channel
 * @property string $channel_code
 * @property string $deposit
 * @property string $biz_type
 * @property string $memo
 * @property string $message
 * @property string $execute_at
 * @property string $operate_type
 * @property string $finish_at
 * @property string $create_at
 * @property string $update_at
 */
class CleanWithdrawOrder extends ActiveRecord
{
    use GetDbTrait;

    public const STATUS_INIT = 'init';
    public const STATUS_PROCESS = 'process';
    public const STATUS_SUCCESS = 'success';
    public const STATUS_FAIL = 'fail';

    public const TALLY_STATUS_INIT = 'init';
    public const TALLY_STATUS_PROCESS = 'process';
    public const TALLY_STATUS_SUCCESS = 'success';
    public const TALLY_STATUS_FAIL = 'fail';
    public const TALLY_STATUS_REVOKE_PROCESS = 'revoke_process';
    public const TALLY_STATUS_REVOKE_SUCCESS = 'revoke_success';

    public const WITHDRAW_STATUS_INIT = 'init';
    public const WITHDRAW_STATUS_PROCESS = 'process';
    public const WITHDRAW_STATUS_SUCCESS = 'success';
    public const WITHDRAW_STATUS_FAIL = 'fail';

    public const OPERATE_TYPE_AUTO = 'auto';
    public const OPERATE_TYPE_MANUAL = 'manual';

    public static $statusList = [
        self::STATUS_INIT => '初始',
        self::STATUS_PROCESS => '归集中',
        self::STATUS_SUCCESS => '归集完成',
        self::STATUS_FAIL => '归集失败',
    ];

    public static $tallyStatusList = [
        self::TALLY_STATUS_INIT => '初始',
        self::TALLY_STATUS_PROCESS => '处理中',
        self::TALLY_STATUS_SUCCESS => '成功',
        self::TALLY_STATUS_FAIL => '失败',
        self::TALLY_STATUS_REVOKE_PROCESS => '记账撤销处理中',
        self::TALLY_STATUS_REVOKE_SUCCESS => '记账撤销成功',
    ];

    public static $withdrawStatusList = [
        self::WITHDRAW_STATUS_INIT => '初始',
        self::WITHDRAW_STATUS_PROCESS => '处理中',
        self::WITHDRAW_STATUS_SUCCESS => '成功',
        self::WITHDRAW_STATUS_FAIL => '失败',
    ];

    public static $operateTypeStatus = [
        self::OPERATE_TYPE_AUTO => '自动归集',
        self::OPERATE_TYPE_MANUAL => '手动归集',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'clean_withdraw_order';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'payment_channel',
                    'deposit',
                    'channel_code',
                    'amount',
                    'memo',
                ],
                'required',
            ],
            [['amount'], 'integer'],
            [
                ['amount'],
                'integer',
                'min' => 1,
                'max' => 99999999999,
                'tooSmall' => '{attribute}不能小于0.01元',
                'tooBig' => '{attribute}最多不超过999999999.99元',
            ],
            [
                [
                    'order_no',
                    'tally_no',
                    'status',
                    'loan_channel',
                    'withhold_channel',
                    'execute_at',
                    'finish_at',
                    'memo',
                    'biz_type',
                    'operate_type',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'order_no' => '订单号',
            'tally_no' => '归集号',
            'status' => '归集状态',
            'tally_status' => '记账状态',
            'withdraw_status' => '代付状态',
            'deposit' => '归集存管',
            'amount' => '金额',
            'loan_channel' => '放款渠道',
            'withhold_channel' => '代扣通道',
            'payment_channel' => '代付通道',
            'channel_code' => '通道编号',
            'trade_no' => '流水号',
            'comment' => '备注',
            'execute_at' => '执行时间',
            'finish_at' => '完成时间',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
            'memo' => '备注',
            'message' => '结果',
            'operate_type' => '操作类型',
            'biz_type' => '业务类型',
        ];
    }

    public static function getPaymentChannelList(): array
    {
        $config = KeyValue::take('payment_channel_config', 'biz-dcs');
        $result = [];
        $config = $config['clean_withdraw_order']['value'] ?? [];
        foreach ($config as $item) {
            $result[$item] = $item;
        }

        return $result;
    }

    public static function getDepositList(): array
    {
        $result = [];
        $list = DepositAccount::find()
            ->where([
                'type' => 1,
            ])
            ->distinct('deposit')
            ->select('deposit')->column();
        foreach ($list as $item) {
            $result[$item] = $item;
        }

        return $result;
    }

    public static function getChannelCodeList(): array
    {
        $result = DepositAccount::find()
            ->where([
                'type' => 1,
            ])
            ->distinct('identity')
            ->select('identity')->column();
        $data = [];
        foreach ($result as $item) {
            $data[$item] = $item;
        }

        return $data;
    }

    public static function getChannelName($identity): string
    {
        $depositAccount = DepositAccount::findOne(['identity' => $identity]);

        return $depositAccount->name ?? '-';
    }
}
