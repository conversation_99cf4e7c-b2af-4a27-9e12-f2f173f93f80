<?php

namespace capital\tasks;

use capital\deposit\DepositComponent;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;
use yii\helpers\Json;

class SyncOfflineRecharge extends TaskHandler
{
    public $date;
    public $deposit;
    public $transfer_in;
    public $amount;

    public function rules(): array
    {
        return [
            [['date', 'deposit', 'transfer_in', 'amount'], 'required'],
            [['amount'], 'integer'],
        ];
    }

    /**
     * @return TaskResult
     * @throws UserException
     */
    public function process(): TaskResult
    {
        $client = DepositComponent::instance($this->deposit);
        if (!$client->offlineRechargeSync($this->deposit, $this->date, $this->transfer_in, $this->amount)) {
            throw new UserException('线下充值补录信息同步失败:'.Json::encode($client->getResponse()));
        }

        return TaskResult::success($client->getResponse());
    }
}
