<?php

namespace capital\tasks;

use capital\Consts;
use capital\deposit\DepositComponent;
use capital\deposit\QueryInterface;
use capital\models\ClearingAccountTransfer;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Throwable;
use xlerr\httpca\RequestClient;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;
use yii\db\Exception;
use yii\helpers\Json;

class ClearingManualTransferQuery extends TaskHandler
{
    public $merchantKey;
    public $retryTimes;

    protected static function generateKey($data, $options = []): string
    {
        return $data['merchantKey'] ?? parent::generateKey($data, $options);
    }

    public function rules(): array
    {
        return [
            [['retryTimes'], 'default', 'value' => 0],
            [['merchantKey'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws UserException
     * @throws GuzzleException
     */
    public function process(): TaskResult
    {
        $transfer = ClearingAccountTransfer::findOne([
            'clearing_account_transfer_merchant_key' => $this->merchantKey,
        ]);

        if (!$transfer) {
            throw new UserException('记录不存在:' . $this->merchantKey);
        }

        if (!$transfer instanceof QueryInterface) {
            throw new UserException('交易记录对象必须继承至:' . QueryInterface::class);
        }

        if ($transfer->clearing_account_transfer_status !== Consts::STATUS_PROCESS) {
            return TaskResult::success([
                'memo' => '交易不是处理中状态，直接忽略',
            ]);
        }

        // 查询代付结果
        $depositComponent = DepositComponent::instance($transfer->getDeposit());
        $response = $depositComponent->query(...$transfer->getQueryArgs());

        // 接口异常直接重试
        if ($response['code'] !== RequestClient::SUCCESS) {
            $this->retry();

            return TaskResult::success($response);
        }

        $responseData = $response['data'];
        switch ($responseData['status']) {
            case Consts::CLEARING_TRANSFER_STATUS_NEW:
                $this->retry();
                $newInfo = [
                    'clearing_account_transfer_channel_message' => '未处理',
                    'clearing_account_transfer_channel_code' => '0',
                ];
                break;
            case Consts::CLEARING_TRANSFER_STATUS_PROCESSING:
                $this->retry();
                $newInfo = [
                    'clearing_account_transfer_channel_message' => '处理中',
                    'clearing_account_transfer_channel_code' => '1',
                ];
                break;
            case Consts::CLEARING_TRANSFER_STATUS_FAIL:
                $newInfo = [
                    'clearing_account_transfer_status' => Consts::STATUS_FAIL,
                    'clearing_account_transfer_channel_message' => '处理失败',
                    'clearing_account_transfer_channel_code' => '3',
                ];
                break;
            case Consts::CLEARING_TRANSFER_STATUS_SUCCESS:
                $tradeData = $responseData['trades'][0] ?? null;
                if ($tradeData['status'] !== Consts::CLEARING_TRANSFER_STATUS_SUCCESS) {
                    throw new UserException('订单状态和交易状态不一致: ' . Json::encode($response));
                }

                $newInfo = [
                    'clearing_account_transfer_status' => Consts::STATUS_SUCCESS,
                    'clearing_account_transfer_channel_message' => $tradeData['memo'] ?? '无',
                    'clearing_account_transfer_channel_code' => (string)$tradeData['status'],
                ];
                break;
            case Consts::CLEARING_TRANSFER_STATUS_NOTHING:
                $newInfo = [
                    'clearing_account_transfer_status' => Consts::STATUS_FAIL,
                    'clearing_account_transfer_channel_message' => '交易不存在',
                    'clearing_account_transfer_channel_code' => '6',
                ];
                break;
            default:
                throw new UserException('交易类型错误');
        }

        if (!empty($tradeData['finished_at'])) {
            $finishAt = Carbon::createFromTimestampMs($responseData['finished_at'])->toDateTimeString();
            $newInfo['clearing_account_transfer_finish_at'] = $finishAt;
        }

        $newInfo['clearing_account_transfer_update_at'] = Carbon::now()->toDateTimeString();

        $condition = [
            'clearing_account_transfer_id' => $transfer->clearing_account_transfer_id,
            'clearing_account_transfer_status' => Consts::STATUS_PROCESS,
        ];
        $affected = ClearingAccountTransfer::updateAll($newInfo, $condition);
        if ($affected !== 1) {
            throw new UserException('保存数据失败:' . Json::encode([$newInfo, $condition]));
        }

        return TaskResult::success($response);
    }

    protected function retry()
    {
        self::make([
            'merchantKey' => $this->merchantKey,
            'retryTimes' => $this->retryTimes + 1,
        ], [
            'task_next_run_date' => Carbon::parse('5 minute')->toDateTimeString(),
            'task_priority' => $this->task->task_priority,
        ]);
    }
}
