<?php

namespace capital\tasks;

use common\components\DcsComponent;
use Exception;
use xlerr\task\TaskHandler;

class AccrualRebrushNotify extends TaskHandler
{
    public $batchNo;

    public function rules(): array
    {
        return [
            [['batchNo'], 'required'],
        ];
    }

    protected static function generateKey($data, $options = []): string
    {
        return $data['batchNo'];
    }

    /**
     * 推送给考拉走我方通道提前还款清单的明细
     *
     * @return array
     * @throws Exception
     */
    public function process(): array
    {
        $client = DcsComponent::instance();

        $client->accrualReBrush([
            'from_system' => $this->task->task_from_system,
            'key' => $this->task->task_key,
            'type' => 'AccrualReBrush',
            'data' => [
                'batch_no' => $this->batchNo,
            ],
        ]);

        return $client->getResponse();
    }
}
