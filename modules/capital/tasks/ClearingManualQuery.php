<?php

namespace capital\tasks;

use capital\Consts;
use capital\deposit\QueryInterface;
use capital\models\ClearingAccountTransfer;
use capital\models\ClearingAccountWithdraw;
use capital\models\ClearingManual;
use Carbon\Carbon;
use RuntimeException;
use Throwable;
use waterank\audit\task\LinkCardTvMessageTask;
use xlerr\common\helpers\MoneyHelper;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;
use yii\db\Exception;

class ClearingManualQuery extends TaskHandler
{
    public $merchantKey;
    public $retryTimes;

    private string $fieldPrefix = '';

    protected static function generateKey($data, $options = []): string
    {
        return $data['merchantKey'] ?? parent::generateKey($data, $options);
    }

    public function rules(): array
    {
        return [
            [['retryTimes'], 'default', 'value' => 0],
            [['merchantKey'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws Exception
     * @throws Throwable
     * @throws UserException
     */
    public function process(): TaskResult
    {
        $clearingManual = ClearingManual::findOne([
            'clearing_manual_merchant_key' => $this->merchantKey,
        ]);
        if (!$clearingManual) {
            throw new UserException('记录不存在:' . $this->merchantKey);
        }

        if ($clearingManual->clearing_manual_type === Consts::CLEARING_MANUAL_TYPE_TRANSFER) {
            $this->fieldPrefix = 'clearing_account_transfer';
            $trade = ClearingAccountTransfer::findOne([
                'clearing_account_transfer_merchant_key' => $this->merchantKey,
            ]);
        } elseif ($clearingManual->clearing_manual_type === Consts::CLEARING_MANUAL_TYPE_WITHDRAW) {
            $this->fieldPrefix = 'clearing_account_withdraw';
            $trade = ClearingAccountWithdraw::findOne([
                'clearing_account_withdraw_merchant_key' => $this->merchantKey,
            ]);
        } else {
            $trade = null;
        }

        if (!$trade) {
            throw new UserException('记录不存在:' . $this->merchantKey);
        }

        if (!$trade instanceof QueryInterface) {
            throw new UserException('交易记录对象必须继承至:' . QueryInterface::class);
        }

        $this->handleResult($clearingManual, $trade);

        return TaskResult::success();
    }

    /**
     * @param ClearingManual $clearingManual
     * @param ClearingAccountWithdraw|ClearingAccountTransfer $trade
     *
     * @throws UserException
     * @throws Throwable
     */
    private function handleResult(ClearingManual $clearingManual, $trade): void
    {
        $clearingManual->clearing_manual_status = $this->tradeAttribute($trade, 'status');
        $clearingManual->clearing_manual_finish_at = $this->tradeAttribute($trade, 'finish_at');
        $clearingManual->clearing_manual_update_at = $this->tradeAttribute($trade, 'update_at');
        $clearingManual->clearing_manual_result = $this->tradeAttribute($trade, 'channel_message');
        // 有可能新值和旧值一样所以更新操作返回`0`
        $clearingManual->update();

        if ($clearingManual->clearing_manual_status === Consts::STATUS_PROCESS) {
            self::make([
                'merchantKey' => $this->merchantKey,
                'retryTimes' => $this->retryTimes + 1,
            ], [
                'task_next_run_date' => Carbon::parse('1 minute')->toDateTimeString(),
                'task_priority' => $this->task->task_priority,
            ]);
        } elseif ($clearingManual->clearing_manual_status === Consts::STATUS_SUCCESS) {
            LinkCardTvMessageTask::make([
                'emails' => [$clearingManual->clearing_manual_operator_email],
                'title' => sprintf('# %s成功', Consts::CLEARING_MANUAL_TYPE[$clearingManual->clearing_manual_type]),
                'content' => vsprintf("流水号: %s\n金额: %s\n备注: %s", [
                    $clearingManual->clearing_manual_merchant_key,
                    MoneyHelper::f2y($clearingManual->clearing_manual_amount, true),
                    $clearingManual->clearing_manual_comment,
                ]),
                'url' => 'https://biz.kuainiujinke.com/capital/deposit-manual/view?id=' . $clearingManual->clearing_manual_id,
            ]);
        } elseif ($clearingManual->clearing_manual_status === Consts::STATUS_FAIL) {
            LinkCardTvMessageTask::make([
                'emails' => [$clearingManual->clearing_manual_operator_email],
                'title' => sprintf('# %s失败', Consts::CLEARING_MANUAL_TYPE[$clearingManual->clearing_manual_type]),
                'content' => vsprintf("流水号: %s\n金额: %s\n备注: %s", [
                    $clearingManual->clearing_manual_merchant_key,
                    MoneyHelper::f2y($clearingManual->clearing_manual_amount, true),
                    $clearingManual->clearing_manual_comment,
                ]),
                'url' => 'https://biz.kuainiujinke.com/capital/deposit-manual/view?id=' . $clearingManual->clearing_manual_id,
            ]);
        } elseif ($clearingManual->clearing_manual_status === Consts::STATUS_NEW) {
            throw new RuntimeException('不可能的情况: 已经是处理中的交易不可能将状态变更为待处理');
        } else {
            throw new RuntimeException(vsprintf('该手动转账记录[%s]转账状态不正确[%s]', [
                $clearingManual->clearing_manual_id,
                $clearingManual->clearing_manual_status,
            ]));
        }
    }

    /**
     * @param ClearingAccountTransfer|ClearingAccountWithdraw $trade
     * @param string $attribute
     *
     * @return mixed
     */
    private function tradeAttribute($trade, string $attribute)
    {
        return $trade[$this->fieldPrefix . '_' . $attribute] ?? null;
    }
}
