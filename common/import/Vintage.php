<?php

namespace common\import;

use Carbon\Carbon;
use Carbon\Exceptions\InvalidFormatException;
use Exception;
use import\services\ChunkReader;
use RuntimeException;
use Throwable;
use yii\base\BaseObject;
use yii\base\UserException;
use yii\web\UploadedFile;

class Vintage extends BaseObject implements BeforeUploadInterface
{
    public array $config;

    protected array $title = [];
    protected array $toRows = [];
    protected int $channelIndex;
    protected int $grantMonthIndex;

    protected array $newTitle = [];

    protected string $grantMonthFormat;

    /**
     * @var UploadedFile $file
     */
    public UploadedFile $file;

    /**
     * @throws UserException
     */
    public function process()
    {
        try {
            $this->parse();
            $file = tmpfile();
            fputcsv($file, $this->newTitle, '|');
            foreach ($this->toRows as $row) {
                fputcsv($file, $row, '|');
            }
            return $file;
        } catch (Throwable $exception) {
            throw new UserException('文件处理失败-' . $exception->getMessage());
        }
    }

    /**
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception|UserException
     * @throws Exception
     */
    protected function parse()
    {
        $skipRowNum = (int)($this->config['skip_rows'] ?? 0);
        $readRows = (int)($this->config['read_rows'] ?? 5000);
        $titlePosition = max((int)($this->config['title_position'] ?? 1), 1);
        $valueGetMethod = $this->config['value_get_method'] ?? '';
        $dataReader = new ChunkReader(
            $this->file->tempName, $readRows, $skipRowNum, $this->config['delimiter'] ?? null
        );

        $this->title = $dataReader->getTitle($titlePosition);
        $this->grantMonthIndex = $this->config[self::BEFORE_UPLOAD_KEY]['grant_month_index'] ?? 1;
        $this->channelIndex = $this->config[self::BEFORE_UPLOAD_KEY]['channel_index'] ?? 0;
        $this->newTitle = $this->config[self::BEFORE_UPLOAD_KEY]['new_title'] ?? $this->title;
        $this->grantMonthFormat = $this->config[self::BEFORE_UPLOAD_KEY]['grant_month_format'] ?? 'Y-m-d';

        $mobStartIndex = -1;
        for ($i = 0; $i < count($this->title); $i++) {
            if (strpos($this->title[$i], 'MOB') === 0) {
                $mobStartIndex = $i;
                break;
            }
        }

        if ($mobStartIndex === -1) {
            throw new Exception("未找到MOB列");
        }

        $titleCount = count($this->title);
        $keys = array_keys($this->title);

        foreach ($dataReader->dataIterator($valueGetMethod, $keys) as $i => $row) {
            if (empty($row[$this->channelIndex]) || empty($row[$this->grantMonthIndex])) {
                continue;
            }

            $productCode = $row[$this->channelIndex]; // product_code -> channel
            $grantMonth = $this->getGrantMonth($row[$this->grantMonthIndex], $i + 1); // grant_month
            for ($mobCol = $mobStartIndex; $mobCol < $titleCount; $mobCol++) {
                $mobHeader = $this->title[$mobCol];
                $vintageValue = $row[$mobCol] ?? null;

                if (!$this->isValidVintage($vintageValue)) {
                    continue;
                }

                $mob = $this->extractMobNumber($mobHeader);
                if ($mob === null) {
                    continue;
                }

                $vintage = $this->convertVintageValue($vintageValue);

                $this->toRows[] = [
                    'channel' => $productCode,
                    'grant_month' => $grantMonth,
                    'mob' => $mob,
                    'vintage' => $vintage,
                ];
            }
        }
    }

    /**
     * 提取MOB数字
     * @param string $mobHeader MOB列头
     * @return int|null MOB数字
     */
    private function extractMobNumber(string $mobHeader): ?int
    {
        if (preg_match('/MOB(\d+)/', $mobHeader, $matches)) {
            return (int)$matches[1];
        }
        return null;
    }

    /**
     * 检查vintage值
     * @param mixed $value 值
     * @return bool
     * @throws UserException
     */
    private function isValidVintage($value): bool
    {
        if (is_null($value) || $value === '') {
            return false;
        }

        //如果小数点超过6位，需提示产品人员
        if (preg_match('/^[+-]?\d+\.(?=\d{7,})\d+$/', $value)) {
            throw new UserException('vintage值验证出现错误,请仔细检查重新上传:' . $value);
        }

        return true;
    }


    /**
     * @param string $date
     * @param int $index
     * @return string
     */
    private function getGrantMonth(string $date, int $index): string
    {
        try {
            return Carbon::parse($date)->rawFormat($this->grantMonthFormat);
        } catch (InvalidFormatException $exception) {
            throw new RuntimeException(sprintf('文件第[%d]行的日期格式错误:{%s}', $index, $date));
        }
    }

    /**
     * 转换vintage值
     * @param string $value 原始值 (如: "0.88%", "10.10%")
     * @return string 转换后的值 (如: 0.88, 10.10)
     */
    private function convertVintageValue(string $value): string
    {
        $cleanValue = str_replace(['%', ' '], '', trim($value));

        return (string)$cleanValue;
    }
}