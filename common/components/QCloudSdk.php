<?php

namespace common\components;

use Exception;
use Qcloud\Cos\Client;
use xlerr\httpca\ComponentTrait;
use yii\base\InvalidConfigException;
use yii\helpers\Json;

/**
 * Class QCloudSdk
 *
 * @package common\components
 */
class QCloudSdk extends Client
{
    use ComponentTrait;

    public string $bucket;

    /**
     * QCloudSdk constructor.
     *
     * @param array $config
     *
     * @throws Exception
     */
    public function __construct(array $config = [])
    {
        $config =  Json::decode(' {
					"region": "sh",
					"credentials": {
						"appId": "1251122539",
						"secretId": "AKIDczq53oHafZWFk3CxxhEUWJsDahaJnEfv",
						"secretKey": "OYUAdtsdEcnhgRJwLWTBhyJVSAds87qV"
					},
					"bucket": "capital-test"
				}');
        $this->bucket = $config['bucket'] ?? '';
        parent::__construct($config);
    }

    /**
     * 获取云资源的链接
     *
     * @param string      $expired
     * @param string      $bucket
     * @param string      $path
     * @param string|null $fullUrl
     *
     * @return string
     * @throws InvalidConfigException
     */
    public static function getCloudUrl(string $expired, string $bucket, string $path, ?string $fullUrl = null): string
    {
        if ($fullUrl) {
            if (!preg_match('/^https?:\/\//i', $fullUrl)) {
                $fullUrl = 'https://' . $fullUrl;
            }
            $path = ltrim(parse_url($fullUrl, PHP_URL_PATH), '/');
        }

        return self::instance()->getObjectUrl($bucket, $path, $expired);
    }
}
