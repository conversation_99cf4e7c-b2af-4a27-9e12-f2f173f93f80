<?php

namespace common\components;

use Carbon\Carbon;
use datasource\tasks\workflow\BaseWorkflowTask;
use dcs\models\IncomeVerification;
use GuzzleHttp\RequestOptions;
use RuntimeException;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;

class DcsComponent extends RequestClient
{
    use ComponentTrait;

    public const FROM_SYSTEM = 'BIZ';

    /**
     * @param array $data
     *
     * @return bool
     */
    public function accrualReBrush(array $data): bool
    {
        return $this->post('accrual/re-brush', [
            RequestOptions::JSON => $data,
        ]);
    }

    public function accountRefresh(BaseWorkflowTask $baseWorkflowTask): bool
    {
        return $this->post('account/refresh', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'type' => 'OfflinefeeRefresh',
                'key' => $baseWorkflowTask->businessId,
                'data' => [
                    'fromType' => 'PAGE',
                    'workflowId' => $baseWorkflowTask->workflowId,
                    'operatorId' => $baseWorkflowTask->operatorId,
                    'businessId' => $baseWorkflowTask->businessId,
                    'businessData' => array_merge($baseWorkflowTask->businessData, [
                        'startDate' => Carbon::parse($baseWorkflowTask->businessData['startDate'])->toDateString(),
                        'endDate' => Carbon::yesterday()->toDateString(),
                    ]),
                ],
            ],
        ]);
    }

    /**
     * 手动结算
     *
     * @param array $body
     *
     * @return bool
     */
    public function manualSettlement(array $body): bool
    {
        return $this->post('settlement/manual', [
            RequestOptions::JSON => $body,
        ]);
    }

    /**
     * 手动归集
     *
     * @param array $body
     *
     * @return bool
     */
    public function manualCollect(array $body): bool
    {
        return $this->post('collect/manual', [
            RequestOptions::JSON => $body,
        ]);
    }

    public function manualDepositWithdraw(array $body): bool
    {
        return $this->post('depositWithdraw/manual', [
            RequestOptions::JSON => $body,
        ]);
    }

    /**
     * 发起存管统一交易
     *
     * @param string $orderType
     * @param string $orderNo
     * @param string $outAcct
     * @param string $outAcctBank
     * @param string $inAcct
     * @param string $inAcctBank
     * @param int $amount
     * @param string|null $memo
     * @param string|null $label
     *
     * @return bool
     */
    public function depbankOrderApply(
        string $orderType,
        string $orderNo,
        string $outAcct,
        string $outAcctBank,
        string $inAcct,
        string $inAcctBank,
        int $amount,
        string $memo = '',
        string $label = ''
    ): bool {
        return $this->post('depbank/order/apply', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'key' => uuid_create(),
                'type' => 'DepbankOrderApply',
                'data' => [
                    'orderType' => $orderType,
                    'orderNo' => $orderNo,
                    'outAcctNo' => $outAcct,
                    'outAcctBank' => $outAcctBank,
                    'inAcctNo' => $inAcct,
                    'inAcctBank' => $inAcctBank,
                    'amount' => $amount,
                    'memo' => $memo,
                    'label' => $label,
                ]
            ]
        ]);
    }

    /**
     * 存管统一交易查询
     *
     * @param string $orderNo
     *
     * @return bool
     */
    public function depbankOrderQuery(string $orderNo): bool
    {
        return $this->post('depbank/order/query', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'key' => uuid_create(),
                'type' => 'OrderQuery',
                'data' => [
                    'orderNo' => $orderNo,
                ]
            ]
        ]);
    }

    public function depbankIncomeVerify(array $params): bool
    {
        return $this->post('depbank/income/verify', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'key' => uuid_create(),
                'type' => 'IncomeVerify',
                'data' => $params,
            ],
        ]);
    }

    /**
     * 重新结算
     *
     * @param string $orderNo
     *
     * @return bool
     */
    public function reSettlement(string $orderNo): bool
    {
        return $this->post('settlement/reSettlement', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'key' => uuid_create(),
                'type' => 'ManualReSettlement',
                'data' => [
                    'order_no' => $orderNo,
                ]
            ]
        ]);
    }

    /**
     * 重新结算申请
     *
     * @param string $orderNo
     *
     * @return bool
     */
    public function manualApply(string $orderNo): bool
    {
        return $this->post('settlement/manualApply', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'key' => uuid_create(),
                'type' => 'ManualSettlementApply',
                'data' => [
                    'order_no' => $orderNo,
                ]
            ]
        ]);
    }

    /**
     * 发起归集
     *
     * @param string $orderNo
     *
     * @return bool
     */
    public function collectManualApply(string $orderNo): bool
    {
        return $this->post('collect/manualApply', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'key' => uuid_create(),
                'type' => 'ManualCollectionApply',
                'data' => [
                    'order_no' => $orderNo,
                ]
            ]
        ]);
    }

    /**
     * 重新归集
     *
     * @param string $orderNo
     *
     * @return bool
     */
    public function reCollection(string $orderNo): bool
    {
        return $this->post('collect/reCollection', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'key' => uuid_create(),
                'type' => 'ManualReCollection',
                'data' => [
                    'order_no' => $orderNo,
                ]
            ]
        ]);
    }
}
