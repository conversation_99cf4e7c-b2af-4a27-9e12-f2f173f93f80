<?php

use Carbon\Carbon;
use yii\base\Event;
use yii\BaseYii;
use yii\db\Connection;
use yii\di\Container;
use yii\helpers\Html;

class Yii extends BaseYii
{
}

Yii::$container = new Container();

defined('SIDE_LOGO') or define('SIDE_LOGO', Html::img('/images/logo.ico'));


Carbon::macro('toSystemDateTimeString', function ($self = null) {
    /** @var Carbon $this */
    if (isset($self) && !$self instanceof Carbon) {
        $self = Carbon::parse($self);
    } elseif (isset($this)) {
        $self = $this;
    }

    return $self->format('Y-m-d H:i:sP');
});

Carbon::macro('withSystemTimezone', function ($tz = null) {
    /** @var Carbon $this */
    return $this->setTimezone($tz ?? Yii::$app->getTimeZone());
});

/** 注册事件 **************/
if (!YII_ENV_PROD) {
    Event::on(Connection::class, Connection::EVENT_AFTER_OPEN, function (Event $event) {
        /** @var Connection $db */
        $db = $event->sender;

        $mode = [
            'STRICT_TRANS_TABLES',
            'NO_ZERO_IN_DATE',
            'NO_ZERO_DATE',
            'ERROR_FOR_DIVISION_BY_ZERO',
            'NO_ENGINE_SUBSTITUTION',
        ];
        $sql = sprintf('set @@global.sql_mode = \'%s\'', implode(',', $mode));
        try {
            $db->createCommand($sql)->execute();
        } catch (Throwable $e) {
            // nobody
        }
    });
}
