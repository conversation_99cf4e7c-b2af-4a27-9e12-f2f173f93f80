<?php

use Carbon\Carbon;
use Carbon\Traits\Creator;
use cmdb\components\CmdbComponent;
use Codingheping\StatementComponent\component\StatementGateComponent;
use common\behaviors\AssetDataFormatter;
use common\behaviors\ImportExpressionFunctionProvider;
use common\components\CapitalComponent;
use common\components\DcsComponent;
use common\components\DepositComponent;
use common\components\ExpressionComponent;
use common\components\MsgBusComponent;
use common\components\OaComponent;
use common\components\QCloudSdk;
use common\event\OssListener;
use common\models\CpopIncomeItemStatement;
use common\models\Holiday;
use common\models\ReportCapital;
use contract\components\ContractComponent;
use cpm\models\CapitalChannel;
use datasource\components\NotifyComponent;
use dcs\components\ContractsvrComponent;
use dcs\components\GateComponent;
use dcs\components\GatewayComponent;
use dcs\components\PaymentComponent;
use dcs\models\CapitalSettlementRuleConfig;
use dcs\procedures\BankEnterpriseWd;
use dcs\procedures\BiAudit;
use dcs\procedures\DepbankIncomeVerification;
use dcs\procedures\DepbankIncomeVerificationAuto;
use dcs\procedures\DepositRefundCheck;
use dcs\procedures\IncomeVerificationTq;
use dcs\procedures\IncomeVerificationWd;
use dcs\procedures\JointAccountTransfer;
use dcs\procedures\ReserveEntity;
use dcs\procedures\ReserveVirtual;
use dcs\procedures\tasks\WorkflowApplyGenerateDeposit;
use dcs\procedures\TransferRecord;
use gateway\GatewayAdminComponent;
use grant\components\GrantComponent;
use grant\components\RouterComponent;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\MessageFormatter;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Utils;
use import\behaviors\ImportDataFormatterBehavior;
use import\models\BizOssFile;
use import\models\ImportCsv;
use import\Module;
use kvmanager\components\NacosComponent;
use kvmanager\models\KeyValue;
use Monolog\Formatter\JsonFormatter;
use Monolog\Handler\FilterHandler;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Logger;
use payment\components\GrayPaymentComponent;
use Psr\Log\LoggerInterface;
use repay\components\CentralComponent;
use repay\components\RepayComponent;
use waterank\audit\provider\AuditProvider;
use waterank\audit\provider\AvoidProvider;
use Xlerr\ApplicationPayment\Interfaces\OaComponentInterface;
use Xlerr\ApplicationPayment\Procedures\CapitalTransfer;
use Xlerr\ApplicationPayment\Procedures\GlobalCapitalTransfer;
use xlerr\common\common\lib\proxy\CapitalProxy;
use Xlerr\CostManagement\Model\DataReportFinanceIncomeStatement;
use Xlerr\CostManagement\Model\DataReportFinancePaymentFee;
use xlerr\desensitise\Desensitise;
use xlerr\desensitise\FormatterBehavior;
use xlerr\httpca\RequestClient;
use xlerr\jasypt\Jasypt;
use xlerr\mq\components\MessageQueueComponent;
use Xlerr\SettlementFlow\Models\Adjust;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Models\Rule;
use xlerr\task\models\Task;
use yii\base\Event;
use yii\db\BaseActiveRecord;
use yii\db\Connection;
use yii\di\Instance;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\log\FileTarget;
use yii\web\UploadedFile;

use function externalComponentConfig as ecc;
use function waterank\audit\config;

$root = dirname(__DIR__, 2);

$injectCpopIncomeMetricFunction = static function () {
    if (!function_exists('capitalChannelName')) {
        function capitalChannelName(string $code): string
        {
            return CapitalChannel::name($code);
        }
    }

    if (!function_exists('capitalChannelList')) {
        function capitalChannelList(bool $active = false): array
        {
            return CapitalChannel::list($active);
        }
    }

    if (!function_exists('adminChannelList')) {
        function adminChannelList($channel): array
        {
            $channels = array_merge(
                [$channel],
                (array)CapitalSettlementRuleConfig::find()
                    ->where(['admin_channel' => $channel])
                    ->select('asset_loan_channel')
                    ->distinct()
                    ->column()
            );

            return ArrayHelper::filter(capitalChannelList(), $channels);
        }
    }

    if (!function_exists('dbInstance')) {
        function dbInstance(): Connection
        {
            return Yii::$app->getDb();
        }
    }
};

return [
    'name' => 'BIZ',
    'version' => 2.0,
    'timeZone' => 'Asia/Shanghai',
    'vendorPath' => $root . '/vendor',
    'bootstrap' => ['log', 'dataplatform', 'cpop-settlement', 'metric', 'cpop-income', 'cost-management'],
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm' => '@vendor/npm-asset',

        'common' => $root . '/common',
        'backend' => $root . '/backend',
        'console' => $root . '/console',
        'api' => $root . '/api',
        // modules
        'account' => $root . '/modules/account',
        'contract' => $root . '/modules/contract',
        'dashboard' => $root . '/modules/dashboard',
        'dcs' => $root . '/modules/dcs',
        'capital' => $root . '/modules/capital',
        'cpm' => $root . '/modules/cpm',
        'EventManager' => $root . '/modules/EventManager',
        'gateway' => $root . '/modules/gateway',
        'grant' => $root . '/modules/grant',
        'repay' => $root . '/modules/repay',
        'system' => $root . '/modules/system',
        'payment' => $root . '/modules/payment',
        'kuainiu' => $root . '/vendor/kuainiu/yii2-kuainiu',
    ],
    'modules' => [
        'dataplatform' => \datasource\Module::class,
        'cost-management' => [
            'class' => \Xlerr\CostManagement\Module::class,
            'on init' => static function () {
                $config = KeyValue::take('cost_management_config');
                $paymentFee = $config['payment_fee'] ?? [];
                $incomeStatement = $config['income_statement'] ?? [];
                $formatter = Yii::$app->formatter;
                DataReportFinancePaymentFee::setDb('db');
                DataReportFinancePaymentFee::setThreshold($paymentFee['threshold'] ?? 0);
                DataReportFinancePaymentFee::setViewColumns([
                    [
                        'attribute' => 'merchant',
                        'label' => '商户主体'
                    ],
                    [
                        'attribute' => 'subject',
                        'label' => '我方主体'
                    ],
                    [
                        'attribute' => 'type',
                        'label' => '成本类型'
                    ],
                    [
                        'attribute' => 'data_month',
                        'label' => '上报月份',
                    ],
                    [
                        'label' => '生成时间',
                        'value' => function ($model) {
                            if ($model['id']) {
                                return DataReportFinancePaymentFee::findOne($model['id'])->generation_at;
                            }

                            return '-';
                        },
                    ],
                    [
                        'attribute' => 'lastReportDate',
                        'label' => '上次上报时间',
                        'value' => function ($model) {
                            return $model['lastReportDate'] ?? '';
                        },
                    ],
                    [
                        'attribute' => 'initAmount',
                        'label' => '本次上报金额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['initAmount'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }

                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #e6f7ff;'], // 设置背景色
                    ],
                    [
                        'attribute' => 'lastAmount',
                        'label' => '已上报金额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['lastAmount'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }

                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #fffbe6;']
                    ],
                    [
                        'attribute' => 'diff',
                        'label' => '差异额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['diff'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }

                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #ffcccc;']
                    ],
                    [
                        'label' => '差异率',
                        'value' => function ($model) {
                            return $model['differenceRate'] . '%';
                        },
                        'contentOptions' => function ($model) {
                            $threshold = DataReportFinancePaymentFee::$threshold;
                            if ($model['differenceRate'] > (float)$threshold) {
                                return ['style' => 'background-color: #ff4c4c; color: #ffffff;'];
                            }

                            return ['style' => 'background-color: #d4edda;'];
                        }
                    ],
                    [
                        'label' => '备注',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['id']) {
                                $inputId = 'remark-input-' . $model['id'];
                                $saveButtonId = 'save-button-' . $model['id'];
                                $cancelButtonId = 'cancel-button-' . $model['id'];
                                $text = DataReportFinancePaymentFee::findOne($model['id'])->memo;
                                $text = !empty($text) ? $text : '差异率为:' . $model['differenceRate'] . '%';

                                return Html::tag('span', $text, [
                                        'id' => 'remark-display-' . $model['id'],
                                        'class' => 'remark-display',
                                    ]) .
                                    Html::a('<i class="fa fa-pencil"></i>', '#', [
                                        'class' => 'show-input',
                                        'data-id' => $model['id'],
                                        'style' => 'margin-left: 5px;',
                                    ]) .
                                    Html::textInput('remark', $text, [
                                        'id' => $inputId,
                                        'class' => 'remark-input',
                                        'style' => 'display:none; margin-right: 5px;',
                                        'data-id' => $model['id'],
                                    ]) .
                                    Html::button('保存', [
                                        'id' => $saveButtonId,
                                        'class' => 'save-button btn btn-success btn-sm',
                                        'style' => 'display:none; margin-right: 5px;',
                                        'data-id' => $model['id'],
                                    ]) .
                                    Html::button('取消', [
                                        'id' => $cancelButtonId,
                                        'class' => 'cancel-button btn btn-secondary btn-sm',
                                        'style' => 'display:none;',
                                        'data-id' => $model['id'],
                                    ]);
                            }

                            return '';
                        }
                    ],
                ]);
                DataReportFinancePaymentFee::setAuditColumns([
                    [
                        'attribute' => 'merchant',
                        'label' => '商户主体'
                    ],
                    [
                        'attribute' => 'subject',
                        'label' => '我方主体'
                    ],
                    [
                        'attribute' => 'type',
                        'label' => '成本类型'
                    ],
                    [
                        'attribute' => 'data_month',
                        'label' => '上报月份',
                    ],
                    [
                        'attribute' => 'initAmount',
                        'label' => '本次上报金额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['initAmount'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }

                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #e6f7ff;'], // 设置背景色
                    ],
                    [
                        'attribute' => 'lastAmount',
                        'label' => '已上报金额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['lastAmount'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }

                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #fffbe6;']
                    ],
                    [
                        'attribute' => 'diff',
                        'label' => '差异额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['diff'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }

                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #ffcccc;']
                    ],
                    [
                        'label' => '差异率',
                        'value' => function ($model) {
                            return $model['differenceRate'] . '%';
                        },
                        'contentOptions' => function ($model) {
                            $threshold = DataReportFinancePaymentFee::$threshold;
                            if ($model['differenceRate'] > (float)$threshold) {
                                return ['style' => 'background-color: #ff4c4c; color: #ffffff;'];
                            }

                            return ['style' => 'background-color: #d4edda;'];
                        }
                    ],
                    [
                        'label' => '备注',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['id']) {
                                $text = DataReportFinancePaymentFee::findOne($model['id'])->memo;
                                $text = !empty($text) ? $text : '差异率为:' . $model['differenceRate'] . '%';

                                return Html::tag('span', $text, [
                                    'id' => 'remark-display-' . $model['id'],
                                    'class' => 'remark-display',
                                ]);
                            }

                            return '';
                        }
                    ],
                ]);
                DataReportFinancePaymentFee::setDiffSql($paymentFee['sql'], $paymentFee['params'], [
                    'data_month' => 'data_month',
                    'type' => 'type',
                    'subject' => 'subject',
                    'merchant' => 'merchant',
                    'beginDiffRate' => 'differenceRate',
                    'endDiffRate' => 'differenceRate',
                ]);
                DataReportFinanceIncomeStatement::setDb('db');
                DataReportFinanceIncomeStatement::setThreshold($incomeStatement['threshold'] ?? 0);
                DataReportFinanceIncomeStatement::setDiffSql($incomeStatement['sql'], $incomeStatement['params']);
            }
        ],
        'import' => [
            'class' => Module::class,
            'on init' => static function () {
                ImportCsv::$config = fn(): array => KeyValue::takeAsArray('import_template');

                ExpressionComponent::instance()->registerProvider(new ImportExpressionFunctionProvider());

                //事件handler
                $importHandler = static function (Event $event): void {
                    $listener = new OssListener();
                    $listener->process($event);
                };

                //创建导入task
                Event::on(BizOssFile::class, BaseActiveRecord::EVENT_AFTER_INSERT, $importHandler);
                //重新导入
                Event::on(BizOssFile::class, BaseActiveRecord::EVENT_AFTER_UPDATE, $importHandler);
                //上传文件
                Event::on(BizOssFile::class, BaseActiveRecord::EVENT_BEFORE_INSERT, $importHandler);
                //删除
                Event::on(BizOssFile::class, BaseActiveRecord::EVENT_AFTER_DELETE, $importHandler);
                //下载
                Event::on(BizOssFile::class, BizOssFile::EVENT_DOWNLOAD, $importHandler);
            },
        ],
        'metric' => [
            'class' => Xlerr\Metric\Module::class,
            'injectionFunctions' => $injectCpopIncomeMetricFunction
        ],
        'cpop-income' => [
            'class' => Xlerr\CpopIncome\Module::class,
            'injectionFunctions' => $injectCpopIncomeMetricFunction
        ],
        'cpop-settlement' => [
            'class' => \Xlerr\SettlementFlow\Module::class,
            'injectionFunctions' => static function () use ($injectCpopIncomeMetricFunction) {
                $injectCpopIncomeMetricFunction();

                function settleCallback(Order $order)
                {
                    Adjust::updateAll([
                        'status' => Adjust::STATUS_ACTIVE,
                    ], [
                        'settlement_order_id' => $order->id,
                        'status' => Adjust::STATUS_NEW,
                    ]);

                    // 保证金抵充且金额大于0, 生成一条保证金数据设为已结清
                    $generateDeposit = $order->payment_channel === Order::PAYMENT_CHANNEL_DEPOSIT_CHARGE
                        && $order->payableAmount();
                    if ($generateDeposit) {
                        WorkflowApplyGenerateDeposit::make([
                            'id' => $order->id,
                        ], [
                            'task_priority' => Task::PRIORITY_1,
                        ]);
                    }
                }

                /**
                 * @param OrderProcedure[] $procedures
                 *
                 * @return array
                 */
                function lackPaymentVoucher(array $procedures): array
                {
                    return array_values(array_filter($procedures, static function ($procedure) {
                        return $procedure['type'] === BankEnterpriseWd::key()
                            && (string)ArrayHelper::getValue($procedure, 'config.data.keep_payment_voucher') === '1'
                            && empty(ArrayHelper::getValue($procedure, 'config.data.payment_voucher'))
                            && empty(ArrayHelper::getValue($procedure, 'config.data.payment_voucher_memo'));
                    }));
                }

                /**
                 * @param OrderProcedure[] $procedures
                 *
                 * @return array
                 */
                function lackBankFlow(array $procedures): array
                {
                    $procedureKeys = [
                        IncomeVerificationWd::key(),
                        IncomeVerificationTq::key(),
                        DepositRefundCheck::key(),
                    ];

                    return array_values(array_filter($procedures, static function ($procedure) use ($procedureKeys) {
                        return in_array($procedure['type'], $procedureKeys, true)
                            && (string)ArrayHelper::getValue($procedure, 'config.data.remit') === '0';
                    }));
                }

                /**
                 * @param string|DateTimeInterface|Creator $date
                 * @param int $day
                 *
                 * @return string
                 */
                function nthWorkingDay($date, int $day): string
                {
                    return Holiday::nthWorkingDay($date, $day);
                }

                function cpopSettlementIncomeBillDetails(Rule $rule, $startDate, $endDate): ?array
                {
                    $isAllocation = CapitalSettlementRuleConfig::find()->where([
                        'asset_loan_channel' => $rule->channel,
                        'is_allocation' => '1' // 开启成本分摊
                    ])->exists();

                    // 未开启成本分摊
                    if (!$isAllocation) {
                        return null;
                    }

                    $sql = <<<SQL
SELECT case ciis.calc_group
           when 'tha' then '泰国'
           when 'phl' then '菲律宾'
           when 'pak' then '巴基斯坦'
           when 'mex' then '墨西哥'
           when 'idn' then '印度尼西亚'
           end              AS `business_region`,
       sum(ciis.inc_amount) AS `amount`
FROM cpop_income_item_statement ciis
         inner join cpop_income_item cii on cii.id = ciis.inc_item_id
WHERE true
  AND calc_group in ('idn', 'tha', 'phl', 'pak', 'mex')
  AND ciis.inc_amount > 0
  AND cii.ms_rule_id = :ruleId
  AND ciis.`date` >= (SELECT IFNULL(MAX(bill_end_date) + INTERVAL 1 DAY, '2000-01-01')
               FROM `cpop_settlement_order`
               WHERE (`rule_id` = :ruleId)
                 AND (`status` = 5))
  AND ciis.`date` >= :startDate
  AND ciis.`date` < :endDate
group by ciis.calc_group
SQL;

                    $command = CpopIncomeItemStatement::getDb()->createCommand($sql, [
                        'ruleId' => $rule->id,
                        'startDate' => $startDate,
                        'endDate' => Carbon::parse($endDate)->addDay()->toDateString(),
                    ]);

                    return $command->queryAll();
                }

                function capitalGrantAt(string $channel): string
                {
                    return CapitalSettlementRuleConfig::find()
                        ->where(['asset_loan_channel' => $channel])
                        ->select('grant_start_at')
                        ->scalar() ?: Carbon::today()->toDateString();
                }

                function attachmentFilepath(string $filepath): string
                {
                    return "workflowapply/$filepath";
                }

                function uploadAttachment(UploadedFile $file, string $key): string
                {
                    $fs = Utils::tryFopen($file->tempName, 'rb');
                    $client = QCloudSdk::instance();
                    $result = $client->Upload($client->bucket, $key, $fs);
                    Yii::debug((string)$result, __METHOD__);

                    return $client->getObjectUrl($client->bucket, $key, '+30 minute');
                }

                function deleteAttachment(string $key): void
                {
                    $client = QCloudSdk::instance();

                    $client->deleteObject([
                        'Bucket' => $client->bucket,
                        'Key' => $key,
                    ]);
                }

                function urlSign(string $url, $expires = '14 day'): string
                {
                    return QCloudSdk::getCloudUrl($expires, QCloudSdk::instance()->bucket, '', $url);
                }

                function computeFee($startDate, $endDate, $channel, $settlementType, $revenueType): array
                {
                    $fee = 0;
                    $computeConfig = KeyValue::takeAsArray('workflow_config_fee_compute');
                    $computeInfo = $computeConfig[$channel][$revenueType][$settlementType] ?? [];
                    if (empty($computeInfo['source'])) {
                        return [
                            'amount' => $fee,
                            'config' => $computeInfo,
                        ];
                    }
                    if ($computeInfo['source'] === 'report_capital') {
                        $column = $computeInfo['column'] ?? [];
                        $endDate = Carbon::parse($endDate)->addDay()->toDateString();
                        $fee = ReportCapital::find()
                            ->where([
                                'type' => $column,
                                'channel' => $channel,
                            ])
                            ->andWhere([
                                'and',
                                ['>=', 'date', $startDate],
                                ['<', 'date', $endDate],
                            ])
                            ->sum('[[values]]');
                    } elseif ($computeInfo['source'] === 'custom') {
                        $sql = $computeInfo['sql'] ?? null;
                        if ($sql) {
                            $fee = ReportCapital::getDb()
                                ->createCommand($sql, [
                                    'startDate' => $startDate,
                                    'endDate' => $endDate,
                                ])
                                ->queryScalar();
                        }
                    } else {
                        throw new RuntimeException('不支持选项:' . $computeInfo['source']);
                    }

                    return [
                        'amount' => (int)$fee,
                        'config' => $computeInfo,
                    ];
                }
            }
        ],
    ],
    'components' => [
        TransferRecord::key() => TransferRecord::class,
        BiAudit::key() => BiAudit::class,
        BankEnterpriseWd::key() => BankEnterpriseWd::class,
        CapitalTransfer::key() => CapitalTransfer::class,
        GlobalCapitalTransfer::key() => GlobalCapitalTransfer::class,
        IncomeVerificationTq::key() => IncomeVerificationTq::class,
        IncomeVerificationWd::key() => IncomeVerificationWd::class,
        JointAccountTransfer::key() => JointAccountTransfer::class,
        DepositRefundCheck::key() => DepositRefundCheck::class,
        ReserveVirtual::key() => ReserveVirtual::class,
        ReserveEntity::key() => ReserveEntity::class,
        DepbankIncomeVerification::key() => DepbankIncomeVerification::class,
        DepbankIncomeVerificationAuto::key() => DepbankIncomeVerificationAuto::class,
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => FileTarget::class,
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'apiLogger' => static function () {
            if (YII_ENV_PROD) {
                $logfile = '/data/logs1/biz-admin/rest_api.log';
            } else {
                $logfile = Yii::$app->getRuntimePath() . '/logs/rest_api.log';
            }

            $handler = new RotatingFileHandler($logfile, 5, Logger::INFO);
            $handler->setFormatter(new JsonFormatter());

            return new Logger('API', [
                new FilterHandler($handler, [
                    Logger::INFO,
                ]),
            ]);
        },
        Jasypt::class => static function () {
            $binConfig = KeyValue::take('bin_config');

            return new Jasypt($binConfig['java'] ?? null, null, [
                'password' => $_SERVER['JASYPT_PASSWD'] ?? '123456',
            ]);
        },
        RequestClient::HANDLER_STACK_NAME => static function () {
            /** @var LoggerInterface $logger */
            $logger = Instance::ensure('apiLogger', LoggerInterface::class);
            $stack = HandlerStack::create();
            $stack->push(Middleware::log($logger, new MessageFormatter(MessageFormatter::DEBUG)));

            return $stack;
        },
        NacosComponent::class => static fn() => new NacosComponent([
            'baseUri' => ArrayHelper::getValue(KeyValue::take(NacosComponent::CONFIG_KEY), 'baseUri'),
            'username' => $_SERVER['BIZ_NACOS_CONFIG_USERNAME'] ?? '',
            'password' => $_SERVER['BIZ_NACOS_CONFIG_PASSWORD'] ?? '',
        ]),
        ExpressionComponent::class => ExpressionComponent::class,
        GrantComponent::class => static fn() => new GrantComponent(ecc('grant')),
        RouterComponent::class => static fn() => new RouterComponent(ecc('grant-router')),
        DcsComponent::class => static fn() => new DcsComponent(ecc('dcs')),
        OaComponent::class => static fn() => new OaComponent(ecc('oa')),
        OaComponentInterface::class => static fn() => Instance::ensure(OaComponent::class),
        ContractComponent::class => static fn() => new ContractComponent(ecc('contract')),
        GateComponent::class => static fn() => new GateComponent(ecc('filegate')),
        GatewayComponent::class => static fn() => new GatewayComponent(ecc('gateway')),
        GatewayAdminComponent::class => static fn() => new GatewayAdminComponent(ecc('gateway-admin')),
        CapitalComponent::class => static fn() => new CapitalComponent(ecc('caiwu')),
        RepayComponent::class => static fn() => new RepayComponent(ecc('rbiz')),
        PaymentComponent::class => static fn() => new PaymentComponent(ecc('payment')),
        GrayPaymentComponent::class => static fn() => new GrayPaymentComponent(ecc('payment')),
        CentralComponent::class => static fn() => new CentralComponent(ecc('central')),
        StatementGateComponent::class => static fn() => new StatementGateComponent(ecc('statement')),
        CmdbComponent::class => static fn() => new CmdbComponent(ecc('cmdb')),
        DepositComponent::class => static fn() => new DepositComponent(ecc('qjj_deposit')),
        Desensitise::class => static fn() => new Desensitise(ecc('desensitise')),
        MessageQueueComponent::class => static fn() => new MessageQueueComponent(ecc('msgbus')),
        MsgBusComponent::class => static fn() => new MsgBusComponent(ecc('msgbus')),
        NotifyComponent::class => static fn() => new NotifyComponent(ecc('msgsvr')),
        ContractsvrComponent::class => static fn() => new ContractsvrComponent(ecc('contractsvr')),
        'audit' => static fn() => new AuditProvider(config('oauth')),
        'avoid_audit' => static fn() => new AvoidProvider(config('avoid')),
        'formatter' => [
            'as CustomFormatter' => \common\behaviors\FormatterBehavior::class,
            'as DesensitiseFormatter' => FormatterBehavior::class,
            'as ImportDataFormatter' => ImportDataFormatterBehavior::class,
            'as AssetDataFormatter' => AssetDataFormatter::class,
        ],
        CapitalProxy::class => static fn() => new CapitalProxy(ecc('caiwu')),
    ],
];
