<?php

use common\models\User;
use kvmanager\models\KeyValue;
use function xlerr\adminlte\userFullName;

return [
    'country' => 'china',
    'adminEmail' => '<EMAIL>',
    'supportEmail' => '<EMAIL>',
    'kvmanager' => [
        'defaultNamespace' => 'biz',
        'defaultGroup' => 'KV',
    ],
    'audit' => [
        'getConfigMethod' => static fn(string $key) => KeyValue::take($key),
        'extendColumns' => [
            [
                'label' => '申请人',
                'attribute' => 'audit_creator_id',
                'format' => static function (int $userId) {
                    $user = User::findOne($userId);

                    return $user ? userFullName($user) : '-';
                },
            ],
        ],
    ],
];
