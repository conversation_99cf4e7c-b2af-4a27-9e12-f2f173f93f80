<?php

namespace common\business\rbiz;

use Carbon\Carbon;
use common\models\Asset;
use common\models\AssetCard;
use common\models\AssetExtend;
use common\models\AssetIndividual;
use common\models\AssetLoanRecord;
use common\models\AssetRepayRecord;
use common\models\Dtransaction;
use common\models\Fee;
use common\models\Individual;
use Exception;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use yii\base\UserException;

class RBizSyncHelper
{
    /**
     * 资产全量数据
     */
    public const RBIZ_SYNC_DATA_TYPE_ALL = "asset_data_all";

    /**
     * 资产主数据[asset,dtran,fee,ftran]
     */
    public const RBIZ_SYNC_DATA_TYPE_MAIN = "asset_data_main";

    /**
     * 资产卡数据[资产对应的用户收还卡数据]
     */
    public const RBIZ_SYNC_DATA_TYPE_CARD = "asset_data_card";

    /**
     * 根据类型返回中文描述
     *
     * @param $tranType
     *
     * @return string
     * @throws KVException
     */
    public static function getTranDescByType($tranType): string
    {
        $feeTypeConfig = KeyValue::takeAsArray("rbiz_config");

        $text = $feeTypeConfig['tran_type_mapping'][$tranType] ?? [
            Dtransaction::DTRANSACTION_TYPE_GRANT => '放款',
            Dtransaction::DTRANSACTION_TYPE_REPAYINTEREST => '偿还利息',
            Dtransaction::DTRANSACTION_TYPE_REPAYPRINCIPAL => '偿还本金',
            Fee::FEE_TYPE_SERVICE => '服务费',
            Fee::FEE_TYPE_AFTER_LOAN_MANAGE => '贷后管理费',
            Fee::FEE_TYPE_TECH_SERVICE => '技术服务费',
            Fee::FEE_TYPE_MANAGE => '管理费',
            'lateservice' => '逾期服务费',
            'latemanage' => '逾期管理费',
            'lateinterest' => '逾期罚息',
            'qscservice' => '渠道服务费',
            'delay' => '展期服务费',
            'delay_interest' => '展期利息',
            'penalty' => '违约金',
            'writeoff_penalty' => '注销违约金',
            'reserve' => '风险保障金',
            'consult' => '咨询服务费',
        ][$tranType] ?? '未知费用';

        return (string)$text;
    }

    /**
     * 根据交易类型获取还款顺序
     *
     * @param $tranType
     * @param $assetType
     *
     * @return int
     * @throws KVException
     */
    public static function getPriorityByType($tranType, $assetType)
    {
        $config = KeyValue::takeAsArray("rbiz_config");

        return $config['repay_priority'][$assetType][$tranType] ??
            $config['repay_priority']['common_priority'][$tranType] ?? 0;
    }

    /**
     * 组装资产主信息[asset表及asset_tran表]
     *
     * @param Asset $asset
     * @param string $syncType
     *
     * @return array
     * @throws KVException
     * @throws UserException
     */
    public static function genAssetMainData(Asset $asset, string $syncType): array
    {
        if (in_array($asset->asset_status, ['sign', 'sale'])) {
            throw new UserException("资产的状态不允许同步到RBIZ");
        }

        $asset_base_data = [
            'asset_actual_grant_at' => $asset->asset_actual_grant_at,
            'asset_due_at' => $asset->asset_due_at,
            'asset_payoff_at' => $asset->asset_payoff_at,
            'asset_status' => $asset->asset_status,
            'asset_update_at' => $asset->asset_update_at,
            'asset_item_no' => $asset->asset_item_no,
            'asset_loan_channel' => $asset->asset_loan_channel,
            'asset_cmdb_product_number' => $asset->asset_cmdb_product_number,
            'asset_interest_amount' => $asset->asset_interest_amount_f,
            'asset_interest_rate' => $asset->asset_interest_rate,
            'asset_period_type' => $asset->asset_period_type,
            'asset_period_count' => $asset->asset_period_count,
            'asset_product_category' => $asset->asset_period_days,
            'asset_grant_at' => $asset->asset_grant_at,
            'asset_effect_at' => $asset->asset_sign_at,
            'asset_from_system' => $asset->asset_from_system,
            'asset_from_system_name' => $asset->asset_from_system_name,
            'asset_principal_amount' => $asset->asset_principal_amount_f,
            'asset_granted_principal_amount' => $asset->asset_granted_principal_amount_f,
            'asset_owner' => $asset->asset_owner,
            'asset_channel_id' => $asset->asset_channel_id,
            'asset_from_app' => $asset->asset_from_app,
            'asset_repayment_app' => $asset->asset_repayment_app,
        ];
        if ($syncType === self::RBIZ_SYNC_DATA_TYPE_ALL) {
            $asset_base_data = array_merge([
                'asset_type' => $asset->asset_type,
                'asset_sub_type' => $asset->asset_sub_type,
                'asset_alias_name' => $asset->asset_name,
                'asset_create_at' => $asset->asset_create_at,
            ], $asset_base_data);
        }
        $dtrans = $asset->dtransaction;

        $asset_trans = [];

        $asset_fee_amount = 0;//费总量
        $asset_repaid_amount = 0;//已还本息费
        $asset_decrease_principal_amount = 0;//本金减免
        $asset_decrease_interest_amount = 0;//利息减免
        $asset_decrease_fee_amount = 0;//费减免

        foreach ($dtrans as $dtran) {
            $asset_tran_category = '';
            if ($dtran->dtransaction_type === Dtransaction::DTRANSACTION_TYPE_GRANT) {
                $asset_tran_category = Dtransaction::DTRANSACTION_TYPE_GRANT;
            } elseif ($dtran->dtransaction_type === Dtransaction::DTRANSACTION_TYPE_REPAYINTEREST) {
                $asset_tran_category = 'interest';
                $asset_repaid_amount += $dtran->dtransaction_repaid_amount_f;
                $asset_decrease_interest_amount += $dtran->dtransaction_decrease_amount;
            } elseif ($dtran->dtransaction_type === Dtransaction::DTRANSACTION_TYPE_REPAYPRINCIPAL) {
                $asset_tran_category = 'principal';
                $asset_repaid_amount += $dtran->dtransaction_repaid_amount_f;
                $asset_decrease_principal_amount += $dtran->dtransaction_decrease_amount;
            }

            //获取还款顺序
            $asset_trans[] = [
                "asset_tran_category" => $asset_tran_category,
                "asset_tran_type" => $dtran->dtransaction_type,
                "asset_tran_description" => self::getTranDescByType($dtran->dtransaction_type),
                "asset_tran_amount" => $dtran->dtransaction_amount_f - $dtran->dtransaction_decrease_amount,
                "asset_tran_decrease_amount" => $dtran->dtransaction_decrease_amount,
                "asset_tran_repaid_amount" => $dtran->dtransaction_repaid_amount_f,
                "asset_tran_balance_amount" => $dtran->dtransaction_amount_f - $dtran->dtransaction_repaid_amount_f -
                    $dtran->dtransaction_decrease_amount,
                //剩余未还=amt-repaidamt-decreaseamt
                "asset_tran_total_amount" => $dtran->dtransaction_amount_f,
                "asset_tran_status" => $dtran->dtransaction_status,
                "asset_tran_due_at" => $dtran->dtransaction_expect_finish_time,
                "asset_tran_finish_at" => $dtran->dtransaction_finish_at,
                "asset_tran_period" => $dtran->dtransaction_period,
                "asset_tran_late_status" => $dtran->dtransaction_late_status,
                "asset_tran_remark" => $dtran->dtransaction_remark,
                "asset_tran_repay_priority" => self::getPriorityByType($dtran->dtransaction_type, $asset->asset_type),
                "asset_tran_trade_at" => $dtran->dtransaction_finish_at,
                "asset_tran_create_at" => $dtran->dtransaction_create_at,
                "asset_tran_update_at" => $dtran->dtransaction_update_at,
            ];
        }

        $fees = $asset->fee;
        foreach ($fees as $fee) {
            $ftrans = $fee->ftransactions;
            foreach ($ftrans as $ftran) {
                $ftran_expect_time = $ftran->ftransaction_expect_finish_time;
                if (Carbon::now()->toDateString() > $ftran_expect_time) {
                    //逾期
                    $late_status = 'late';
                } else {//正常
                    $late_status = 'normal';
                }
                $asset_repaid_amount += $ftran->ftransaction_repaid_amount_f;
                $asset_fee_amount += $ftran->ftransaction_amount_f;
                $asset_decrease_fee_amount += $ftran->ftransaction_decrease_amount_f;
                $asset_trans[] = [
                    "asset_tran_category" => "fee",
                    "asset_tran_type" => $fee->fee_type,
                    "asset_tran_description" => self::getTranDescByType($fee->fee_type),
                    "asset_tran_amount" => $ftran->ftransaction_amount_f,
                    //应还不包含减免
                    "asset_tran_decrease_amount" => $ftran->ftransaction_decrease_amount_f,
                    "asset_tran_repaid_amount" => $ftran->ftransaction_repaid_amount_f,
                    "asset_tran_balance_amount" => $ftran->ftransaction_amount_f -
                        $ftran->ftransaction_repaid_amount_f,
                    //应还-实还
                    //剩余未还=amt-repaidamt-decreaseamt
                    "asset_tran_total_amount" => $ftran->ftransaction_amount_f +
                        $ftran->ftransaction_decrease_amount_f,
                    //应还+减免
                    "asset_tran_status" => $ftran->ftransaction_status,
                    "asset_tran_due_at" => $ftran->ftransaction_expect_finish_time,
                    "asset_tran_finish_at" => $ftran->ftransaction_finish_at,
                    "asset_tran_period" => $ftran->ftransaction_period,
                    "asset_tran_late_status" => $late_status,
                    "asset_tran_remark" => "",
                    "asset_tran_repay_priority" => self::getPriorityByType($fee->fee_type, $asset->asset_type),
                    "asset_tran_trade_at" => $ftran->ftransaction_finish_at,
                    "asset_tran_create_at" => $ftran->ftransaction_create_at,
                    "asset_tran_update_at" => $ftran->ftransaction_update_at,
                ];
            }
        }

        $asset_base_data = array_merge($asset_base_data, [
            'asset_fee_amount' => $asset_fee_amount,   //总费（不包括减免）
            'asset_repaid_amount' => $asset_repaid_amount,//已还总金额
            'asset_total_amount' => array_sum([
                $asset->asset_principal_amount_f,
                $asset->asset_interest_amount_f,
                $asset_fee_amount,
                $asset_decrease_principal_amount,
                $asset_decrease_interest_amount,
                $asset_decrease_fee_amount,
            ]),//总额：本息费=应还+减免
            'asset_balance_amount' => array_sum([
                    $asset->asset_principal_amount_f,
                    $asset->asset_interest_amount_f,
                    $asset_fee_amount,
                ]) - $asset_repaid_amount,//剩余未还：应还-已还
            'asset_decrease_principal_amount' => $asset_decrease_principal_amount,
            'asset_decrease_interest_amount' => $asset_decrease_interest_amount,
            'asset_decrease_fee_amount' => $asset_decrease_fee_amount,
        ]);

        return [
            'asset' => $asset_base_data,
            'asset_tran' => $asset_trans,
        ];
    }

    /**
     * 组装资产扩展信息表:asset_extend
     *
     * @param Asset $asset
     *
     * @return array
     */
    public static function genAssetExtend(Asset $asset): array
    {
        //先获取loan_recored表
        $asset_loan_recored = AssetLoanRecord::findOne([
            "asset_loan_record_asset_id" => $asset->asset_id,
            "asset_loan_record_status" => AssetLoanRecord::STATUS_LOAN_SUCCESSFUL,
            "asset_loan_record_is_deleted" => 0,
        ]);
        $asset_extends = [];
        if ($asset_loan_recored) {
            $asset_extends[] = [
                "asset_extend_type" => "withholding_amount",
                "asset_extend_val" => $asset_loan_recored->asset_loan_record_withholding_amount,
                "asset_extend_create_at" => $asset_loan_recored->asset_loan_record_create_at,
                "asset_extend_update_at" => $asset_loan_recored->asset_loan_record_update_at,
            ];
            $asset_extends[] = [
                "asset_extend_type" => "identifier",
                "asset_extend_val" => $asset_loan_recored->asset_loan_record_identifier,
                "asset_extend_create_at" => $asset_loan_recored->asset_loan_record_create_at,
                "asset_extend_update_at" => $asset_loan_recored->asset_loan_record_update_at,
            ];
            $asset_extends[] = [
                "asset_extend_type" => "trade_no",
                "asset_extend_val" => $asset_loan_recored->asset_loan_record_trade_no,
                "asset_extend_create_at" => $asset_loan_recored->asset_loan_record_create_at,
                "asset_extend_update_at" => $asset_loan_recored->asset_loan_record_update_at,
            ];
            $asset_extends[] = [
                "asset_extend_type" => "due_bill_no",
                "asset_extend_val" => $asset_loan_recored->asset_loan_record_due_bill_no,
                "asset_extend_create_at" => $asset_loan_recored->asset_loan_record_create_at,
                "asset_extend_update_at" => $asset_loan_recored->asset_loan_record_update_at,
            ];
            $asset_extends[] = [
                "asset_extend_type" => "is_qnn_loan",
                "asset_extend_val" => '0',
                "asset_extend_create_at" => $asset_loan_recored->asset_loan_record_create_at,
                "asset_extend_update_at" => $asset_loan_recored->asset_loan_record_update_at,
            ];
            $asset_extends[] = [
                "asset_extend_type" => "grant_at",
                "asset_extend_val" => $asset_loan_recored->asset_loan_record_grant_at,
                "asset_extend_create_at" => $asset_loan_recored->asset_loan_record_create_at,
                "asset_extend_update_at" => $asset_loan_recored->asset_loan_record_update_at,
            ];
        }
        $asset_extend_record = AssetExtend::findOne(['asset_extend_asset_id' => $asset->asset_id]);
        if ($asset_extend_record) {
            $asset_extends[] = [
                "asset_extend_type" => "charge_type",
                "asset_extend_val" => $asset_extend_record->asset_extend_charge_type,
                "asset_extend_create_at" => $asset_extend_record->asset_extend_created_at,
                "asset_extend_update_at" => $asset_extend_record->asset_extend_updated_at,
            ];

            // 消费金融场景 - 预扣金额
            if ($asset_extend_record->asset_extend_withholding_amount) {
                $asset_extends[] = [
                    "asset_extend_type" => "retain_amount",
                    "asset_extend_val" => $asset_extend_record->asset_extend_withholding_amount,
                    "asset_extend_create_at" => $asset_extend_record->asset_extend_created_at,
                    "asset_extend_update_at" => $asset_extend_record->asset_extend_updated_at,
                ];
            }

            if ($asset_extend_record->asset_extend_ref_item_no) {
                $asset_extends[] = [
                    "asset_extend_type" => "ref_item_no",
                    "asset_extend_val" => $asset_extend_record->asset_extend_ref_item_no,
                    "asset_extend_create_at" => $asset_extend_record->asset_extend_created_at,
                    "asset_extend_update_at" => $asset_extend_record->asset_extend_updated_at,
                ];
            }

            if ($asset_extend_record->asset_extend_ref_order_no) {
                $asset_extends[] = [
                    "asset_extend_type" => "ref_order_no",
                    "asset_extend_val" => $asset_extend_record->asset_extend_ref_order_no,
                    "asset_extend_create_at" => $asset_extend_record->asset_extend_created_at,
                    "asset_extend_update_at" => $asset_extend_record->asset_extend_updated_at,
                ];
            }

            if ($asset_extend_record->asset_extend_ref_order_type) {
                $asset_extends[] = [
                    "asset_extend_type" => "ref_order_type",
                    "asset_extend_val" => $asset_extend_record->asset_extend_ref_order_type,
                    "asset_extend_create_at" => $asset_extend_record->asset_extend_created_at,
                    "asset_extend_update_at" => $asset_extend_record->asset_extend_updated_at,
                ];
            }
            // 评分等级
            if ($asset_extend_record->asset_extend_risk_level) {
                $asset_extends[] = [
                    "asset_extend_type" => "risk_level",
                    "asset_extend_val" => $asset_extend_record->asset_extend_risk_level,
                    "asset_extend_create_at" => $asset_extend_record->asset_extend_created_at,
                    "asset_extend_update_at" => $asset_extend_record->asset_extend_updated_at,
                ];
            }

            if ($asset_extend_record->asset_extend_sub_order_type) {
                $asset_extends[] = [
                    "asset_extend_type" => "sub_order_type",
                    "asset_extend_val" => $asset_extend_record->asset_extend_sub_order_type,
                    "asset_extend_create_at" => $asset_extend_record->asset_extend_created_at,
                    "asset_extend_update_at" => $asset_extend_record->asset_extend_updated_at,
                ];
            }
            // 同步产品名称
            if ($asset_extend_record->asset_extend_product_name) {
                $asset_extends[] = [
                    "asset_extend_type" => "product_name",
                    "asset_extend_val" => $asset_extend_record->asset_extend_product_name,
                    "asset_extend_create_at" => $asset_extend_record->asset_extend_created_at,
                    "asset_extend_update_at" => $asset_extend_record->asset_extend_updated_at,
                ];
            }
        }

        return $asset_extends;
    }

    /**
     * 构建身份信息
     *
     * @param Asset $asset
     *
     * @return array
     */
    public static function genAssetIndividual(Asset $asset): array
    {
        $query = AssetIndividual::find()->where([
            "asset_individual_asset_id" => $asset->asset_id,
        ]);

        $result = [];
        /** @var AssetIndividual $assetIndividual */
        foreach ($query->each() as $assetIndividual) {
            $individual = Individual::findOne([
                "individual_idnum_encrypt" => $assetIndividual->asset_individual_idnum_encrypt,
                "individual_name_encrypt" => $assetIndividual->asset_individual_name_encrypt,
            ]);
            if ($individual === null || isset($result[$individual->individual_id])) {
                continue;
            }
            $result[$individual->individual_id] = array_merge($individual->attributes, [
                'individual_audit_status' => (int)($individual->individual_audit_status === Individual::AUDIT_STATUS_PASS),
                'individual_id_num' => $individual->individual_idnum,
                'individual_id_num_encrypt' => $individual->individual_idnum_encrypt,
                'individualTypes' => $assetIndividual->asset_individual_type,
            ]);
        }

        return array_values($result);
    }

    /**
     * 构建资产卡信息
     *
     * @param Asset $asset
     *
     * @return array
     * @throws Exception
     */
    public static function genAssetCard(Asset $asset): array
    {
        $asset_card_records = AssetCard::findAll([
            "asset_card_asset_id" => $asset->asset_id,
        ]);
        $res_asset_cards = [];
        $card_types = [];
        $card_ids = [];
        foreach ($asset_card_records as $asset_card_record) {
            $card_num_encrypt = trim($asset_card_record->asset_card_account_card_number_encrypt);
            $card_num = trim($asset_card_record->asset_card_account_card_number);
            $card_bank = $asset_card_record->asset_card_account_bank_name;
            $owner_type = $asset_card_record->asset_card_owner_type;

            $card_types[$card_num_encrypt][] = $asset_card_record->asset_card_type;
            $card_data = $card_ids[$card_num_encrypt] ?? null;
            if (empty($card_data)) {
                $card_ids[$card_num_encrypt] = [
                    "card_id" => $card_num,
                    "card_acc_num" => $card_num,
                    "card_id_encrypt" => $card_num_encrypt,
                    "card_acc_num_encrypt" => $card_num_encrypt,
                    "card_type" => $asset_card_record->asset_card_account_type,
                    "card_acc_tel" => $asset_card_record->asset_card_account_tel,
                    "card_acc_tel_encrypt" => $asset_card_record->asset_card_account_tel_encrypt,
                    "card_category" => $asset_card_record->asset_card_account_category,
                    "card_bank_name" => $card_bank,
                    "card_bank_branch_name" => $asset_card_record->asset_card_account_branch_name,
                    "card_bank_code" => $asset_card_record->asset_card_account_bank_code,
                    "card_individual_type" => $owner_type,
                    "card_create_at" => $asset_card_record->asset_card_create_at,
                    "card_update_at" => $asset_card_record->asset_card_update_at,
                    "card_individual_idnum" => $asset_card_record->asset_card_owner_idnum,
                    "card_individual_name" => $asset_card_record->asset_card_owner_name,
                    "card_acc_id_num" => $asset_card_record->asset_card_account_idnum,
                    "card_acc_name" => $asset_card_record->asset_card_account_name,
                    "card_individual_idnum_encrypt" => $asset_card_record->asset_card_owner_idnum_encrypt,
                    "card_individual_name_encrypt" => $asset_card_record->asset_card_owner_name_encrypt,
                    "card_acc_id_num_encrypt" => $asset_card_record->asset_card_account_idnum_encrypt,
                    "card_acc_name_encrypt" => $asset_card_record->asset_card_account_name_encrypt,
                ];
            } else {
                if (empty($card_data['card_bank_branch_name'])) {
                    $card_data['card_bank_branch_name'] = $asset_card_record->asset_card_account_branch_name;
                }
                if (empty($card_data["card_acc_tel"])) {
                    $card_data["card_acc_tel"] = $asset_card_record->asset_card_account_tel;
                }
                if (empty($card_data["card_acc_id_num"])) {
                    $card_data["card_acc_id_num"] = $asset_card_record->asset_card_account_idnum;
                }
                if (empty($card_data["card_acc_name"])) {
                    $card_data["card_acc_name"] = $asset_card_record->asset_card_account_name;
                }
                if (empty($card_data["card_acc_tel_encrypt"])) {
                    $card_data["card_acc_tel_encrypt"] = $asset_card_record->asset_card_account_tel_encrypt;
                }
                if (empty($card_data["card_acc_id_num_encrypt"])) {
                    $card_data["card_acc_id_num_encrypt"] = $asset_card_record->asset_card_account_idnum_encrypt;
                }
                if (empty($card_data["card_acc_name_encrypt"])) {
                    $card_data["card_acc_name_encrypt"] = $asset_card_record->asset_card_account_name_encrypt;
                }
                $card_ids[$card_num_encrypt] = $card_data;
            }
        }
        foreach ($card_ids as $card_num_encrypt => $card_data) {
            $res_asset_cards[] = array_merge([
                "cardTypes" => $card_types[$card_num_encrypt],
            ], $card_data);
        }

        return $res_asset_cards;
    }

    /**
     * 构建还款记录表[asset_repay_record]
     *
     * @param Asset $asset
     *
     * @return array
     */
    public static function genAssetRepayRecord(Asset $asset): array
    {
        $query = AssetRepayRecord::find()->where(["asset_repay_record_asset_id" => $asset->asset_id]);
        $result = [];
        /** @var AssetRepayRecord $row */
        foreach ($query->each() as $row) {
            $result[] = [
                "asset_repay_record_asset_item_no" => $asset->asset_item_no,
                "asset_repay_record_period" => $row->asset_repay_record_period,
                "asset_repay_record_status" => $row->asset_repay_record_status,
                "asset_repay_record_channel" => $row->asset_repay_record_channel,
                "asset_repay_record_principal_amount" => $row->asset_repay_record_principal_amount,
                "asset_repay_record_repaid_principal_amount" => $row->asset_repay_record_repaid_principal_amount,
                "asset_repay_record_interest_amount" => $row->asset_repay_record_interest_amount,
                "asset_repay_record_repaid_interest_amount" => $row->asset_repay_record_repaid_interest_amount,
                "asset_repay_record_fee_amount" => $row->asset_repay_record_fee_amount,
                "asset_repay_record_repaid_fee_amount" => $row->asset_repay_record_repaid_fee_amount,
                "asset_repay_record_repay_date" => $row->asset_repay_record_repay_date,
                "asset_repay_record_fee_commission_amt" => $row->asset_repay_record_fee_commission_amt,
                "asset_repay_record_principal_commission_amt" => $row->asset_repay_record_principal_commission_amt,
                "asset_repay_record_interest_commission_amt" => $row->asset_repay_record_interest_commission_amt,
                "asset_repay_record_payoff_at" => $row->asset_repay_record_payoff_at,
                "asset_repay_record_is_deleted" => $row->asset_repay_record_is_deleted,
                "asset_repay_record_is_advance" => $row->asset_repay_record_is_advance,
                "asset_repay_record_create_at" => $row->asset_repay_record_create_at,
                "asset_repay_record_update_at" => $row->asset_repay_record_update_at,
                "asset_repay_record_is_raised" => $row->asset_repay_record_is_raised,
            ];
        }

        return $result;
    }
}
