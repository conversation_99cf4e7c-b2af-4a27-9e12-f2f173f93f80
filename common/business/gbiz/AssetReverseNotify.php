<?php

namespace common\business\gbiz;

use Carbon\Carbon;
use common\models\Asset;
use common\models\CapitalTransaction;
use dcs\models\AssetReverse;
use dcs\models\AssetReverseTran;
use xlerr\task\TaskHandler;
use yii\base\UserException;
use yii\helpers\Json;

class AssetReverseNotify extends TaskHandler
{
    public string $channel = '';
    public string $apply_code = '';

    /**
     * @return string
     */
    public function formName(): string
    {
        return 'data';
    }

    public function rules(): array
    {
        return [
            [['channel', 'apply_code'], 'trim'],
            [['channel', 'apply_code'], 'required'],
        ];
    }

    /***
     * 同步任务 接收异常结清任务消息
     *
     * @return array
     * @throws UserException
     */
    public function process(): array
    {
        $assetReverse = AssetReverse::findOne([
            'asset_item_no' => $this->apply_code,
            'loan_channel' => $this->channel,
        ]);

        // 放款回调时，没有查到手动异常结清数据就记录成自动异常结清
        if (!$assetReverse) {
            $asset = Asset::findOne([
                'asset_item_no' => $this->apply_code,
                'asset_loan_channel' => $this->channel
            ]);
            if (!$asset) {
                throw new UserException('资产不存在');
            }
            $assetReverse = new AssetReverse();
            $assetReverse->asset_item_no = $this->apply_code;
            $assetReverse->period_count = $asset->asset_period_count;
            $assetReverse->loan_channel = $asset->asset_loan_channel;
            $assetReverse->status = AssetReverse::STATUS_SUCCESS;
            $assetReverse->create_at = Carbon::now()->toDateString();
            $assetReverse->memo = '系统自动冲正';
        } else {
            if (in_array($assetReverse->status, [AssetReverse::STATUS_NEED_CONFIRM, AssetReverse::STATUS_SUCCESS], true)) {
                return [
                    'code' => 0,
                    'message' => '已经处理完成',
                ];
            }

            if ($assetReverse->status !== AssetReverse::STATUS_GBIZ_RECEIVE) {
                throw new UserException('任务当前目标状态不合法');
            }

            // 设置状态为 处理等待确认;
            $assetReverse->status = AssetReverse::STATUS_NEED_CONFIRM;
        }

        $assetReverse->callback_at = Carbon::now()->toDateTimeString();

        if (!$assetReverse->save()) {
            throw new UserException('保存 asset_reverse 时报错: ' . Json::encode($assetReverse->getErrors()));
        }

        $this->handleVoid($assetReverse);

        return [
            'code' => 0,
            'message' => 'ok',
        ];
    }

    /**
     * @param AssetReverse $assetReverse
     *
     * @throws UserException
     */
    private function handleVoid(AssetReverse $assetReverse): void
    {
        $periods = CapitalTransaction::find()
            ->where([
                'capital_transaction_asset_item_no' => $assetReverse->asset_item_no,
                'capital_transaction_type' => CapitalTransaction::TYPE_PRINCIPAL,
            ])
            ->select('capital_transaction_period')
            ->column();

        foreach ($periods as $period) {
            $principalTran = new AssetReverseTran();
            $principalTran->amount = 0;
            $principalTran->type = AssetReverseTran::TYPE_PRINCIPAL;
            $principalTran->asset_reverse_id = $assetReverse->id;
            $principalTran->period = $period;
            $principalTran->create_at = Carbon::now()->toDateTimeString();

            if (!$principalTran->save()) {
                throw new UserException('保存 本金条目失败' . json_encode($principalTran->getErrors()));
            }
        }
    }
}
