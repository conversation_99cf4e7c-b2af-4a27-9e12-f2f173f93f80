<?php

namespace common\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * DepositBillSearch represents the model behind the search form about `common\models\DepositBill`.
 */
class DepositBillSearch extends DepositBill
{
    public $startDate;
    public $endDate;

    public function formName(): string
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => Carbon::now()->subDays(7)->toDateString()],
            [['endDate'], 'default', 'value' => Carbon::yesterday()->toDateString()],
            [['channel', 'type', 'status'], 'safe'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = DepositBill::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'attributes'   => ['date'],
                'defaultOrder' => ['date' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'status'  => $this->status,
            'channel' => $this->channel,
            'type'    => $this->type,
        ])->andFilterWhere([
            'and',
            ['>=', 'date', $this->startDate],
            ['<', 'date', Carbon::parse($this->endDate)->addDay()->toDateString()],
        ]);

        return $dataProvider;
    }
}
