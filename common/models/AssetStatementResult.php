<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "asset_statement_result".
 *
 * @property int    $asset_statement_result_id
 * @property int    $asset_statement_result_source_id
 * @property string $asset_statement_result_trade_no
 * @property string $asset_statement_result_item_no
 * @property string $asset_statement_result_channel
 * @property string $asset_statement_result_type
 * @property int    $asset_statement_result_status
 * @property int    $asset_statement_result_results
 * @property string $asset_statement_result_create_at
 * @property string $asset_statement_result_statement_date
 * @property string $asset_statement_result_desc
 * @property string $asset_statement_result_memo
 * @property string $asset_statement_result_diff_amount
 */
class AssetStatementResult extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'asset_statement_result';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['asset_statement_result_channel'], 'required'],
            [['asset_statement_result_status', 'asset_statement_result_results'], 'integer'],
            [['asset_statement_result_create_at', 'asset_statement_result_statement_date'], 'safe'],
            [['asset_statement_result_trade_no'], 'string', 'max' => 255],
            [['asset_statement_result_item_no'], 'string', 'max' => 64],
            [['asset_statement_result_channel', 'asset_statement_result_type'], 'string', 'max' => 50],
            [['asset_statement_result_desc', 'asset_statement_result_memo'], 'string', 'max' => 500],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'asset_statement_result_id'             => 'Asset Statement Result ID',
            'asset_statement_result_source_id'      => '对账单ID',
            'asset_statement_result_trade_no'       => '交易流水号',
            'asset_statement_result_item_no'        => '交易流水号',
            'asset_statement_result_channel'        => '资方类型',
            'asset_statement_result_type'           => '对账数据类型',
            'asset_statement_result_status'         => '处理结果:0失败 1成功',
            'asset_statement_result_results'        => '对账结果:0有差错 1对账无差错',
            'asset_statement_result_create_at'      => 'Asset Statement Result Create At',
            'asset_statement_result_statement_date' => '对账操作日期',
            'asset_statement_result_desc'           => '差异描述',
            'asset_statement_result_memo'           => '对账结果备注',
            'asset_statement_result_diff_amount'    => '对账结果差异金额',
        ];
    }
}
