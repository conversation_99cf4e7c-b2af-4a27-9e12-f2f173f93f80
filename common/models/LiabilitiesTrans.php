<?php

namespace common\models;

use yii\behaviors\BlameableBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "spv_repayment_plan".
 *
 * @property int $id ID
 * @property int $liabilities_id 保理资方ID
 * @property int $period 期次
 * @property string $type 费用类型:principal,本金;interest:利息
 * @property int $amount 金额
 * @property int $grant_at 起息日
 * @property int $due_at 还款日期
 * @property int $created_by 创建人
 * @property int $updated_by 修改人
 * @property string $created_at 创建时间
 * @property string $updated_at 修改时间
 */
class LiabilitiesTrans extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'liabilities_trans';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['liabilities_id', 'period', 'amount', 'created_by', 'updated_by'], 'integer'],
            [['created_at', 'updated_at', 'grant_at', 'due_at'], 'safe'],
            [['type'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'liabilities_id' => '保理资方ID',
            'period' => '期次',
            'type' => '费用类型:principal,本金;interest:利息',
            'amount' => '金额',
            'created_by' => '创建人',
            'updated_by' => '修改人',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
            'grant_at' => '起息日',
            'due_at' => '还款日期',
        ];
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => 'updated_by',
            ],
        ];
    }
}
