<?php

namespace common\models;

use Carbon\Carbon;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "depbank_biz_label".
 *
 * @property int $id 自增主键
 * @property string $fund_flow 资金流向：in 流入、out 流出
 * @property string $category label分类: other其他、clean清分、settlement制单
 * @property string $code label编码
 * @property string $name 名称
 * @property string|null $memo 备注
 * @property string|null $scope 作用域：系统（不可修改）：system、其他：other
 * @property string $create_user 创建用户
 * @property string $create_at 创建时间
 * @property string $update_at 更新时间
 */
class BizLabel extends ActiveRecord
{
    public const FUND_FLOW_IN = 'in';
    public const FUND_FLOW_OUT = 'out';

    public const FUND_FLOWS = [
        self::FUND_FLOW_IN => '入金',
        self::FUND_FLOW_OUT => '出金'
    ];


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'depbank_biz_label';
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'create_user',
                'updatedByAttribute' => null,
                'value' => fn($event) => Yii::$app->user->id,
            ],
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'create_at',
                'updatedAtAttribute' => 'update_at',
                'value' => Carbon::now()->toDateTimeString()
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['fund_flow', 'category', 'code', 'name'], 'required'],
            [['create_at', 'update_at'], 'safe'],
            [['fund_flow', 'category', 'scope'], 'string', 'max' => 32],
            [['code', 'name', 'memo'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '自增主键',
            'fund_flow' => '资金流向',
            'category' => '分类',
            'code' => '编码',
            'name' => '名称',
            'memo' => '备注',
            'scope' => '作用域',
            'create_user' => '创建用户',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
        ];
    }

    public static function inTypes(): array
    {
        return self::find()
            ->where(['fund_flow' => self::FUND_FLOW_IN])
            ->select('name')
            ->indexBy('code')
            ->column();
    }

    public static function outTypes(): array
    {
        return self::find()
            ->where(['fund_flow' => self::FUND_FLOW_OUT])
            ->select('name')
            ->indexBy('code')
            ->column();
    }

    /**
     * @param string|array $flow
     * @param string $category
     * @param string $code
     * @return array
     */
    public static function getBizTypeList($flow = self::FUND_FLOW_IN, string $category = '', string $code = ''): array
    {
        return self::find()->select('name')
            ->where(['fund_flow' => $flow])
            ->andFilterWhere(['category' => $category])
            ->andFilterWhere(['code' => $code])
            ->indexBy('code')
            ->column();
    }


    public static function getCategoryList(): array
    {
        return self::find()
            ->select(['category'])
            ->groupBy('category')
            ->indexBy('category')
            ->column();
    }

    public function getCreateUser(): \yii\db\ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'create_user']);
    }
}
