<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "arbitration".
 *
 * @property int    $arbitration_id
 * @property string $arbitration_asset_item_no
 * @property int    $arbitration_amount
 * @property int    $arbitration_repaid_amount
 * @property string $arbitration_status
 * @property string $arbitration_from_system
 * @property string $arbitration_user_id_num
 * @property string $arbitration_user_name
 * @property string $arbitration_user_id_num_encrypt
 * @property string $arbitration_user_name_encrypt
 * @property string $arbitration_create_at
 * @property string $arbitration_update_at
 */
class Arbitration extends ActiveRecord
{
    public const STATUS_FINISH = "finish";

    public const STATUS_NO_FINISH = "nofinish";

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'arbitration';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'arbitration_asset_item_no',
                    'arbitration_from_system',
                    'arbitration_user_id_num',
                    'arbitration_user_name',
                ],
                'required',
            ],
            [['arbitration_amount', 'arbitration_repaid_amount'], 'integer'],
            [['arbitration_status'], 'string'],
            [['arbitration_create_at', 'arbitration_update_at'], 'safe'],
            [['arbitration_asset_item_no'], 'string', 'max' => 64],
            [['arbitration_from_system'], 'string', 'max' => 255],
            [['arbitration_user_id_num_encrypt', 'arbitration_user_name_encrypt'], 'string', 'max' => 32],
            [['arbitration_user_id_num', 'arbitration_user_name'], 'string', 'max' => 20],
            [['arbitration_asset_item_no'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'arbitration_id'                  => 'Arbitration ID',
            'arbitration_asset_item_no'       => 'Arbitration Asset Item No',
            'arbitration_amount'              => 'Arbitration Amount',
            'arbitration_repaid_amount'       => 'Arbitration Repaid Amount',
            'arbitration_status'              => 'Arbitration Status',
            'arbitration_from_system'         => 'Arbitration From System',
            'arbitration_user_id_num'         => 'Arbitration User Id Num',
            'arbitration_user_name'           => 'Arbitration User Name',
            'arbitration_user_id_num_encrypt' => 'Arbitration User Id Num Encrypt',
            'arbitration_user_name_encrypt'   => 'Arbitration User Name Encrypt',
            'arbitration_create_at'           => 'Arbitration Create At',
            'arbitration_update_at'           => 'Arbitration Update At',
        ];
    }
}
