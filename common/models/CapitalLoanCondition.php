<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "capital_loan_condition".
 *
 * @property int    $capital_loan_condition_id
 * @property string $capital_loan_condition_day
 * @property string $capital_loan_condition_channel
 * @property int    $capital_loan_condition_amount
 * @property string $capital_loan_condition_from_system
 * @property string $capital_loan_condition_sub_type
 * @property string $capital_loan_condition_category_unit
 * @property int    $capital_loan_condition_period_count
 * @property string $capital_loan_condition_period_type
 * @property int    $capital_loan_condition_period_days
 * @property string $capital_loan_condition_description
 * @property string $capital_loan_condition_update_memo
 * @property string $capital_loan_condition_create_at
 * @property string $capital_loan_condition_update_at
 */
class CapitalLoanCondition extends ActiveRecord
{
    protected $split = '-';

    /**
     * 用户存储 字段和名称的关系
     *
     * @var
     */
    public $description_map;

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'capital_loan_condition';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                ['capital_loan_condition_day', 'capital_loan_condition_create_at', 'capital_loan_condition_update_at'],
                'safe',
            ],
            [['capital_loan_condition_channel', 'capital_loan_condition_update_memo'], 'required'],
            [
                [
                    'capital_loan_condition_amount',
                    'capital_loan_condition_period_count',
                    'capital_loan_condition_period_days',
                ],
                'integer',
            ],
            [['capital_loan_condition_update_memo'], 'string'],
            [['capital_loan_condition_channel'], 'string', 'max' => 32],
            [
                [
                    'capital_loan_condition_from_system',
                    'capital_loan_condition_sub_type',
                    'capital_loan_condition_period_type',
                ],
                'string',
                'max' => 50,
            ],
            [['capital_loan_condition_description'], 'string', 'max' => 16],
            [
                [
                    'capital_loan_condition_day',
                    'capital_loan_condition_channel',
                    'capital_loan_condition_sub_type',
                    'capital_loan_condition_from_system',
                    'capital_loan_condition_period_type',
                    'capital_loan_condition_period_count',
                    'capital_loan_condition_period_days',
                ],
                'unique',
                'targetAttribute' => [
                    'capital_loan_condition_day',
                    'capital_loan_condition_channel',
                    'capital_loan_condition_sub_type',
                    'capital_loan_condition_from_system',
                    'capital_loan_condition_period_type',
                    'capital_loan_condition_period_count',
                    'capital_loan_condition_period_days',
                ],
                'message'         => '',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'capital_loan_condition_id'           => 'Capital Loan Condition ID',
            'capital_loan_condition_day'          => 'Capital Loan Condition Day',
            'capital_loan_condition_channel'      => 'Capital Loan Condition Channel',
            'capital_loan_condition_amount'       => 'Capital Loan Condition Amount',
            'capital_loan_condition_from_system'  => 'Capital Loan Condition From System',
            'capital_loan_condition_sub_type'     => 'Capital Loan Condition Sub Type',
            'capital_loan_condition_period_count' => 'Capital Loan Condition Period Count',
            'capital_loan_condition_period_type'  => 'Capital Loan Condition Period Type',
            'capital_loan_condition_period_days'  => 'Capital Loan Condition Period Days',
            'capital_loan_condition_description'  => 'Capital Loan Condition Description',
            'capital_loan_condition_update_memo'  => 'Capital Loan Condition Update Memo',
            'capital_loan_condition_create_at'    => 'Capital Loan Condition Create At',
            'capital_loan_condition_update_at'    => 'Capital Loan Condition Update At',
        ];
    }
}
