<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "withhold_detail_his".
 *
 * @property int $withhold_detail_his_id 主键
 * @property int|null $withhold_detail_id
 * @property string $withhold_detail_serial_no 代扣流水号
 * @property string $withhold_detail_asset_item_no 资产编号
 * @property int $withhold_detail_period 期次
 * @property string $withhold_detail_asset_tran_type 资产明细类型，根据asset_tran获取
 * @property string $withhold_detail_asset_tran_no 资产明细编号
 * @property int $withhold_detail_asset_tran_amount 资产应扣金额（单位:分）
 * @property int $withhold_detail_asset_tran_balance_amount 资产剩余金额（单位:分）
 * @property int $withhold_detail_withhold_amount 实际代扣金额（单位:分）
 * @property string $withhold_detail_type 代扣明细分类，根据asset_tran而来
 * @property string $withhold_detail_partial 是否部分还款
 * @property string $withhold_detail_status 状态
 * @property int $withhold_detail_priority 优先级
 * @property string $withhold_detail_create_at 创建时间
 * @property string $withhold_detail_update_at 更新时间
 * @property string $withhold_detail_repay_type 还款类型
 * @property string $withhold_detail_his_create_at 创建时间
 */
class WithholdDetailHis extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'withhold_detail_his';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbRbizHis');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['withhold_detail_id', 'withhold_detail_period', 'withhold_detail_asset_tran_amount', 'withhold_detail_asset_tran_balance_amount', 'withhold_detail_withhold_amount', 'withhold_detail_priority'], 'integer'],
            [['withhold_detail_serial_no', 'withhold_detail_asset_item_no', 'withhold_detail_period', 'withhold_detail_asset_tran_type', 'withhold_detail_asset_tran_no', 'withhold_detail_asset_tran_amount', 'withhold_detail_asset_tran_balance_amount', 'withhold_detail_withhold_amount', 'withhold_detail_type', 'withhold_detail_status', 'withhold_detail_priority', 'withhold_detail_repay_type'], 'required'],
            [['withhold_detail_partial', 'withhold_detail_repay_type'], 'string'],
            [['withhold_detail_create_at', 'withhold_detail_update_at', 'withhold_detail_his_create_at'], 'safe'],
            [['withhold_detail_serial_no', 'withhold_detail_asset_item_no', 'withhold_detail_asset_tran_no'], 'string', 'max' => 50],
            [['withhold_detail_asset_tran_type', 'withhold_detail_type'], 'string', 'max' => 32],
            [['withhold_detail_status'], 'string', 'max' => 16],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withhold_detail_his_id' => '主键',
            'withhold_detail_id' => 'Withhold Detail ID',
            'withhold_detail_serial_no' => '代扣流水号',
            'withhold_detail_asset_item_no' => '资产编号',
            'withhold_detail_period' => '期次',
            'withhold_detail_asset_tran_type' => '资产明细类型，根据asset_tran获取',
            'withhold_detail_asset_tran_no' => '资产明细编号',
            'withhold_detail_asset_tran_amount' => '资产应扣金额（单位:分）',
            'withhold_detail_asset_tran_balance_amount' => '资产剩余金额（单位:分）',
            'withhold_detail_withhold_amount' => '实际代扣金额（单位:分）',
            'withhold_detail_type' => '代扣明细分类，根据asset_tran而来',
            'withhold_detail_partial' => '是否部分还款',
            'withhold_detail_status' => '状态',
            'withhold_detail_priority' => '优先级',
            'withhold_detail_create_at' => '创建时间',
            'withhold_detail_update_at' => '更新时间',
            'withhold_detail_repay_type' => '还款类型',
            'withhold_detail_his_create_at' => '创建时间',
        ];
    }
}
