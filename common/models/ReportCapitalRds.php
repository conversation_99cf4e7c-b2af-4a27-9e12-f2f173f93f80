<?php

namespace common\models;

use Carbon\Carbon;
use dashboard\traits\ReportModelTrait;
use datasource\DataSourceContext;
use Throwable;
use Yii;
use yii\base\UserException;
use yii\db\ActiveQuery;
use yii\db\Exception;

/**
 * Report Capital Relation Data Source
 */
class ReportCapitalRds extends ReportCapitalSync
{
    use ReportModelTrait;

    /**
     * @param array $data
     * @param array $config
     * @param DataSourceContext $dataSourceContext
     *
     * @return int 写入表中的数据行数
     * @throws Exception
     * @throws Throwable
     * @throws UserException
     */
    public static function storage(array $data, array $config, DataSourceContext $dataSourceContext): int
    {
        $query = self::queryByContext($dataSourceContext);

        $finishAt = Carbon::parse($dataSourceContext->finishAt)->setTimezone(Yii::$app->getTimeZone());
        // 数据源完成时间大于表中最后一次落库时间才能落库，防止顺序混乱导致数据被覆盖
        $createdAtHis = (string)(clone $query)->select('max(created_at)')->scalar();
        if (!empty($createdAtHis) && Carbon::parse($createdAtHis) >= $finishAt) {
            return 0;
        }

        // 将旧数据迁移到历史表
        self::saveReportCapitalHis($query);

        if (empty($data) || empty($data = self::transform($data, $config))) {
            return 0;
        }

        $columns = array_keys($data[0]);

        $command = static::getDb()->createCommand();

        $affectedCountList = array_map(static function (array $data) use ($command, $columns): int {
            return $command->batchInsert(self::tableName(), $columns, $data)->execute();
        }, array_chunk($data, 10000));

        return array_sum($affectedCountList);
    }

    /**
     * @param DataSourceContext $dataSourceContext
     *
     * @return ActiveQuery
     */
    protected static function queryByContext(DataSourceContext $dataSourceContext): ActiveQuery
    {
        return self::find()->where([
            'channel' => $dataSourceContext->executeParams['channel'],
            'country' => $dataSourceContext->country,
            'data_source_id' => $dataSourceContext->dataSource->id,
        ])->andWhere([
            'and',
            ['>=', 'date', $dataSourceContext->executeParams['startDate']],
            ['<', 'date', $dataSourceContext->executeParams['endDate']],
        ]);
    }
}
