<?php

namespace common\models;

use RuntimeException;
use Throwable;
use yii\db\ActiveRecord;
use yii\helpers\Json;

/**
 * This is the model class for table "{{%portal_sendmsg}}".
 *
 * @property int $sendmsg_id
 * @property string $sendmsg_order_no 消息业务编号
 * @property string $sendmsg_type 消息具体类型
 * @property string|null $sendmsg_content 消息内容
 * @property string $sendmsg_memo 备注
 * @property string $sendmsg_tosystem msg发送的系统
 * @property string $sendmsg_status 消息状态
 * @property string $sendmsg_next_run_at 下次发送时间
 * @property string $sendmsg_create_at 消息创建时间
 * @property string $sendmsg_update_at 更新时间
 * @property int $sendmsg_version 消息版本
 * @property int $sendmsg_priority 消息优先级
 * @property int $sendmsg_retrytimes 消息重试次数
 * @property string|null $sendmsg_response_data
 */
class SendMsg extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'portal_sendmsg';
    }

    public function formName(): string
    {
        return '';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['sendmsg_type', 'sendmsg_status'], 'required'],
            [['sendmsg_content', 'sendmsg_status', 'sendmsg_response_data'], 'string'],
            [['sendmsg_next_run_at'], 'safe'],
            [['sendmsg_version', 'sendmsg_priority', 'sendmsg_retrytimes'], 'integer'],
            [['sendmsg_order_no', 'sendmsg_tosystem'], 'string', 'max' => 64],
            [['sendmsg_type'], 'string', 'max' => 45],
            [['sendmsg_memo'], 'string', 'max' => 2048],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'sendmsg_id' => 'Sendmsg ID',
            'sendmsg_order_no' => '消息业务编号',
            'sendmsg_type' => '消息具体类型',
            'sendmsg_content' => '消息内容',
            'sendmsg_memo' => '备注',
            'sendmsg_tosystem' => 'msg发送的系统',
            'sendmsg_status' => '消息状态',
            'sendmsg_next_run_at' => '下次发送时间',
            'sendmsg_create_at' => '消息创建时间',
            'sendmsg_update_at' => '更新时间',
            'sendmsg_version' => '消息版本',
            'sendmsg_priority' => '消息优先级',
            'sendmsg_retrytimes' => '消息重试次数',
            'sendmsg_response_data' => 'Sendmsg Response Data',
        ];
    }

    /**
     * 发送消息, sendmsg服务自动扫描sendmsg表中状态为open的任务
     *
     * @param string $type
     * @param string $orderNo
     * @param array $data
     * @param string $fromSystem
     *
     * @return void
     * @throws Throwable
     */
    public static function send(string $type, string $orderNo, array $data, string $fromSystem = 'PORTAL'): void
    {
        $msg = new self();
        $msg->sendmsg_type = $type;
        $msg->sendmsg_order_no = $orderNo;
        $msg->sendmsg_content = Json::encode([
            'headers' => [
                'type' => $type,
            ],
            'body' => [
                'type' => $type,
                'key' => $orderNo,
                'from_system' => $fromSystem,
                'data' => $data,
            ],
        ]);
        $msg->sendmsg_tosystem = 'MQ';
        $msg->sendmsg_status = 'open';
        if (!$msg->insert()) {
            throw new RuntimeException(Json::encode($msg->getErrors()));
        }
    }
}
