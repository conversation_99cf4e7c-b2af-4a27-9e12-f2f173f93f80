<?php

namespace common\models;

use Carbon\Carbon;
use cpm\models\CapitalChannel;
use dcs\models\CapitalSettlementRuleConfig;
use RuntimeException;
use Xlerr\SettlementFlow\Models\Rule;
use yii\behaviors\BlameableBehavior;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "{{%liabilities}}".
 *
 * @property int $id            ID
 * @property string $loan_channel  资金方
 * @property int $amount        金额
 * @property string $status        状态 new: 待提交, auditing: 审核中, success: 已完成, failure: 失败
 * @property int $period_days   借款天数
 * @property string $grant_at      起息日
 * @property string $due_at        到期日
 * @property string $origin_due_at 原始到期日
 * @property float $rate          年利率
 * @property string $created_at    创建时间
 * @property string $updated_at    修改时间
 * @property int $created_by    创建人
 * @property string $receipt_date 到账日期
 * @property string $currency  币种(CNY人民币,USD美元)
 * @property string $exchange_rate 汇率
 * @property string $periods 期次
 * @property-read CapitalSettlementRuleConfig $settlementRuleConfig
 * @property-read User $operator
 * @property-read LiabilitiesCostAllocate[] $costAllocates
 * @property-read LiabilitiesTrans[] $trans
 */
class Liabilities extends ActiveRecord
{
    public const STATUS_NEW = 'new';
    public const STATUS_AUDITING = 'auditing';
    public const STATUS_SUCCESS = 'success';
    public const STATUS_FAILURE = 'failure';
    public const STATUS_LIST = [
        self::STATUS_NEW => '待提交',
        self::STATUS_AUDITING => '审核中',
        self::STATUS_SUCCESS => '放款成功',
        self::STATUS_FAILURE => '失败',
    ];

    public const CURRENCY_LIST = [
        'USD' => '美元',
        'CNY' => '人民币',
    ];

    public function behaviors(): array
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => null,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%liabilities}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['status'], 'default', 'value' => self::STATUS_NEW],
            [
                [
                    'loan_channel',
                    'amount',
                    'status',
                    'period_days',
                    'grant_at',
                    'yearRate',
                    'receipt_date',
                    'currency',
                    'exchange_rate'
                ],
                'required'
            ],
            [['period_days', 'created_by', 'periods'], 'integer'],
            [['amount'], 'integer', 'min' => 1, 'tooSmall' => '金额最低不能低于0.01元'],
            [['grant_at', 'due_at', 'origin_due_at', 'receipt_date', 'periods'], 'safe'],
            [
                ['due_at'],
                'filter',
                'filter' => function () {
                    return Carbon::parse($this->grant_at)->addDays($this->period_days)->toDateString();
                },
            ],
            [['rate', 'exchange_rate'], 'number'],
            [
                ['yearRate'],
                'number',
                'min' => .01,
                'max' => 100,
                'tooSmall' => '年利率不能低于0.01%',
                'tooBig' => '年利率最多不能超过100%',
            ],
            [['loan_channel', 'status'], 'string', 'max' => 32],
        ];
    }

    public function getYearRate(): float
    {
        return $this->rate * 100;
    }

    public function setYearRate(float $rate): void
    {
        $this->rate = $rate / 100;
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'loan_channel' => '资金方',
            'amount' => '金额',
            'status' => '状态',
            'period_days' => '借款天数',
            'grant_at' => '起息日',
            'due_at' => '到期日',
            'rate' => '年利率',
            'yearRate' => '年利率',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
            'created_by' => '创建人',
            'receipt_date' => '到账日期',
            'currency' => '币种',
            'exchange_rate' => '汇率',
            'periods' => '期次',
        ];
    }

    public function getSettlementRuleConfig(): ActiveQuery
    {
        return $this->hasOne(CapitalSettlementRuleConfig::class, ['asset_loan_channel' => 'loan_channel']);
    }

    public function getOperator(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    public function getTrans(): ActiveQuery
    {
        return $this->hasMany(LiabilitiesTrans::class, ['liabilities_id' => 'id']);
    }

    public function getCostAllocates(): ActiveQuery
    {
        return $this->hasMany(LiabilitiesCostAllocate::class, ['liabilities_id' => 'id']);
    }

    /**
     * @param string|string[] $version 空表示所有版本
     *
     * @return LiabilitiesCostAllocatePlan[]
     */
    public function getCostAllocatePlans(string $version = ''): array
    {
        $plans = $this->getCostAllocates()
            ->select([
                'liabilitiesId' => 'liabilities_id',
                'version',
                'beginAt' => 'begin_at',
                'endAt' => 'end_at',
                'plan' => "JSON_OBJECTAGG(country, rate)",
            ])
            ->filterWhere(['version' => $version])
            ->groupBy(['version'])
            ->orderBy(['version' => SORT_DESC])
            ->asArray()
            ->all();

        return array_map(static function ($plan) {
            return new LiabilitiesCostAllocatePlan($plan);
        }, $plans);
    }

    public function getCostAllocatePlan(string $version): LiabilitiesCostAllocatePlan
    {
        return current($this->getCostAllocatePlans($version));
    }

    public function createCostAllocatePlan(): LiabilitiesCostAllocatePlan
    {
        return LiabilitiesCostAllocatePlan::create($this);
    }

    public static function channelList(): array
    {
        $channels = CapitalSettlementRuleConfig::find()
            ->where(['funding_nature' => CapitalSettlementRuleConfig::NATURE_BAOLI])
            ->select('asset_loan_channel')
            ->indexBy('asset_loan_channel')
            ->column();

        return CapitalChannel::list(true, 'CONCAT(name, \' \', code)', [
            'code' => array_values($channels),
        ]);
    }

    public static function allocateRate(string $date, bool $throw = true): array
    {
        $sql = <<<SQL
with br as (select date,
                   country,
                   sum(value) as value
            from biz_report
            where type = 'borr_outstanding_amount_daily'
              and date = :date
            group by date, country)
select br.country,
       if((COUNT(*) OVER (PARTITION BY br.date)) != 5, 0,
          FORMAT(br.value * er.rate / (SUM(br.value * er.rate) OVER (PARTITION BY br.date)), 4)) as rate
from br
         inner join exchange_rates er
                    on br.date = er.date
                        and (case br.country
                                 when 'mex' then 'MXN'
                                 when 'idn' then 'IDR'
                                 when 'tha' then 'THB'
                                 when 'phl' then 'PHP'
                                 when 'pak' then 'PKR' end) = er.origin_currency;
SQL;

        $command = BizReport::getDb()->createCommand($sql, ['date' => $date]);

        $allocateRate = $command->queryAll();

        $allocateRate = ArrayHelper::map($allocateRate, 'country', 'rate');

        $totalRate = array_sum($allocateRate);

        if ($throw && $totalRate !== 1.0) {
            throw new RuntimeException(sprintf('当前总分摊比例为: %.2f, 总比例必须为: 1.00', $totalRate));
        }

        return [
            $allocateRate,
            $command->rawSql,
        ];
    }

    public function create(array $data): bool
    {
        return self::getDb()->transaction(function () use ($data) {
            $this->load($data);
            if (!$this->save()) {
                throw new RuntimeException('保存借款记录失败：' . Json::encode($this->errors));
            }

            /** @var CapitalSettlementRuleConfig $rule */
            $rule = CapitalSettlementRuleConfig::findOne([
                'asset_loan_channel' => $this->loan_channel,
            ]);

            if ($rule->is_allocation === CapitalSettlementRuleConfig::ALLOCATION_ALLOCATABLE) {
                [$allocateRate] = self::allocateRate($this->grant_at);
            } else {
                $country = array_find_key(Rule::bizRegions(), static fn($val) => $val === $rule->biz_region);
                $allocateRate = [$country => 1];
            }

            $allocatePlan = $this->createCostAllocatePlan();
            $allocatePlan->beginAt = $this->grant_at;
            $allocatePlan->endAt = $this->due_at;
            $allocatePlan->plan = Json::encode($allocateRate);
            if (!$allocatePlan->save()) {
                throw new RuntimeException('保存分摊计划失败：' . Json::encode($allocatePlan->errors));
            }

            return true;
        });
    }
}
