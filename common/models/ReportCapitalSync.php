<?php

namespace common\models;

use Carbon\Carbon;
use dashboard\traits\ReportModelTrait;
use datasource\DataSourceContext;
use datasource\interfaces\ReportMonitorInterface;
use RuntimeException;
use Throwable;
use yii\base\UserException;
use yii\db\ActiveQuery;
use yii\db\Exception;
use yii\db\StaleObjectException;
use yii\db\Transaction;

class ReportCapitalSync extends ReportCapital implements ReportMonitorInterface
{
    use ReportModelTrait;

    /**
     * @param array $data
     * @param array $config
     * @param DataSourceContext $dataSourceContext
     *
     * @return int
     * @throws Exception
     * @throws Throwable
     * @throws UserException
     */
    public static function storage(array $data, array $config, DataSourceContext $dataSourceContext): int
    {
        if (empty($data)) {
            return 0;
        }

        $data = self::transform($data, $config);

        if (empty($data)) {
            return 0;
        }

        $firstRow = $data[0];

        $createdAt = $firstRow['created_at'];

        $query = self::query($data);

        // 如果数据库已有数据最后一次的创建时间小于当前批次数据的创建时间才允许写入数据库
        // 数据库中没有查到数据也允许写入, 这个情况出现在首次写入时
        $createdAtHis = (string)(clone $query)->select('max(created_at)')->scalar();
        if (!empty($createdAtHis) && Carbon::parse($createdAtHis) >= Carbon::parse($createdAt)) {
            return 0;
        }

        // 将旧数据迁移到历史表
        self::saveReportCapitalHis($query);

        $columns = array_keys($firstRow);

        $command = static::getDb()->createCommand();

        $affectedCountList = array_map(static function (array $data) use ($command, $columns): int {
            return $command->batchInsert(self::tableName(), $columns, $data)->execute();
        }, array_chunk($data, 10000));

        return array_sum($affectedCountList);
    }

    /**
     * @param array $data
     * @return ActiveQuery
     */
    protected static function query(array $data): ActiveQuery
    {
        $country = array_values(array_unique(array_column($data, 'country')));

        // 兼容国内数据源没有country的情况
        if (empty($country)) {
            $country = ['china'];
        }

        return self::find()->where([
            'date' => array_values(array_unique(array_column($data, 'date'))),
            'type' => array_values(array_unique(array_column($data, 'type'))),
            'channel' => array_values(array_unique(array_column($data, 'channel'))),
            'country' => $country,
        ]);
    }

    /**
     * @param ActiveQuery $query
     * @param int $batch
     * @return void
     * @throws Exception
     * @throws Throwable
     * @throws UserException
     */
    protected static function saveReportCapitalHis(ActiveQuery $query, int $batch = 10000): void
    {
        $columns = [
            'date',
            'period_count',
            'type',
            'channel',
            'values',
            'created_at',
            'country',
            'data_source_id',
        ];
        $insertIntoSql = vsprintf('insert into `%s` (`%s`) ', [
            ReportCapitalHis::tableName(),
            implode('`, `', $columns),
        ]);

        $command = ReportCapitalHis::getDb()->createCommand();

        /** @var Transaction $transaction */
        $transaction = self::getDb()->beginTransaction();
        try {
            $chunkIds = array_chunk($query->select('id')->column(), $batch);
            foreach ($chunkIds as $ids) {
                $rawSql = $insertIntoSql . ReportCapital::find()->where(['id' => $ids])->select($columns)->createCommand()->rawSql;

                $affectedInsertCount = $command
                    ->setRawSql($rawSql)
                    ->execute();

                $affectedDeleteCount = ReportCapital::deleteAll([
                    'id' => $ids,
                ]);

                if ($affectedInsertCount !== $affectedDeleteCount || $affectedInsertCount !== count($ids)) {
                    throw new UserException(vsprintf('迁移ReportCapital失败, 删除%d行, 写入历史表%d行', [
                        $affectedDeleteCount,
                        $affectedInsertCount,
                    ]));
                }
            }

            $transaction->commit();
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @param array $data
     * @return void
     */
    public static function clean(array $data): void
    {
        throw new RuntimeException('不允许直接调用');
    }
}
