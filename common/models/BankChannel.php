<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "bank_channel".
 *
 * @property int    $bank_channel_id
 * @property int    $bank_channel_bank_info_id
 * @property string $bank_channel_code
 * @property int    $bank_channel_sort
 * @property string $bank_channel_name
 */
class BankChannel extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'bank_channel';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['bank_channel_bank_info_id', 'bank_channel_sort'], 'required'],
            [['bank_channel_bank_info_id', 'bank_channel_sort'], 'integer'],
            [['bank_channel_name'], 'string'],
            [['bank_channel_code'], 'string', 'max' => 30],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'bank_channel_id'           => '银行代扣渠道ID - 主键',
            'bank_channel_bank_info_id' => '银行信息表ID',
            'bank_channel_code'         => '银行代扣渠道编码',
            'bank_channel_sort'         => '银行代扣渠道优先级，优先级别最高为0',
            'bank_channel_name'         => '银行代扣渠道名',
        ];
    }
}
