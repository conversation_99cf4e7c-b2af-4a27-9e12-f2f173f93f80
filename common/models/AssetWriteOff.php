<?php

namespace common\models;

use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "asset_write_off".
 *
 * @property int    $asset_write_off_id
 * @property int    $asset_write_off_asset_id
 * @property string $asset_write_off_status
 * @property string $asset_write_off_penalty_amount
 * @property string $asset_write_off_remark
 * @property string $asset_write_off_audit_at
 * @property int    $asset_write_off_audit_user_id
 * @property string $asset_write_off_audit_user_name
 * @property int    $asset_write_off_create_user_id
 * @property string $asset_write_off_create_user_name
 * @property string $asset_write_off_create_at
 * @property int    $asset_write_off_update_user_id
 * @property string $asset_write_off_update_user_name
 * @property string $asset_write_off_update_at
 * @property Asset  $assetWriteOffAsset
 */
class AssetWriteOff extends ActiveRecord
{
    public const WRITE_OFF_STATUS_UNREVIEWED = 'unreviewed';
    public const WRITE_OFF_STATUS_APPROVED   = 'approved';
    public const WRITE_OFF_STATUS_REJECTED   = 'rejected';

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'asset_write_off';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'asset_write_off_asset_id',
                    'asset_write_off_create_user_id',
                    'asset_write_off_create_user_name',
                    'asset_write_off_create_at',
                    'asset_write_off_update_user_id',
                    'asset_write_off_update_user_name',
                ],
                'required',
            ],
            [
                [
                    'asset_write_off_asset_id',
                    'asset_write_off_audit_user_id',
                    'asset_write_off_create_user_id',
                    'asset_write_off_update_user_id',
                ],
                'integer',
            ],
            [['asset_write_off_status'], 'string'],
            [['asset_write_off_penalty_amount'], 'number'],
            [['asset_write_off_audit_at', 'asset_write_off_create_at', 'asset_write_off_update_at'], 'safe'],
            [['asset_write_off_remark'], 'string', 'max' => 255],
            [
                [
                    'asset_write_off_audit_user_name',
                    'asset_write_off_create_user_name',
                    'asset_write_off_update_user_name',
                ],
                'string',
                'max' => 10,
            ],
            [
                ['asset_write_off_asset_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => Asset::className(),
                'targetAttribute' => ['asset_write_off_asset_id' => 'asset_id'],
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'asset_write_off_id'               => 'Asset Write Off ID',
            'asset_write_off_asset_id'         => 'Asset Write Off Asset ID',
            'asset_write_off_status'           => 'Asset Write Off Status',
            'asset_write_off_penalty_amount'   => 'Asset Write Off Penalty Amount',
            'asset_write_off_remark'           => 'Asset Write Off Remark',
            'asset_write_off_audit_at'         => 'Asset Write Off Audit At',
            'asset_write_off_audit_user_id'    => 'Asset Write Off Audit User ID',
            'asset_write_off_audit_user_name'  => 'Asset Write Off Audit User Name',
            'asset_write_off_create_user_id'   => 'Asset Write Off Create User ID',
            'asset_write_off_create_user_name' => 'Asset Write Off Create User Name',
            'asset_write_off_create_at'        => 'Asset Write Off Create At',
            'asset_write_off_update_user_id'   => 'Asset Write Off Update User ID',
            'asset_write_off_update_user_name' => 'Asset Write Off Update User Name',
            'asset_write_off_update_at'        => 'Asset Write Off Update At',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getAssetWriteOffAsset(): ActiveQuery
    {
        return $this->hasOne(Asset::class, ['asset_id' => 'asset_write_off_asset_id']);
    }
}
