<?php

namespace common\models;

use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%deposit_bill}}".
 *
 * @property int    $id
 * @property string $date
 * @property string $channel
 * @property string $type
 * @property string $receive_body
 * @property string $receive_account
 * @property int    $balance
 * @property string $status
 * @property string $create_at
 * @property string $update_at
 * @property int    $operator
 */
class DepositBill extends ActiveRecord
{
    public const TYPE_DEPOSIT      = 'deposit_balance';
    public const TYPE_COMPENSATION = 'compensation_balance';
    public const TYPE_LIST         = [
        self::TYPE_DEPOSIT      => '保证金',
        self::TYPE_COMPENSATION => '代偿金',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return '{{%deposit_bill}}';
    }

    /**
     * @return string[]
     *
     * @psalm-return array{new: '待提交', confirming: '待确认', deviation: '待修复', success: '已发送'}
     */
    public static function statusList(): array
    {
        return [
            'new'        => '待提交',
            'confirming' => '待确认',
            'deviation'  => '待修复',
            'success'    => '已发送',
        ];
    }

    public function behaviors(): array
    {
        return [
            [
                'class'              => BlameableBehavior::class,
                'updatedByAttribute' => 'operator',
                'createdByAttribute' => null,
            ],
        ];
    }

    /**
     * @return array
     * @throws KVException
     */
    public static function config(): array
    {
        return KeyValue::takeAsArray('biz_deposit_statement');
    }

    /**
     * @return array
     * @throws KVException
     */
    public static function channels(): array
    {
        return array_intersect_key(Asset::channelList(true), self::config());
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['date', 'channel', 'type', 'receive_body', 'receive_account'], 'required'],
            [['date', 'create_at', 'update_at'], 'safe'],
            [['balance', 'operator'], 'integer'],
            [['channel', 'type', 'receive_account'], 'string', 'max' => 64],
            [['receive_body'], 'string', 'max' => 128],
            [['status'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id'              => 'ID',
            'date'            => '日期',
            'channel'         => '资金方',
            'type'            => '类型',
            'receive_body'    => '收款主体',
            'receive_account' => '收款账号',
            'balance'         => '保证金余额',
            'status'          => '状态',
            'create_at'       => '创建时间',
            'update_at'       => '更新时间',
            'operator'        => '操作者',
        ];
    }

    /**
     * 修复
     *
     * @return bool
     * @throws KVException
     */
    public function repair(): bool
    {
        $config       = self::config();
        $splitDeposit = ($config[$this->channel]['split_deposit'] ?? false);

        $query = ReportCapital::find()->where([
            'channel' => $this->channel,
            'date'    => $this->date,
        ]);
        if ($splitDeposit) {
            $query->andWhere(['type' => $this->type]);
        } else {
            $query->andWhere(['type' => array_keys(self::TYPE_LIST)]);
        }

        $balance = (int)$query->select("sum(`values`)")->scalar();

        return (bool)self::updateAll([
            'balance'  => $balance,
            'status'   => 'new',
            'operator' => Yii::$app->getUser()->id,
        ], [
            'id'     => $this->id,
            'status' => 'deviation',
        ]);
    }
}
