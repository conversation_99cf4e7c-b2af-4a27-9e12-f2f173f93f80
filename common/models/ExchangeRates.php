<?php

namespace common\models;

use xlerr\common\db\SaveTrait;

/**
 * This is the model class for table "exchange_rates".
 *
 * @property int $id
 * @property string $date 日期
 * @property string $origin_currency 基础币种
 * @property string $target_currency 目标币种
 * @property float $rate 利率
 * @property string $source 数据来源
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class ExchangeRates extends \yii\db\ActiveRecord
{
    use SaveTrait;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'exchange_rates';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['date', 'origin_currency', 'target_currency', 'rate', 'source'], 'required'],
            [['date', 'created_at', 'updated_at'], 'safe'],
            [['rate'], 'number'],
            [['origin_currency', 'target_currency'], 'string', 'max' => 3],
            [['source'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'date' => 'Date',
            'origin_currency' => 'Origin Currency',
            'target_currency' => 'Target Currency',
            'rate' => 'Rate',
            'source' => 'Source',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
