<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "withhold_request_his".
 *
 * @property int         $withhold_request_his_id         主键
 * @property int|null    $withhold_request_id
 * @property string      $withhold_request_no             业务主键，编号
 * @property string      $withhold_request_req_key        请求key
 * @property string      $withhold_request_operate_type   代扣操作类型：active  auto  manual
 * @property int         $withhold_request_amount         代扣请求金额（单位:分）
 * @property string      $withhold_request_user_name      用户姓名
 * @property string      $withhold_request_user_phone     手机号
 * @property string      $withhold_request_user_idnum     身份证号
 * @property string      $withhold_request_card_num       银行卡号
 * @property string      $withhold_request_bank_code      银行编码
 * @property string      $withhold_request_status         状态,nofinish:未完成,finish:完成,void:作废
 * @property string|null $withhold_request_creator        创建人
 * @property string|null $withhold_request_operator       操作人
 * @property string      $withhold_request_create_at      创建时间
 * @property string      $withhold_request_update_at      修改时间
 * @property string|null $withhold_request_finished_at    完成时间
 * @property string|null $withhold_request_origin_req_key 原始请求key
 * @property string|null $withhold_request_extend         保存短信验证码等信息
 * @property string|null $withhold_request_from_system    系统来源
 * @property string      $withhold_request_trade_type     交易类型
 * @property string      $withhold_request_his_create_at  创建时间
 */
class WithholdRequestHis extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'withhold_request_his';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbRbizHis');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['withhold_request_id', 'withhold_request_amount'], 'integer'],
            [
                [
                    'withhold_request_no',
                    'withhold_request_req_key',
                    'withhold_request_operate_type',
                    'withhold_request_amount',
                    'withhold_request_user_name',
                    'withhold_request_user_phone',
                    'withhold_request_user_idnum',
                    'withhold_request_card_num',
                    'withhold_request_bank_code',
                    'withhold_request_status',
                    'withhold_request_trade_type',
                ],
                'required',
            ],
            [['withhold_request_operate_type', 'withhold_request_status', 'withhold_request_extend'], 'string'],
            [
                [
                    'withhold_request_create_at',
                    'withhold_request_update_at',
                    'withhold_request_finished_at',
                    'withhold_request_his_create_at',
                ],
                'safe',
            ],
            [
                [
                    'withhold_request_no',
                    'withhold_request_user_name',
                    'withhold_request_user_phone',
                    'withhold_request_user_idnum',
                    'withhold_request_card_num',
                    'withhold_request_creator',
                    'withhold_request_operator',
                    'withhold_request_from_system',
                    'withhold_request_trade_type',
                ],
                'string',
                'max' => 32,
            ],
            [['withhold_request_req_key', 'withhold_request_origin_req_key'], 'string', 'max' => 64],
            [['withhold_request_bank_code'], 'string', 'max' => 12],
            [['withhold_request_req_key'], 'unique'],
            [['withhold_request_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withhold_request_his_id'         => '主键',
            'withhold_request_id'             => 'Withhold Request ID',
            'withhold_request_no'             => '业务主键，编号',
            'withhold_request_req_key'        => '请求key',
            'withhold_request_operate_type'   => '代扣操作类型：active  auto  manual',
            'withhold_request_amount'         => '代扣请求金额（单位:分）',
            'withhold_request_user_name'      => '用户姓名',
            'withhold_request_user_phone'     => '手机号',
            'withhold_request_user_idnum'     => '身份证号',
            'withhold_request_card_num'       => '银行卡号',
            'withhold_request_bank_code'      => '银行编码',
            'withhold_request_status'         => '状态,nofinish:未完成,finish:完成,void:作废',
            'withhold_request_creator'        => '创建人',
            'withhold_request_operator'       => '操作人',
            'withhold_request_create_at'      => '创建时间',
            'withhold_request_update_at'      => '修改时间',
            'withhold_request_finished_at'    => '完成时间',
            'withhold_request_origin_req_key' => '原始请求key',
            'withhold_request_extend'         => '保存短信验证码等信息',
            'withhold_request_from_system'    => '系统来源',
            'withhold_request_trade_type'     => '交易类型',
            'withhold_request_his_create_at'  => '创建时间',
        ];
    }
}
