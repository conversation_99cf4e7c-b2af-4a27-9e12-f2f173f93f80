<?php

namespace common\models;

use Carbon\Carbon;
use dashboard\grid\MoneyTotalDataColumn;
use Xlerr\Metric\Models\BizReport;
use yii\data\SqlDataProvider;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;

class BizReportSearch extends BizReport
{
    public $startDate;
    public $endDate;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => Carbon::parse('30 days ago')->toDateString()],
            [['endDate'], 'default', 'value' => Carbon::parse('yesterday')->toDateString()],
            [['channel'], 'safe'],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'channel' => '通道',
            'startDate' => '开始日期',
            'endDate' => '结束日期',
        ];
    }

    /**
     * @param array $params
     *
     * @return array
     */
    public function search(array $params)
    {
        $this->load($params);
        $this->validate();

        $query = self::find()
            ->where([
                'channel' => 'depbank_acct',
                'type' => 'v2401_dp_acct_flowinout_amount_daily',
            ]);

        $categoryTypes = (clone $query)
            ->select(['subset', 'category'])
            ->groupBy(['subset', 'category'])
            ->asArray()
            ->all();

        $categoryList = BizLabel::find()
            ->where([
                'CONCAT(fund_flow, \'|\', code)' => array_map(static function ($row) {
                    return vsprintf('%s|%s', [
                        $row['subset'],
                        $row['category']
                    ]);
                }, $categoryTypes),
            ])
            ->select(['name', 'fund_flow', 'code'])
            ->asArray()
            ->all();
        $categoryList = ArrayHelper::map($categoryList, static function ($row) {
            return vsprintf('%s|%s', [
                $row['fund_flow'],
                $row['code'],
            ]);
        }, 'name');

        if (empty($categoryList)) {
            $query->andWhere('1 = 0');
        } else {
            $query
                ->andWhere([
                    'and',
                    ['>=', 'date', $this->startDate],
                    ['<', 'date', Carbon::parse($this->endDate)->addDay()->toDateString()],
                ])
                ->select(
                    array_map(static function ($category) {
                        $selectSql = vsprintf(
                            'sum(if(concat(`subset`, \'|\', `category`) = \'%s\', `value`, 0)) as `%s`',
                            [
                                $category,
                                $category,
                            ]
                        );

                        return new Expression($selectSql);
                    }, array_keys($categoryList))
                );

            $subIn = implode(',', array_keys($categoryList));

            $query
                ->addSelect([
                    'date',
                    'in_other' => 'sum(if(`subset` = \'in\' and not find_in_set(concat(`subset`, \'|\', `category`), :subIn), `value`, 0))',
                    'out_other' => 'sum(if(`subset` = \'out\' and not find_in_set(concat(`subset`, \'|\', `category`), :subIn), `value`, 0))',
                ])
                ->addParams([
                    'subIn' => $subIn,
                ])
                ->groupBy('date');
        }

        $mainQuery = new Query();
        $mainQuery
            ->from([
                'report_capital' => ReportCapital::find()
                    ->where([
                        'type' => [
                            'start_balance',
                            'end_balance',
                            'overshort_start_balance',
                            'overshort_end_balance',
                        ],
                    ])
                    ->andWhere([
                        'and',
                        ['>=', 'date', $this->startDate],
                        ['<', 'date', Carbon::parse($this->endDate)->addDay()->toDateString()],
                    ])
                    ->select([
                        'date' => 'date(`date`)',
                        'start_balance' => 'sum(if(type = \'start_balance\', `values`, 0))',
                        'end_balance' => 'sum(if(type = \'end_balance\', `values`, 0))',
                        'overshort_start_balance' => 'sum(if(type = \'overshort_start_balance\', `values`, 0))',
                        'overshort_end_balance' => 'sum(if(type = \'overshort_end_balance\', `values`, 0))',
                    ])
                    ->groupBy('date(`date`)')
            ])
            ->leftJoin(['biz_report' => $query], 'report_capital.date = biz_report.date')
            ->select([
                'biz_report.*',
                'report_capital.start_balance',
                'report_capital.end_balance',
                'report_capital.overshort_start_balance',
                'report_capital.overshort_end_balance',
            ]);

        $command = $mainQuery->createCommand();

        $inColumns = [];
        $outColumns = [];

        foreach ($categoryTypes as $category) {
            $attribute = vsprintf('%s|%s', [
                $category['subset'],
                $category['category'],
            ]);
            $type = $category['subset'];
            if (!array_key_exists($attribute, $categoryList)) {
                continue;
            }
            if ($type === 'in') {
                $inColumns[] = [
                    'label' => $categoryList[$attribute],
                    'attribute' => $attribute,
                    'class' => MoneyTotalDataColumn::class,
                    'headerOptions' => [
                        'class' => 'bg-success text-right',
                    ],
                    'contentOptions' => [
                        'class' => 'bg-success text-right',
                    ],
                ];
            } else {
                $outColumns[] = [
                    'label' => $categoryList[$attribute],
                    'attribute' => $attribute,
                    'class' => MoneyTotalDataColumn::class,
                    'headerOptions' => [
                        'class' => 'bg-danger text-right',
                    ],
                    'contentOptions' => [
                        'class' => 'bg-danger text-right',
                    ],
                ];
            }
        }

        $inTypes = [...array_column($inColumns, 'attribute'), 'in_other'];
        $outTypes = [...array_column($outColumns, 'attribute'), 'out_other'];

        $columns = [
            [
                'label' => '日期',
                'attribute' => 'date',
                'footer' => '合计',
            ],
            [
                'label' => '期初余额',
                'attribute' => 'start_balance',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
            ],
            [
                'label' => '期末余额',
                'attribute' => 'end_balance',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
            ],
            [
                'label' => '期末-期初',
                'class' => MoneyTotalDataColumn::class,
                'value' => function ($data) {
                    $startBalance = (int)($data['start_balance'] ?? 0);
                    $endBalance = (int)($data['end_balance'] ?? 0);

                    return $endBalance - $startBalance;
                },
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
            ],
            [
                'label' => '总流入 - 总流出',
                'class' => MoneyTotalDataColumn::class,
                'value' => function ($data) use ($inTypes, $outTypes) {
                    return array_sum(ArrayHelper::filter($data, $inTypes))
                        - array_sum(ArrayHelper::filter($data, $outTypes));
                },
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #fcf8e3;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #fcf8e3;'
                ],
            ],
            [
                'label' => '核对(账户余额-流入流出余额)',
                'class' => MoneyTotalDataColumn::class,
                'value' => function ($data) use ($inTypes, $outTypes) {
                    $inoutBalance = array_sum(ArrayHelper::filter($data, $inTypes))
                        - array_sum(ArrayHelper::filter($data, $outTypes));

                    $startBalance = (int)($data['start_balance'] ?? 0);
                    $endBalance = (int)($data['end_balance'] ?? 0);

                    return $endBalance - $startBalance - $inoutBalance;
                },
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #e4759d;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #e4759d;'
                ],
            ],
            [
                'label' => 'overshort期末余额',
                'attribute' => 'overshort_end_balance',
                'class' => MoneyTotalDataColumn::class,
            ],
            [
                'label' => '总流入',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'value' => function ($data) use ($inTypes) {
                    return array_sum(ArrayHelper::filter($data, $inTypes));
                },
            ],
            ...$inColumns,
            [
                'label' => '未知流入',
                'attribute' => 'in_other',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-success text-right',
                ],
            ],
            [
                'label' => '总流出',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'value' => function ($data) use ($outTypes) {
                    return array_sum(ArrayHelper::filter($data, $outTypes));
                },
            ],
            ...$outColumns,
            [
                'label' => '未知流出',
                'attribute' => 'out_other',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-danger text-right',
                ],
            ],
        ];

        return [
            new SqlDataProvider([
                'sql' => $command->sql,
                'params' => $command->params,
                'db' => $command->db,
                'sort' => [
                    'attributes' => ['date'],
                    'defaultOrder' => ['date' => SORT_DESC],
                ],
            ]),
            $columns,
        ];
    }
}
