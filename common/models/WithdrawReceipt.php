<?php

namespace common\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%withdraw_receipt}}".
 *
 * @property int         $withdraw_receipt_id
 * @property string|null $withdraw_receipt_trade_no             请求流水号(业务幂等)
 * @property string      $withdraw_receipt_merchant_name        商户编号
 * @property string      $withdraw_receipt_merchant_key         商户流水号
 * @property int         $withdraw_receipt_amount               付款金额（分）
 * @property int         $withdraw_receipt_status               支付状态 0=新建, 1=处理中，2=成功，3=失败 4=冻结
 * @property string      $withdraw_receipt_channel_name         渠道名称
 * @property string|null $withdraw_receipt_channel_key          渠道订单号
 * @property string|null $withdraw_receipt_channel_resp_code    渠道状态码
 * @property string|null $withdraw_receipt_channel_resp_message 渠道错误消息
 * @property string      $withdraw_receipt_started_at           发起时间
 * @property string      $withdraw_receipt_finished_at          完成时间
 * @property string      $withdraw_receipt_created_at           创建时间
 * @property string      $withdraw_receipt_updated_at           更新时间
 * @property string|null $withdraw_receipt_redirect             通道方跳转URL
 */
class WithdrawReceipt extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%withdraw_receipt}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbPaysvr');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'withdraw_receipt_merchant_name',
                    'withdraw_receipt_merchant_key',
                    'withdraw_receipt_amount',
                    'withdraw_receipt_channel_name',
                ],
                'required',
            ],
            [['withdraw_receipt_amount', 'withdraw_receipt_status'], 'integer'],
            [
                [
                    'withdraw_receipt_started_at',
                    'withdraw_receipt_finished_at',
                    'withdraw_receipt_created_at',
                    'withdraw_receipt_updated_at',
                ],
                'safe',
            ],
            [
                [
                    'withdraw_receipt_trade_no',
                    'withdraw_receipt_merchant_name',
                    'withdraw_receipt_merchant_key',
                    'withdraw_receipt_channel_name',
                    'withdraw_receipt_channel_key',
                ],
                'string',
                'max' => 64,
            ],
            [['withdraw_receipt_channel_resp_code'], 'string', 'max' => 32],
            [['withdraw_receipt_channel_resp_message'], 'string', 'max' => 255],
            [['withdraw_receipt_redirect'], 'string', 'max' => 512],
            [
                ['withdraw_receipt_channel_key', 'withdraw_receipt_channel_name'],
                'unique',
                'targetAttribute' => ['withdraw_receipt_channel_key', 'withdraw_receipt_channel_name'],
            ],
            [['withdraw_receipt_trade_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withdraw_receipt_id'                   => 'Withdraw Receipt ID',
            'withdraw_receipt_trade_no'             => '请求流水号(业务幂等)',
            'withdraw_receipt_merchant_name'        => '商户编号',
            'withdraw_receipt_merchant_key'         => '商户流水号',
            'withdraw_receipt_amount'               => '付款金额（分）',
            'withdraw_receipt_status'               => '支付状态 0=新建, 1=处理中，2=成功，3=失败 4=冻结',
            'withdraw_receipt_channel_name'         => '渠道名称',
            'withdraw_receipt_channel_key'          => '通道订单号',
            'withdraw_receipt_channel_resp_code'    => '渠道状态码',
            'withdraw_receipt_channel_resp_message' => '渠道错误消息',
            'withdraw_receipt_started_at'           => '发起时间',
            'withdraw_receipt_finished_at'          => '完成时间',
            'withdraw_receipt_created_at'           => '创建时间',
            'withdraw_receipt_updated_at'           => '更新时间',
            'withdraw_receipt_redirect'             => '通道方跳转URL',
        ];
    }
}
