<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "report_capital_his".
 *
 * @property int    $id
 * @property string $date
 * @property int    $period_count
 * @property string $channel
 * @property string $type
 * @property int    $values
 * @property string $created_at
 * @property string $updated_at
 * @property string $country
 */
class ReportCapitalHis extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'report_capital_his';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['date'], 'required'],
            [['date', 'created_at', 'updated_at', 'country'], 'safe'],
            [['period_count', 'values'], 'integer'],
            [['channel'], 'string', 'max' => 32],
            [['type'], 'string', 'max' => 64],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'date' => '日期',
            'period_count' => '总期次',
            'channel' => '资金方',
            'type' => '类型',
            'values' => '值',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
            'country' => '国家',
        ];
    }
}
