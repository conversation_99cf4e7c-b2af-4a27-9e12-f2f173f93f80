<?php

namespace common\models;

use common\traits\PrivacyProtectedTrait;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "asset_enterprise".
 *
 * @property int    $asset_enterprise_id
 * @property int    $asset_enterprise_asset_id
 * @property string $asset_enterprise_type
 * @property string $asset_enterprise_enterprise_name
 * @property string $asset_enterprise_license
 * @property string $asset_enterprise_office_addr
 * @property string $asset_enterprise_province
 * @property string $asset_enterprise_city
 * @property string $asset_enterprise_contact
 * @property string $asset_enterprise_tel
 * @property string $asset_enterprise_legal_person
 * @property string $asset_enterprise_legal_person_tel
 * @property string $asset_enterprise_legal_person_num
 * @property string $asset_enterprise_backup_name
 * @property string $asset_enterprise_backup_tel
 * @property string $asset_enterprise_grade
 * @property string $asset_enterprise_create_at
 * @property int    $asset_enterprise_create_user_id
 * @property string $asset_enterprise_create_user_name
 * @property string $asset_enterprise_update_at
 * @property int    $asset_enterprise_update_user_id
 * @property string $asset_enterprise_update_user_name
 * @property Asset  $assetEnterpriseAsset
 */
class AssetEnterprise extends ActiveRecord
{
    use PrivacyProtectedTrait;

    public const TYPE_BORROW = 'borrow';
    public const TYPE_SECURE = 'secure';

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'asset_enterprise';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'asset_enterprise_asset_id',
                    'asset_enterprise_type',
                    'asset_enterprise_enterprise_name',
                    'asset_enterprise_license',
                    'asset_enterprise_create_user_id',
                    'asset_enterprise_create_user_name',
                    'asset_enterprise_update_user_id',
                    'asset_enterprise_update_user_name',
                ],
                'required',
            ],
            [
                ['asset_enterprise_asset_id', 'asset_enterprise_create_user_id', 'asset_enterprise_update_user_id'],
                'integer',
            ],
            [['asset_enterprise_type'], 'string'],
            [['asset_enterprise_create_at', 'asset_enterprise_update_at'], 'safe'],
            [['asset_enterprise_enterprise_name', 'asset_enterprise_office_addr'], 'string', 'max' => 255],
            [['asset_enterprise_license'], 'string', 'max' => 45],
            [['asset_enterprise_province', 'asset_enterprise_city'], 'string', 'max' => 64],
            [['asset_enterprise_contact'], 'string', 'max' => 32],
            [
                [
                    'asset_enterprise_tel',
                    'asset_enterprise_legal_person',
                    'asset_enterprise_legal_person_tel',
                    'asset_enterprise_backup_name',
                    'asset_enterprise_backup_tel',
                ],
                'string',
                'max' => 16,
            ],
            [['asset_enterprise_legal_person_num'], 'string', 'max' => 18],
            [['asset_enterprise_grade'], 'string', 'max' => 50],
            [['asset_enterprise_create_user_name', 'asset_enterprise_update_user_name'], 'string', 'max' => 10],
            [
                ['asset_enterprise_asset_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => Asset::className(),
                'targetAttribute' => ['asset_enterprise_asset_id' => 'asset_id'],
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'asset_enterprise_id'               => 'Asset Enterprise ID',
            'asset_enterprise_asset_id'         => '资产信息 ID',
            'asset_enterprise_type'             => '企业类型',
            'asset_enterprise_enterprise_name'  => '企业名称',
            'asset_enterprise_license'          => '营业执照号码',
            'asset_enterprise_office_addr'      => '企业办公地址',
            'asset_enterprise_province'         => '所在省份',
            'asset_enterprise_city'             => '所在城市',
            'asset_enterprise_contact'          => '企业联系人',
            'asset_enterprise_tel'              => '企业联系人电话',
            'asset_enterprise_legal_person'     => '企业法人',
            'asset_enterprise_legal_person_tel' => '企业法人联系电话',
            'asset_enterprise_legal_person_num' => '企业法人身份证号',
            'asset_enterprise_backup_name'      => '企业备用联系人',
            'asset_enterprise_backup_tel'       => '企业备用联系人电话',
            'asset_enterprise_grade'            => '商户等级',
            'asset_enterprise_create_at'        => '创建时间',
            'asset_enterprise_create_user_id'   => '创建用户ID',
            'asset_enterprise_create_user_name' => '创建用户名',
            'asset_enterprise_update_at'        => '更新时间',
            'asset_enterprise_update_user_id'   => '更新用户ID',
            'asset_enterprise_update_user_name' => '更新用户名',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getAssetEnterpriseAsset(): ActiveQuery
    {
        return $this->hasOne(Asset::class, ['asset_id' => 'asset_enterprise_asset_id']);
    }

    public static function deleteEnterprise($enterpriseId, $assetId): void
    {
        self::deleteAll([
            'asset_enterprise_id'       => $enterpriseId,
            'asset_enterprise_asset_id' => $assetId,
        ]);
    }
}
