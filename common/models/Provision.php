<?php

namespace common\models;

use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "provision".
 *
 * @property int        $provision_id
 * @property string     $provision_type
 * @property string     $provision_recharge_serial_no
 * @property string     $provision_item_type
 * @property string     $provision_item_no
 * @property string     $provision_tran_type
 * @property int        $provision_tran_id
 * @property int        $provision_tran_period
 * @property int        $provision_amount
 * @property string     $provision_date
 * @property string     $provision_status
 * @property string     $provision_create_at
 * @property string     $provision_update_at
 * @property-read Asset $asset
 */
class Provision extends ActiveRecord
{
    public const TYPE_ARBITRATION   = 'arbitration';   // 仲裁
    public const TYPE_ASSET_VOID    = 'asset_void';    // 资产作废
    public const TYPE_ASSET_REVERSE = 'asset_reverse'; // 资产冲正
    public const TYPE_CLEAR_ERROR   = 'clear_error';   // 清分错误

    public const ITEM_TYPE_ASSET = "asset";

    public const STATUS_OPEN = 'open';

    public const STATUS_PROCESS = 'process';

    public const STATUS_CLOSE = 'close';

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'provision';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['provision_type', 'provision_item_type', 'provision_tran_type', 'provision_date'], 'required'],
            [['provision_type', 'provision_item_type', 'provision_tran_type', 'provision_status'], 'string'],
            [['provision_tran_id', 'provision_tran_period', 'provision_amount'], 'integer'],
            [['provision_date', 'provision_create_at', 'provision_update_at'], 'safe'],
            [['provision_recharge_serial_no', 'provision_item_no'], 'string', 'max' => 64],
            [
                [
                    'provision_item_no',
                    'provision_tran_period',
                    'provision_tran_type',
                    'provision_type',
                    'provision_item_type',
                ],
                'unique',
                'targetAttribute' => [
                    'provision_item_no',
                    'provision_tran_period',
                    'provision_tran_type',
                    'provision_type',
                    'provision_item_type',
                ],
                'message'         => 'The combination of Provision Type, Provision Item Type, Provision Item No, Provision Tran Type and Provision Tran Period has already been taken.',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'provision_id'                 => 'Provision ID',
            'provision_type'               => 'Provision Type',
            'provision_recharge_serial_no' => 'Provision Recharge Serial No',
            'provision_item_type'          => 'Provision Item Type',
            'provision_item_no'            => 'Provision Item No',
            'provision_tran_type'          => 'Provision Tran Type',
            'provision_tran_id'            => 'Provision Tran ID',
            'provision_tran_period'        => 'Provision Tran Period',
            'provision_amount'             => 'Provision Amount',
            'provision_date'               => 'Provision Date',
            'provision_status'             => 'Provision Status',
            'provision_create_at'          => 'Provision Create At',
            'provision_update_at'          => 'Provision Update At',
        ];
    }

    public function getAsset(): ActiveQuery
    {
        return $this->hasOne(Asset::class, ['asset_item_no' => 'provision_item_no']);
    }
}
