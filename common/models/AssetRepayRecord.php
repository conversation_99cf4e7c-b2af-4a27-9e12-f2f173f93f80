<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "asset_repay_record".
 *
 * @property int    $asset_repay_record_id
 * @property int    $asset_repay_record_asset_id
 * @property int    $asset_repay_record_period
 * @property int    $asset_repay_record_status
 * @property string $asset_repay_record_channel
 * @property string $asset_repay_record_principal_amount
 * @property string $asset_repay_record_repaid_principal_amount
 * @property string $asset_repay_record_interest_amount
 * @property string $asset_repay_record_repaid_interest_amount
 * @property string $asset_repay_record_fee_amount
 * @property string $asset_repay_record_repaid_fee_amount
 * @property string $asset_repay_record_repay_date
 * @property string $asset_repay_record_fee_commission_amt
 * @property string $asset_repay_record_principal_commission_amt
 * @property string $asset_repay_record_interest_commission_amt
 * @property string $asset_repay_record_payoff_at
 * @property int    $asset_repay_record_is_deleted
 * @property int    $asset_repay_record_is_advance
 * @property string $asset_repay_record_create_at
 * @property string $asset_repay_record_update_at
 * @property int    $asset_repay_record_is_raised
 */
class AssetRepayRecord extends ActiveRecord
{
    // 还款状态
    public const STATUS_FINISHED   = 1; // 已完成
    public const STATUS_UNFINISHED = 0; // 未完成

    // 删除标志符
    public const FLAG_DELETED     = 1;
    public const FLAG_NOT_DELETED = 0;

    // 提前还款标志符
    public const ADVANCE_FLEG_ADVANCEED    = 1;
    public const ADVANCE_FLEG_NO_ADVANCEED = 0;

    // 归集标识符
    public const FLAG_RAISED     = 1;
    public const FLAG_NOT_RAISED = 0;

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'asset_repay_record';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'asset_repay_record_asset_id',
                    'asset_repay_record_period',
                    'asset_repay_record_channel',
                    'asset_repay_record_principal_amount',
                    'asset_repay_record_repaid_principal_amount',
                    'asset_repay_record_interest_amount',
                    'asset_repay_record_fee_amount',
                    'asset_repay_record_repay_date',
                    'asset_repay_record_fee_commission_amt',
                    'asset_repay_record_principal_commission_amt',
                    'asset_repay_record_interest_commission_amt',
                ],
                'required',
            ],
            [
                [
                    'asset_repay_record_asset_id',
                    'asset_repay_record_period',
                    'asset_repay_record_status',
                    'asset_repay_record_principal_amount',
                    'asset_repay_record_repaid_principal_amount',
                    'asset_repay_record_interest_amount',
                    'asset_repay_record_repaid_interest_amount',
                    'asset_repay_record_fee_amount',
                    'asset_repay_record_repaid_fee_amount',
                    'asset_repay_record_fee_commission_amt',
                    'asset_repay_record_principal_commission_amt',
                    'asset_repay_record_interest_commission_amt',
                    'asset_repay_record_is_deleted',
                    'asset_repay_record_is_advance',
                    'asset_repay_record_is_raised',
                ],
                'integer',
            ],
            [
                [
                    'asset_repay_record_repay_date',
                    'asset_repay_record_payoff_at',
                    'asset_repay_record_create_at',
                    'asset_repay_record_update_at',
                ],
                'safe',
            ],
            [['asset_repay_record_channel'], 'string', 'max' => 35],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'asset_repay_record_id'                       => '主键',
            'asset_repay_record_asset_id'                 => '资产ID',
            'asset_repay_record_period'                   => '还款期数',
            'asset_repay_record_status'                   => '还款状态',
            'asset_repay_record_principal_amount'         => '应还本金',
            'asset_repay_record_repaid_principal_amount'  => '已还本金',
            'asset_repay_record_interest_amount'          => '应还利息',
            'asset_repay_record_repaid_interest_amount'   => '已还利息',
            'asset_repay_record_fee_amount'               => '应还服务费',
            'asset_repay_record_repaid_fee_amount'        => '已还服务费',
            'asset_repay_record_repay_date'               => '预期还款日',
            'asset_repay_record_fee_commission_amt'       => '费用分润金额（单位分）',
            'asset_repay_record_principal_commission_amt' => '本金分润金额（单位分）',
            'asset_repay_record_interest_commission_amt'  => '利息分润金额（单位分）',
            'asset_repay_record_payoff_at'                => '实际结清日',
            'asset_repay_record_is_deleted'               => '是否删除',
            'asset_repay_record_is_advance'               => '是否提前结清',
            'asset_repay_record_create_at'                => '创建时间',
            'asset_repay_record_update_at'                => '更新时间',
        ];
    }

    public function getStatus(): array
    {
        return [
            self::STATUS_UNFINISHED => '未结清',
            self::STATUS_FINISHED   => '已结清',
        ];
    }

    public function getAsset(): ?Asset
    {
        return Asset::findOne($this->asset_repay_record_asset_id);
    }
}
