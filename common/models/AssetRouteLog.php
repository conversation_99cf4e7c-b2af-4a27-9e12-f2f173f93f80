<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "asset_route_log".
 *
 * @property int    $asset_route_log_id
 * @property string $asset_route_log_asset_item_number
 * @property int    $asset_route_log_asset_type
 * @property string $asset_route_log_borrower_idnum
 * @property string $asset_route_log_borrower_name
 * @property string $asset_route_log_loan_channel
 * @property string $asset_route_log_message
 * @property string $asset_route_create_at
 * @property string $asset_route_update_at
 */
class AssetRouteLog extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'asset_route_log';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['asset_route_log_borrower_name', 'asset_route_create_at'], 'required'],
            [['asset_route_create_at', 'asset_route_update_at'], 'safe'],
            [['asset_route_log_asset_item_number'], 'string', 'max' => 64],
            [['asset_route_log_asset_type'], 'string', 'max' => 60],
            [
                ['asset_route_log_borrower_idnum', 'asset_route_log_borrower_name', 'asset_route_log_loan_channel'],
                'string',
                'max' => 32,
            ],
            [['asset_route_log_message'], 'string', 'max' => 100],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'asset_route_log_id'                => 'Asset Route Log ID',
            'asset_route_log_asset_item_number' => 'Asset Route Log Asset Item Number',
            'asset_route_log_asset_type'        => 'Asset Route Log Asset Type',
            'asset_route_log_borrower_idnum'    => 'Asset Route Log Borrower Idnum',
            'asset_route_log_borrower_name'     => 'Asset Route Log Borrower Name',
            'asset_route_log_loan_channel'      => 'Asset Route Log Loan Channel',
            'asset_route_log_message'           => 'Asset Route Log Message',
            'asset_route_create_at'             => 'Asset Route Create At',
            'asset_route_update_at'             => 'Asset Route Update At',
        ];
    }
}
