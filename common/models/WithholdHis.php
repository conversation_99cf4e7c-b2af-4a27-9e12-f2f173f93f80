<?php

namespace common\models;

use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "withhold_his".
 *
 * @property int $withhold_his_id             主键
 * @property int|null $withhold_id                 主键
 * @property string $withhold_serial_no          代扣流水号，业务主键
 * @property string $withhold_request_no         代扣请求编号
 * @property int $withhold_amount             代扣金额（单位:分）
 * @property string|null $withhold_channel            代扣渠道，根据不同的放款渠道进行路由
 * @property string $withhold_status             状态
 * @property string $withhold_sub_status         子状态,normal:正常,payment_cancel:协议支付取消,agreement:协议支付
 * @property string|null $withhold_channel_code       代扣渠道返回的code
 * @property string|null $withhold_channel_message    代扣渠道返回的消息描述
 * @property string|null $withhold_channel_key
 * @property int|null $withhold_channel_fee        代扣渠道费用（单位:分）
 * @property string|null $withhold_comment            备注
 * @property string|null $withhold_error_code         根据代扣渠道返回的状态自定义的code
 * @property string|null $withhold_custom_code        自定义code
 * @property string $withhold_supplement         是否为补单 Y N
 * @property int|null $withhold_order              代扣顺序
 * @property string|null $withhold_third_serial_no    代扣通道返回的流水号
 * @property string|null $withhold_extend_info        针对一些特殊处理保存数据
 * @property string $withhold_create_at          创建时间
 * @property string $withhold_update_at          更新时间
 * @property string|null $withhold_execute_at         执行时间
 * @property string|null $withhold_finish_at          代扣完成时间
 * @property int|null $withhold_version            版本号
 * @property string $withhold_req_key            代扣请求key
 * @property string $withhold_user_name          姓名
 * @property string $withhold_user_idnum         身份证号
 * @property string $withhold_user_phone         手机号
 * @property string $withhold_card_num           用户卡号
 * @property string $withhold_capital_receive_at 资方接收时间
 * @property string|null $withhold_call_back          回调地址
 * @property string $withhold_his_create_at      创建时间
 * @property-read WithholdRequestHis $withholdRequest
 * @property-read WithholdDetailHis[] $withholdDetails
 */
class WithholdHis extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'withhold_his';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbRbizHis');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [
                ['withhold_id', 'withhold_amount', 'withhold_channel_fee', 'withhold_order', 'withhold_version'],
                'integer',
            ],
            [
                [
                    'withhold_serial_no',
                    'withhold_request_no',
                    'withhold_amount',
                    'withhold_status',
                    'withhold_req_key',
                    'withhold_user_name',
                    'withhold_user_idnum',
                    'withhold_user_phone',
                    'withhold_card_num',
                ],
                'required',
            ],
            [['withhold_status', 'withhold_sub_status', 'withhold_supplement', 'withhold_extend_info'], 'string'],
            [
                [
                    'withhold_create_at',
                    'withhold_update_at',
                    'withhold_execute_at',
                    'withhold_finish_at',
                    'withhold_capital_receive_at',
                    'withhold_his_create_at',
                ],
                'safe',
            ],
            [
                [
                    'withhold_serial_no',
                    'withhold_request_no',
                    'withhold_channel_code',
                    'withhold_error_code',
                    'withhold_custom_code',
                    'withhold_user_name',
                    'withhold_user_idnum',
                    'withhold_user_phone',
                    'withhold_card_num',
                ],
                'string',
                'max' => 32,
            ],
            [['withhold_channel'], 'string', 'max' => 50],
            [['withhold_channel_message', 'withhold_comment'], 'string', 'max' => 255],
            [['withhold_channel_key', 'withhold_third_serial_no', 'withhold_req_key'], 'string', 'max' => 64],
            [['withhold_call_back'], 'string', 'max' => 500],
            [['withhold_serial_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'withhold_his_id' => '主键',
            'withhold_id' => '主键',
            'withhold_serial_no' => '代扣流水号，业务主键',
            'withhold_request_no' => '代扣请求编号',
            'withhold_amount' => '代扣金额（单位:分）',
            'withhold_channel' => '代扣渠道，根据不同的放款渠道进行路由',
            'withhold_status' => '状态',
            'withhold_sub_status' => '子状态,normal:正常,payment_cancel:协议支付取消,agreement:协议支付',
            'withhold_channel_code' => '代扣渠道返回的code',
            'withhold_channel_message' => '代扣渠道返回的消息描述',
            'withhold_channel_key' => 'Withhold Channel Key',
            'withhold_channel_fee' => '代扣渠道费用（单位:分）',
            'withhold_comment' => '备注',
            'withhold_error_code' => '根据代扣渠道返回的状态自定义的code',
            'withhold_custom_code' => '自定义code',
            'withhold_supplement' => '是否为补单 Y N ',
            'withhold_order' => '代扣顺序',
            'withhold_third_serial_no' => '代扣通道返回的流水号',
            'withhold_extend_info' => '针对一些特殊处理保存数据',
            'withhold_create_at' => '创建时间',
            'withhold_update_at' => '更新时间',
            'withhold_execute_at' => '执行时间',
            'withhold_finish_at' => '代扣完成时间',
            'withhold_version' => '版本号',
            'withhold_req_key' => '代扣请求key',
            'withhold_user_name' => '姓名',
            'withhold_user_idnum' => '身份证号',
            'withhold_user_phone' => '手机号',
            'withhold_card_num' => '用户卡号',
            'withhold_capital_receive_at' => '资方接收时间',
            'withhold_call_back' => '回调地址',
            'withhold_his_create_at' => '创建时间',
        ];
    }

    public function getWithholdRequest(): ActiveQuery
    {
        return $this->hasOne(WithholdRequestHis::class, ['withhold_request_no' => 'withhold_request_no']);
    }

    public function getWithholdDetails(): ActiveQuery
    {
        return $this->hasMany(WithholdDetailHis::class, ['withhold_detail_serial_no' => 'withhold_serial_no']);
    }
}
