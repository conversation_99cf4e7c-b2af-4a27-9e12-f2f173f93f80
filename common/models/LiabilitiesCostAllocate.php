<?php

namespace common\models;

use yii\db\ActiveQuery;

/**
 * This is the model class for table "liabilities_cost_allocate".
 *
 * @property int $id ID
 * @property int $liabilities_id 保理资方ID
 * @property string $country 国家
 * @property float $rate 成本占比，百分比
 * @property string $begin_at 生效时间
 * @property string $end_at 失效时间
 * @property string $version 分配版本
 * @property int $created_by 创建人
 * @property int $updated_by 修改人
 * @property string $created_at 创建时间
 * @property string $updated_at 修改时间
 * @property-read User $operator
 */
class LiabilitiesCostAllocate extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'liabilities_cost_allocate';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['rate'], 'default', 'value' => 0.0000],
            [['updated_by'], 'default', 'value' => 0],
            [['liabilities_id', 'country', 'begin_at', 'end_at', 'version'], 'required'],
            [['liabilities_id', 'created_by', 'updated_by'], 'integer'],
            [['rate'], 'number'],
            [['begin_at', 'end_at', 'created_at', 'updated_at'], 'safe'],
            [['country', 'version'], 'string', 'max' => 16],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'liabilities_id' => 'Liabilities ID',
            'country' => '国家',
            'rate' => '占比',
            'begin_at' => '生效时间',
            'end_at' => '失效时间',
            'version' => '版本号',
            'created_by' => '创建人',
            'updated_by' => '操作人',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
        ];
    }

    public function getOperator(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }
}
