<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "atransaction_extra".
 *
 * @property int    $atransaction_extra_id
 * @property int    $atransaction_extra_asset_id
 * @property int    $atransaction_extra_period
 * @property int    $atransaction_extra_atransation_id
 * @property int    $atransaction_extra_amount_f
 * @property string $atransaction_extra_comment
 * @property int    $atransaction_extra_user
 * @property string $atransaction_extra_create_at
 * @property string $atransaction_extra_update_at
 * @property string $atransaction_extra_recharge_type
 * @property string $atransaction_extra_card_number
 * @property string $atransaction_extra_serial_no
 */
class AtransactionExtra extends ActiveRecord
{
    //充值类型
    public const RECHARGE_TYPE_WITHHOLD_RESULT = 'withhold_result'; //代扣
    public const RECHARGE_TYPE_MANUAL          = 'manual';          //人工
    public const RECHARGE_TYPE_PROVISION       = 'provision';

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'atransaction_extra';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'atransaction_extra_asset_id',
                    'atransaction_extra_atransation_id',
                    'atransaction_extra_amount_f',
                    'atransaction_extra_create_at',
                ],
                'required',
            ],
            [
                [
                    'atransaction_extra_asset_id',
                    'atransaction_extra_atransation_id',
                    'atransaction_extra_period',
                    'atransaction_extra_amount_f',
                    'atransaction_extra_user',
                ],
                'integer',
            ],
            [['atransaction_extra_comment'], 'string'],
            [['atransaction_extra_create_at', 'atransaction_extra_update_at'], 'safe'],
            [['atransaction_extra_recharge_type', 'atransaction_extra_serial_no'], 'string', 'max' => 50],
            [['atransaction_extra_card_number'], 'string', 'max' => 45],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'atransaction_extra_id'             => 'Atransaction Extra ID',
            'atransaction_extra_asset_id'       => 'asset表主键',
            'atransaction_extra_atransation_id' => 'atransation表主键',
            'atransaction_extra_period'         => '资产期数',
            'atransaction_extra_amount_f'       => '交易金额',
            'atransaction_extra_comment'        => '备注',
            'atransaction_extra_user'           => '操作人id',
            'atransaction_extra_create_at'      => 'Atransaction Extra Create At',
            'atransaction_extra_update_at'      => 'Atransaction Extra Update At',
            'atransaction_extra_recharge_type'  => '充值类型(withhold_result:代扣,manual:人工)',
            'atransaction_extra_card_number'    => '账户号',
            'atransaction_extra_serial_no'      => '代扣(充值)交易流水号,唯一',
        ];
    }
}
