<?php

namespace common\models;

use dcs\models\CapitalSettlementRuleConfig;
use Xlerr\Metric\Models\ReportConfig;

class BizReport extends \Xlerr\Metric\Models\BizReport
{
    public function invalidChannels(): array
    {
        static $channels;
        if (!isset($channels)) {
            $channels = CapitalSettlementRuleConfig::find()
                ->where([
                    'channel_status' => CapitalSettlementRuleConfig::CHANNEL_STATUS_5,
                ])
                ->select('asset_loan_channel')
                ->distinct()
                ->column();
        }

        return $channels;
    }

    public function rowFilter(ReportConfig $config, array $row): bool
    {
        if (in_array($config->channel($row), $this->invalidChannels(), true)) {
            return false;
        }

        return parent::rowFilter($config, $row);
    }
}
