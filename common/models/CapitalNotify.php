<?php

namespace common\models;

/**
 * This is the model class for table "capital_notify".
 *
 * @property int         $capital_notify_id
 * @property string      $capital_notify_push_serial
 * @property string      $capital_notify_asset_item_no      资产编号
 * @property int         $capital_notify_period_start       还款起始期次
 * @property int         $capital_notify_period_end         还款结束期次
 * @property string      $capital_notify_channel            放款渠道
 * @property string      $capital_notify_withhold_serial_no 执行代扣流水号
 * @property string      $capital_notify_status             推送状态:
 * @property string|null $capital_notify_req_data           请求消息体内容
 * @property string|null $capital_notify_res_data           接口返回结果内容
 * @property string      $capital_notify_plan_at            计划发送时间
 * @property string      $capital_notify_type               overdue：逾期推送,repay：还款推送,payoff:偿清通知
 * @property string      $capital_notify_to_system
 * @property string      $capital_notify_capital_receive_at 资方接收推送成功时间
 * @property string      $capital_notify_create_at          创建时间
 * @property string      $capital_notify_update_at          更新时间
 */
class CapitalNotify extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'capital_notify';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['capital_notify_plan_at', 'capital_notify_status'], 'required'],
            [['capital_notify_plan_at'], 'datetime', 'format' => 'php:Y-m-d H:i:s'],
            [['capital_notify_period_start', 'capital_notify_period_end'], 'integer'],
            [['capital_notify_status', 'capital_notify_req_data', 'capital_notify_res_data'], 'string'],
            [
                [
                    'capital_notify_capital_receive_at',
                    'capital_notify_create_at',
                    'capital_notify_update_at',
                ],
                'safe',
            ],
            [
                ['capital_notify_push_serial', 'capital_notify_asset_item_no', 'capital_notify_to_system'],
                'string',
                'max' => 64,
            ],
            [['capital_notify_channel', 'capital_notify_type'], 'string', 'max' => 32],
            [['capital_notify_withhold_serial_no'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'capital_notify_id' => 'ID',
            'capital_notify_push_serial' => '序列号',
            'capital_notify_asset_item_no' => '资产编号',
            'capital_notify_period_start' => '开始期次',
            'capital_notify_period_end' => '结束期次',
            'capital_notify_channel' => '放款渠道',
            'capital_notify_withhold_serial_no' => '代扣序列号',
            'capital_notify_status' => '状态',
            'capital_notify_req_data' => '请求参数',
            'capital_notify_res_data' => '响应参数',
            'capital_notify_plan_at' => '推送时间',
            'capital_notify_type' => 'overdue：逾期推送,repay：还款推送,payoff:偿清通知',
            'capital_notify_to_system' => '目标系统',
            'capital_notify_capital_receive_at' => '资方接收推送成功时间',
            'capital_notify_create_at' => '创建时间',
            'capital_notify_update_at' => '更新时间',
        ];
    }
}
