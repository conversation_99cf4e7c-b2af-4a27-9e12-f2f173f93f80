<?php

namespace common\models;

use RuntimeException;
use Throwable;
use yii\base\Model;
use yii\db\Transaction;
use yii\helpers\Json;

class LiabilitiesCostAllocatePlan extends Model
{
    public $liabilitiesId;
    public $version;
    public $beginAt;
    public $endAt;
    public $plan;

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['plan', 'beginAt', 'endAt', 'version'], 'required'],
            [['liabilitiesId'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'plan' => '分摊计划',
            'beginAt' => '生效时间',
            'endAt' => '失效时间',
            'version' => '版本号',
        ];
    }

    public function save(): bool
    {
        $plan = Json::decode($this->plan);

        if (1.0 - array_sum($plan) > 0.0000001) {
            throw new RuntimeException('参数错误, 分摊比例合计不为100%');
        }

        $costAllocatePlan = [];
        foreach ($plan as $country => $rate) {
            if ($rate <= 0) {
                continue;
            }
            $costAllocatePlan[] = [
                $this->liabilitiesId,
                $this->beginAt,
                $this->endAt,
                $country,
                $rate,
                $this->version,
            ];
        }

        $db = LiabilitiesCostAllocate::getDb();
        /** @var Transaction $trans */
        $trans = $db->beginTransaction();
        try {
            LiabilitiesCostAllocate::deleteAll([
                'liabilities_id' => $this->liabilitiesId,
                'version' => $this->version,
            ]);

            $affected = (int)$db
                ->createCommand()
                ->batchInsert(LiabilitiesCostAllocate::tableName(), [
                    'liabilities_id',
                    'begin_at',
                    'end_at',
                    'country',
                    'rate',
                    'version',
                ], $costAllocatePlan)
                ->execute();

            if ($affected !== count($costAllocatePlan)) {
                throw new RuntimeException('设置分摊比例失败');
            }
            $trans->commit();

            return true;
        } catch (Throwable $e) {
            $trans->rollBack();
            throw $e;
        }
    }

    public static function create(Liabilities $model): self
    {
        $version = (string)$model->getCostAllocates()->max('version');
        if ($version) {
            $version = substr($version, 1);
        }

        return new self([
            'liabilitiesId' => $model->id,
            'version' => 'v' . (((int)$version) + 1),
            'plan' => '{}'
        ]);
    }
}
