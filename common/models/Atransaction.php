<?php

namespace common\models;

use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "atransaction".
 *
 * @property int          $atransaction_id
 * @property string       $atransaction_type
 * @property string       $atransaction_amount
 * @property string       $atransaction_amount_f
 * @property string       $atransaction_create_at
 * @property string       $atransaction_late
 * @property string       $atransaction_transaction_type
 * @property string       $atransaction_serial_no
 * @property int          $atransaction_transaction_id
 * @property int          $atransaction_account_id
 * @property int          $atransaction_user
 * @property AccountLog[] $accountLogs
 * @property Account      $account
 * @property string       $atransaction_record_at
 * @property-read User    $atransactionUser
 */
class Atransaction extends ActiveRecord
{
    public const TRANSACTION_TYPE_NO_TRANSACTION = 'notransaction';
    public const TRANSACTION_TYPE_D_TRANSACTION  = 'dtransaction';
    public const TRANSACTION_TYPE_F_TRANSACTION  = 'ftransaction';

    public const LATE_STATUS_NORMAL     = 'normal';
    public const LATE_STATUS_LATE       = 'late';
    public const LATE_STATUS_LATEFINISH = 'latefinish';

    public const TYPE_RECHARGE           = 'recharge';
    public const TYPE_AUTORECHARGE       = 'autorecharge';
    public const TYPE_FEERECHARGE        = 'feerecharge';
    public const TYPE_REPAY              = 'repay';
    public const TYPE_WITHDRAW           = 'withdraw';
    public const TYPE_HAND               = 'hand';
    public const TYPE_REFUND             = 'refund';
    public const TYPE_PROVISION_RECHARGE = "provision_recharge";
    public const OPERATER_XIEYIN         = '谢银';

    public $charge_type;
    public $operator_user;

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'atransaction';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['atransaction_type', 'atransaction_transaction_type'], 'string'],
            [['atransaction_amount'], 'number'],
            [['atransaction_create_at'], 'safe'],
            [['atransaction_transaction_id', 'atransaction_account_id', 'atransaction_user'], 'integer'],
            [['atransaction_account_id', 'atransaction_late'], 'required'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'atransaction_id'                 => 'id',
            'atransaction_type'               => '交易类型',
            'atransactionTypeText'            => '交易类型',
            'atransaction_amount'             => '操作金额(元)',
            'atransaction_amount_f'           => '操作金额',
            'atransaction_create_at'          => '交易时间',
            'atransaction_late'               => '交易状态',
            'atransaction_transaction_type'   => '对应资产支付类型',
            'atransactionTransactionTypeText' => '对应资产支付类型',
            'atransaction_transaction_id'     => '支付id',
            'atransaction_account_id'         => '账户id',
            'atransaction_user'               => '操作人',
            'atransaction_record_at'          => '充值时间',
            'atransaction_serial_no'          => '充值流水号',
            'AtransactionRechargeText'        => '充值类型',
            'atransactionUserText'            => '操作人',
        ];
    }

    public function getAtransactionExtra(): ActiveQuery
    {
        return $this->hasOne(AtransactionExtra::class, ['atransaction_extra_atransation_id' => 'atransaction_id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getAccountLog(): ActiveQuery
    {
        return $this->hasOne(AccountLog::class, ['account_log_atransaction_id' => 'atransaction_id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getAccount(): ActiveQuery
    {
        return $this->hasOne(Account::class, ['account_id' => 'atransaction_account_id']);
    }

    /**
     * 获取借款人姓名
     */
    public function getIndividualName(): string
    {
        return $this->account->individual->individual_name;
    }

    /**
     * 获取借款人资产类型
     */
    public function getAssetType()
    {
        $owner = $this->account->account_owner;
        if (empty($this->account->individual->getAssetsByOwner($owner))) {
            return;
        }
        $assetType = $this->account->individual->getAssetsByOwner($owner)[0]->AssetTypeText;
        foreach ($this->account->individual->getAssetsByOwner($owner) as $asset) {
            if (strpos($assetType, $asset->AssetTypeText) === false) {
                $assetType .= '-' . $asset->AssetTypeText;
            }
        }

        return $assetType;
    }

    /**
     * @return Dtransaction|Ftransaction|null
     */
    public function getTransaction()
    {
        if ($this->atransaction_transaction_type === self::TRANSACTION_TYPE_D_TRANSACTION) {
            return Dtransaction::findOne(['dtransaction_id' => $this->atransaction_transaction_id]);
        }

        if ($this->atransaction_transaction_type === self::TRANSACTION_TYPE_F_TRANSACTION) {
            return Ftransaction::findOne(['ftransaction_id' => $this->atransaction_transaction_id]);
        }

        return null;
    }

    public function getAtransactionTransactionTypeDropdownList(): array
    {
        return [
            self::TRANSACTION_TYPE_D_TRANSACTION  => '资金交易',
            self::TRANSACTION_TYPE_F_TRANSACTION  => '费用交易',
            self::TRANSACTION_TYPE_NO_TRANSACTION => '其他交易',
        ];
    }

    public function getAtransactionTransactionTypeText(): string
    {
        switch ($this->atransaction_transaction_type) {
            case self::TRANSACTION_TYPE_D_TRANSACTION:
                $result = '还款';
                break;
            case self::TRANSACTION_TYPE_F_TRANSACTION:
                $result = '罚息';
                break;
            default:
                $result = '充值';
                break;
        }

        if ($this->atransaction_transaction_type === self::TRANSACTION_TYPE_D_TRANSACTION) {
            $result = isset($this->transaction) ? $this->transaction->dtransactionTypeText
                . '(' . $this->transaction->asset->asset_item_no . ')' : '';
        } elseif ($this->atransaction_transaction_type === self::TRANSACTION_TYPE_F_TRANSACTION) {
            $result = isset($this->transaction) ? $this->transaction->ftransactionTypeText
                . '(' . $this->transaction->asset->asset_item_no . ')' : '';
        }

        return $result;
    }

    /**
     * 交易类型下拉列表
     *
     * @return array
     */
    public function getAtransactionDropDownList(): array
    {
        return [
            self::TYPE_PROVISION_RECHARGE => '拨备金充值',
            self::TYPE_RECHARGE           => '充值',
            self::TYPE_AUTORECHARGE       => '代扣充值',
            self::TYPE_FEERECHARGE        => '罚息纠正充值',
            self::TYPE_REPAY              => '还款',
            self::TYPE_WITHDRAW           => '提现',
            self::TYPE_HAND               => '手动调节',
            self::TYPE_REFUND             => '退款',
        ];
    }

    /**
     * 充值类型下拉列表
     *
     * @return array
     */
    public function getChargeTypeDropDownList(): array
    {
        return [
            ''                            => '充值类型',
            self::TYPE_RECHARGE           => '充值',
            self::TYPE_AUTORECHARGE       => '代扣充值',
            self::TYPE_FEERECHARGE        => '罚息纠正充值',
            self::TYPE_PROVISION_RECHARGE => '拨备金充值',
        ];
    }

    /**
     * 操作人下拉列表
     *
     * @return array
     */
    public function getOperaterDropDownList(): array
    {
        return [
            ''                    => '操作人',
            self::OPERATER_XIEYIN => '谢银',
        ];
    }

    /**
     * 充值类型
     *
     * @return string
     */
    public function getAtransactionRechargeText(): string
    {
        switch ($this->atransaction_type) {
            case self::TYPE_RECHARGE:
                return '充值';
            case self::TYPE_AUTORECHARGE:
                return '代扣充值';
            case self::TYPE_FEERECHARGE:
                return '罚息纠正充值';
            case self::TYPE_PROVISION_RECHARGE:
                return '拨备金充值';
            default:
                return '';
        }
    }

    /**
     * 交易类型
     *
     * @return string
     */
    public function getAtransactionTypeText()
    {
        $types = $this->getAtransactionDropDownList();

        return $types[$this->atransaction_type] ?? '';
    }

    public function getAtransactionLateDropdownList(): array
    {
        return [
            self::LATE_STATUS_NORMAL     => '正常',
            self::LATE_STATUS_LATE       => '延迟',
            self::LATE_STATUS_LATEFINISH => '延迟已处理',
        ];
    }

    public function getAtransactionLateText(): string
    {
        switch ($this->atransaction_late) {
            case self::LATE_STATUS_NORMAL:
                $result = '正常';
                break;
            case self::LATE_STATUS_LATE:
                $result = '延迟';
                break;
            case self::LATE_STATUS_LATEFINISH:
                $result = '延迟已处理';
                break;
            default:
                $result = '未知';
                break;
        }

        return $result;
    }

    /**
     * @return ActiveQuery
     */
    public function getAtransactionUser(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'atransaction_user']);
    }

    public function getAtransactionUserText(): string
    {
        return $this->atransactionUser->username ?? '系统';
    }
}
