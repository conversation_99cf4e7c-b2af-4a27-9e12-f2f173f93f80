<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "asset_attachment".
 *
 * @property int    $asset_attachment_id
 * @property int    $asset_attachment_asset_id
 * @property int    $asset_attachment_type
 * @property string $asset_attachment_type_text
 * @property string $asset_attachment_url
 * @property int    $asset_attachment_status
 * @property string $asset_attachment_from_system
 * @property string $asset_attachment_create_at
 * @property string $asset_attachment_update_at
 * @property string $asset_attachment_asset_item_no    资产编号
 * @property string $asset_attachment_contract_code    合同编号
 * @property string $asset_attachment_contract_sign_at 合同时间
 */
class AssetAttachment extends ActiveRecord
{
    //状态
    public const ATTACHMENT_STATUS_OPEN = 1; //正常
    public const ATTACHMENT_STATUS_VOID = 0; //无效

    /**
     * 身份证图片
     */
    public const HN_ATTACHMENT_TYPE_ID_CARD = 1;
    /**
     * 协议图片
     */
    public const HN_ATTACHMENT_TYPE_PROTOCOL = 2;
    /**
     * 单据图片
     */
    public const HN_ATTACHMENT_TYPE_CREDENTIAL = 3;
    /**
     * 视频
     */
    public const HN_ATTACHMENT_TYPE_VIDEO = 4;
    /**
     * 音频
     */
    public const HN_ATTACHMENT_TYPE_RADIO = 5;

    /**
     * 人脸识别照片
     */
    public const HN_ATTACHMENT_TYPE_FACE = 6;

    /**
     * 其他
     */
    public const HN_ATTACHMENT_TYPE_OTHER = 10;

    /**
     * 身份证正面
     */
    public const ATTACHMENT_TYPE_ID_PHOTO_POSITIVE = 1;

    /**
     * 身份证反面
     */
    public const ATTACHMENT_TYPE_ID_PHOTO_OPPOSITE = 2;

    /**
     * 借款协议
     */
    public const ATTACHMENT_TYPE_PROTOCOL = 8;

    /**
     * 手术单
     */
    public const ATTACHMENT_TYPE_OPERATION = 15;

    /**
     * 学生卡或校园卡
     */
    public const ATTACHMENT_TYPE_STUDENT_CARD = 26;

    /**
     * 线上合同协议（海南银行法大大在线查看地址）
     */
    public const ATTACHMENT_TYPE_CONTRACT_ONLINE = 25;

    /**
     * 技术服务协议
     */
    public const ATTACHMENT_TYPE_TECH_CONTRACT = 27;

    /**
     * 资金方借款合同
     */
    public const ATTACHMENT_TYPE_CAPITAL_CONTRACT = 28;

    /**
     * 人脸识别图像
     */
    public const ATTACHMENT_TYPE_FACE_RECOGNITION_IMAGE = 29;

    /**
     * 新网银行授权协议
     */
    public const ATTACHMENT_TYPE_XWBANK_LICENSING_AGREEMENT = 30;
    /**
     * 考拉债权转让协议
     */
    public const ATTACHMENT_TYPE_KAOLA_CREDIT_TRANSFER = 31;
    /**
     * 考拉借款合同
     */
    public const ATTACHMENT_TYPE_KAOLA_GRANT_CONTRACT = 32;

    /**
     * 微神马借款合同
     */
    public const ATTACHMENT_TYPE_WEISMA_GRANT_CONTRACT = 33;

    /**
     * 授权委托协议
     */
    public const ATTACHMENT_TYPE_AUTHORIZATION_AGREEMENT = 34;

    /**
     * 仲裁协议
     */
    public const ATTACHMENT_TYPE_COMPROMISE_AGREEMENT = 35;

    /**
     * 委托代付还款协议
     */
    public const ATTACHMENT_TYPE_ENTRUST_AGREEMENT = 39;

    //线下充值凭证
    public const ATTACHMENT_TYPE_OFFLINE_RECHARGE = 1099;

    public const ATTACHMENT_TYPE_OFFLINE_RECHARGE_TEXT = "线下打款凭证";

    /**
     * 前端（dsq）合同类型 映射 biz合同类型
     *
     * @var array
     */
    public static array $attachmentTypeMapping = [
        'server'                        => 27,   //服务协议
        'qsq_loan'                      => 8,    // 借款合同
        'capital_loan'                  => 28,   //资方签约合同
        'dsq_authorization'             => 34,   //授权委托协议
        'arbitration'                   => 35,   //仲裁协议
        'goods'                         => 41,   //商品购买合同
        'kbgm_buy'                      => 41,   //捆绑购买购买合同
        'order_buy_contract'            => 41,   //订单拆分购买合同
        'goods_bbj'                     => 42,   //商品拨备金协议
        'kbgm_bbj'                      => 42,   //捆绑购买拨备金合同
        'order_bbj_service_contract'    => 42,   //订单拆分拨备金合同
        'goods_service'                 => 43,   //商品服务协议
        'order_service_contract'        => 43,   //订单拆分服务合同
        'user_desensitization_contract' => 45,   //脱敏协议
        'delivery_address_contract'     => 46,   //送达地址协议
        'yhc_platform_contract'         => 8001, //翼汇财-平台服务协议
        'yhc_open_account_contract'     => 8002, //翼汇财-开户服务协议
        'yhc_quick_pay_contract'        => 8003, //翼汇财-快捷支付服务协议
        'yhc_auth_contract'             => 8004, //翼汇财-借款人授权书
        'qzb_user_server_contract'      => 8005, //求职宝用户服务协议
        'qzb_entrust_pay_contract'      => 8006, //求职宝委托代付协议
    ];

    /**
     * 附件类型
     *
     * @var array
     */
    public static array $attachmentTypes = [
        1  => '身份证正面照片',
        2  => '身份证反面照片',
        3  => '手持身份证照片',
        4  => '工牌/名片照片',
        5  => '车辆照片',
        6  => '拍摄合同签名页',
        7  => '现场照片',
        8  => '借款协议',
        9  => '代扣协议',
        10 => '商户合作协议',
        11 => '用户服务协议',
        12 => '支付宝首页',
        13 => '芝麻分',
        14 => '摩托追踪器安装照片',
        15 => '商户上传手术单',
        18 => '准夫妻现场合照',
        19 => '结婚证',
        20 => '产检证明',
        21 => '准生证',
        22 => '出生证',
        23 => '住院清单',
        25 => '线上合同协议查看地址',
        26 => '学生证或校园卡照片（如为学生）',
        27 => '技术服务合同',
        28 => '资金方借款合同',
        29 => '人脸识别图像',
        30 => '新网银行授权协议',
        31 => '考拉债权转让协议',
        32 => '考拉借款合同',
        33 => '微神马借款合同',
        34 => '授权委托协议',
        35 => '仲裁协议',
        36 => '招财猫电子签章授权协议',
        38 => 'NN授权委托协议',
        39 => '委托代付还款协议',
        40 => '融资服务协议',
        41 => '消费金融购买合同',
        42 => '消费金融拨备金协议',
        43 => '消费金融服务协议',
        44 => '委托代付协议',
        45 => '脱敏协议',
        46 => '送达协议',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'asset_attachment';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['asset_attachment_asset_id', 'asset_attachment_type', 'asset_attachment_status'], 'required'],
            [['asset_attachment_asset_id', 'asset_attachment_type', 'asset_attachment_status'], 'integer'],
            [['asset_attachment_create_at', 'asset_attachment_update_at'], 'safe'],
            [['asset_attachment_type_text'], 'string', 'max' => 255],
            [['asset_attachment_url'], 'string', 'max' => 1000],
            [['asset_attachment_from_system'], 'string', 'max' => 24],
            [['asset_attachment_asset_item_no'], 'string', 'max' => 64],
            [['asset_attachment_contract_code'], 'string', 'max' => 25],
            [['asset_attachment_subject'], 'string', 'max' => 48],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'asset_attachment_id'            => 'Asset Attachment ID',
            'asset_attachment_asset_id'      => 'Asset Attachment Asset ID',
            'asset_attachment_type'          => 'Asset Attachment Type',
            'asset_attachment_type_text'     => 'Asset Attachment Type Text',
            'asset_attachment_url'           => 'Asset Attachment Url',
            'asset_attachment_status'        => 'Asset Attachment Status',
            'asset_attachment_from_system'   => 'Asset Attachment From System',
            'asset_attachment_create_at'     => 'Asset Attachment Create At',
            'asset_attachment_update_at'     => 'Asset Attachment Update At',
            'asset_attachment_asset_item_no' => 'Asset Attachment Asset Item No',
            'asset_attachment_contract_code' => 'Asset Attachment Contract Code',
        ];
    }
}
