<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * This is the model class for table "capital_compensated".
 *
 * @property int    $capital_compensated_id
 * @property string $capital_compensated_channel
 * @property string $capital_compensated_item_no
 * @property int    $capital_compensated_period
 * @property int    $capital_compensated_principal
 * @property int    $capital_compensated_interest
 * @property int    $capital_compensated_service_fee
 * @property int    $capital_compensated_management_fee
 * @property int    $capital_compensated_referral_fee
 * @property string $capital_compensated_create_at
 * @property string $capital_compensated_update_at
 */
class CapitalCompensated extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'capital_compensated';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['capital_compensated_channel', 'capital_compensated_item_no', 'capital_compensated_period'], 'required'],
            [
                [
                    'capital_compensated_period',
                    'capital_compensated_principal',
                    'capital_compensated_interest',
                    'capital_compensated_service_fee',
                    'capital_compensated_management_fee',
                    'capital_compensated_referral_fee',
                ],
                'integer',
            ],
            [['capital_compensated_create_at', 'capital_compensated_update_at'], 'safe'],
            [['capital_compensated_channel', 'capital_compensated_item_no'], 'string', 'max' => 64],
            [
                ['capital_compensated_item_no', 'capital_compensated_period'],
                'unique',
                'targetAttribute' => ['capital_compensated_item_no', 'capital_compensated_period'],
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'capital_compensated_id'             => 'ID',
            'capital_compensated_channel'        => '通道',
            'capital_compensated_item_no'        => '资产编号',
            'capital_compensated_period'         => '期次',
            'capital_compensated_principal'      => '本金',
            'capital_compensated_interest'       => '利息',
            'capital_compensated_service_fee'    => '技术服务费',
            'capital_compensated_management_fee' => '贷后管理费',
            'capital_compensated_referral_fee'   => '平台服务费',
            'capital_compensated_create_at'      => '创建时间',
            'capital_compensated_update_at'      => '更新时间',
        ];
    }
}
