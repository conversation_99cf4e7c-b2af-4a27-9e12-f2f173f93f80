<?php

namespace common\models;

use yii\db\ActiveRecord;

/**
 * @property int    id                       编号,
 * @property string final_no                 唯一键
 * @property string pending_batch_no         待清洗还款数据批次号
 * @property string biz_type                 业务类型，repay：还款，compensate：代偿
 * @property string biz_sub_type             还款类型，advance_repay：提前还款，normal_repay：正常还款，overdue_repay：超期还款,compensate:代偿
 * @property string asset_item_no            资产编号
 * @property int    period                   还款期次,
 * @property string asset_owner              资产所有权者(KN-快牛、STB)
 * @property string asset_loan_channel       资金方, 放款渠道
 * @property string asset_type               资产类型，来自Asset表
 * @property string asset_sub_type           资产子类型
 * @property string partly                   是否部分（Y-部分，N-全额）
 * @property string compensated              代偿状态（Y-已代偿，N-未代偿）
 * @property string reorganized              是否展期（Y-展期，N-不展期）
 * @property int    overdue_days             逾期天数,
 * @property int    hold_days                占用天数,
 * @property string asset_ref_order_type     资产关联订单类型
 * @property string asset_ref_order_no       资产关联订单号
 * @property string asset_cmdb_no            资产费率编号
 * @property string asset_actual_grant_time  资产实际放款时间
 * @property string asset_period_type        资产还款周期类型 month，月  quarter，季度,  day 天
 * @property int    asset_period_count       资产还款期数,
 * @property int    asset_period_days        资产周期天数,
 * @property int    capital_period_term      资方资产该期周期长度,
 * @property string compensate_channel       代偿渠道
 * @property string actual_finish_time       实际完成时间
 * @property string expect_finish_time       期望完成时间
 * @property int    amount                   待清分总金额,
 * @property int    distribute_amount        实际清分总金额,
 * @property string status                   最终数据状态，new：新建; confirm：已确认；pushing: 推送中；push：已推送；cancel: 已取消
 * @property string push_time                推送时间
 * @property string ref_final_no             关联已清洗final数据编号，重算专用
 * @property int    redid                    0:未重算; 1:已重算；2：来自重算,
 * @property string origin_status            取消前的状态，取值范围同 status
 * @property string create_time              创建时间
 * @property string update_time              更新时间
 * Class CleanFinalItem
 * @package common\models
 */
class CleanFinal extends ActiveRecord
{
    /**
     * 还款类型: 提前还款
     *
     * @var string
     */
    public const BIZ_SUB_TYPE_ADVANCE_REPAY = 'advance_repay';

    /**
     * 还款类型: 正常还款
     *
     * @var string
     */
    public const BIZ_SUB_TYPE_NORMAL_REPAY = 'normal_repay';

    /**
     * 还款类型: 逾期还款
     *
     * @var string
     */
    public const BIZ_SUB_TYPE_OVERDUE_REPAY = 'overdue_repay';

    /**
     * 业务类型: 还款
     *
     * @var string
     */
    public const BIZ_TYPE_REPAY = 'repay';

    /**
     * 业务类型: 代偿
     *
     * @var string
     */
    public const BIZ_TYPE_COMPENSATE = 'compensate';

    /**
     * 状态: 新建
     *
     * @var string
     */
    public const STATUS_NEW = 'new';

    /**
     * 状态: 已确定
     *
     * @var string
     */
    public const STATUS_CONFIRM = 'confirm';

    /**
     * 状态: 推送中
     *
     * @var string
     */
    public const STATUS_PUSHING = 'pushing';

    /**
     * 状态: 已经推送
     *
     * @var string
     */
    public const STATUS_PUSH = 'push';

    /**
     * 状态: 已取消
     *
     * @var string
     */
    public const STATUS_CANCEL = 'cancel';

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'clean_final';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'final_no',
                    'biz_type',
                    'biz_sub_type',
                    'asset_item_no',
                    'period',
                    'asset_owner',
                    'asset_loan_channel',
                    'asset_type',
                    'asset_sub_type',
                ],
                'required',
            ],
            [['create_time', 'update_time'], 'safe'],
            [
                [
                    'period',
                    'overdue_days',
                    'hold_days',
                    'asset_period_count',
                    'asset_period_days',
                    'capital_period_term',
                    'amount',
                    'distribute_amount',
                    'redid',
                ],
                'integer',
            ],
            [
                [
                    'final_no',
                    'pending_batch_no',
                    'biz_type',
                    'biz_sub_type',
                    'asset_item_no',
                    'asset_owner',
                    'asset_loan_channel',
                    'asset_type',
                    'asset_sub_type',
                    'partly',
                    'compensated',
                    'reorganized',
                    'asset_ref_order_type',
                    'asset_ref_order_no',
                    'asset_cmdb_no',
                    'asset_actual_grant_time',
                    'asset_period_type',
                    'compensate_channel',
                    'actual_finish_time',
                    'expect_finish_time',
                    'status',
                    'push_time',
                    'ref_final_no',
                    'origin_status',
                ],
                'string',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'id'                      => '编号',
            'final_no'                => '唯一键',
            'pending_batch_no'        => '待清洗还款数据批次号',
            'biz_type'                => '业务类型，repay：还款，compensate：代偿',
            'biz_sub_type'            => '还款类型，advance_repay：提前还款，normal_repay：正常还款，overdue_repay：超期还款,compensate:代偿',
            'asset_item_no'           => '资产编号',
            'period'                  => '还款期次',
            'asset_owner'             => '资产所有权者(KN-快牛、STB)',
            'asset_loan_channel'      => '资金方, 放款渠道',
            'asset_type'              => '资产类型，来自Asset表',
            'asset_sub_type'          => '资产子类型',
            'partly'                  => '是否部分（Y-部分，N-全额）',
            'compensated'             => '代偿状态（Y-已代偿，N-未代偿）',
            'reorganized'             => '是否展期（Y-展期，N-不展期）',
            'overdue_days'            => '逾期天数',
            'hold_days'               => '占用天数',
            'asset_ref_order_type'    => '资产关联订单类型',
            'asset_ref_order_no'      => '资产关联订单号',
            'asset_cmdb_no'           => '资产费率编号',
            'asset_actual_grant_time' => '资产实际放款时间',
            'asset_period_type'       => '资产还款周期类型 month，月  quarter，季度,  day 天',
            'asset_period_count'      => '资产还款期数',
            'asset_period_days'       => '资产周期天数',
            'capital_period_term'     => '资方资产该期周期长度',
            'compensate_channel'      => '代偿渠道',
            'actual_finish_time'      => '实际完成时间',
            'expect_finish_time'      => '期望完成时间',
            'amount'                  => '待清分总金额',
            'distribute_amount'       => '实际清分总金额',
            'status'                  => '最终数据状态，new：新建; confirm：已确认；pushing: 推送中；push：已推送；cancel: 已取消',
            'push_time'               => '推送时间',
            'ref_final_no'            => '关联已清洗final数据编号，重算专用',
            'redid'                   => '0:未重算; 1:已重算；2：来自重算',
            'origin_status'           => '取消前的状态，取值范围同 status',
            'create_time'             => '创建时间',
            'update_time'             => '更新时间',
        ];
    }
}
