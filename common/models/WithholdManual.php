<?php

namespace common\models;

use repay\components\RepayComponent;
use yii\base\Model;
use yii\helpers\ArrayHelper;

use function xlerr\desensitise\decrypt;

class WithholdManual extends Model
{
    public $serialNo;
    public $assetItemNo;
    public $period;
    public $username;
    public $channel;
    public $repayType;
    public $principal;
    public $interest;
    public $fee;
    public $lateinterest;
    public $pushAt;
    public $pushStatus;

    public $idNum;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [['assetItemNo', 'repayType', 'period'], 'required'],
            [
                ['username', 'idNum', 'channel', 'principal', 'interest', 'fee', 'lateinterest'],
                'required',
                'on' => 'trial',
            ],
            [['principal', 'interest', 'fee', 'lateinterest'], 'integer', 'min' => 0],
        ];
    }

    public function attributeLabels()
    {
        return [
            'serialNo' => '流水号',
            'assetItemNo' => '资产编号',
            'username' => '借款人姓名',
            'channel' => '资金方',
            'repayType' => '还款类型',
            'principal' => '应还本金',
            'interest' => '应还利息',
            'fee' => '应还费用',
            'lateinterest' => '应还罚息',
            'pushAt' => '推送时间',
            'pushStatus' => '推送状态',

            'period' => '还款期次',
            'idNum' => '身份证号码',
        ];
    }

    public function push(): bool
    {
        $client = RepayComponent::instance();
        if (!$client->offlineRepay($this)) {
            $this->addError('assetItemNo', $client->getError());

            return false;
        } else {
            return true;
        }
    }

    public function trial(): bool
    {
        $client = RepayComponent::instance();
        if (!$client->capitalTranQuery($this)) {
            $this->addError('assetItemNo', $client->getError());

            return false;
        } else {
            $data = $client->getData();

            if (!empty($data['user_name'])) {
                $this->username = decrypt($data['user_name'], true);
            }

            if (!empty($data['user_idnum'])) {
                $this->idNum = decrypt($data['user_idnum'], true);
            }

            $tranList = ArrayHelper::index($data['asset_tran_list'] ?? [], null, 'category');
            $amounts = array_map(function ($group) {
                return array_sum(array_column($group, 'amount'));
            }, $tranList);

            $this->channel = $data['loan_channel'];
            $this->principal = $amounts['principal'] ?? 0;
            $this->interest = $amounts['interest'] ?? 0;
            $this->fee = $amounts['fee'] ?? 0;
            $this->lateinterest = $amounts['late'] ?? 0;

            return true;
        }
    }
}
