<?php

namespace common\models;

use Carbon\Carbon;
use common\consts\Consts;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\ActiveRecordInterface;

/**
 * This is the model class for table "dtransaction".
 *
 * @property int               $dtransaction_id
 * @property int               $dtransaction_asset_id
 * @property string            $dtransaction_type
 * @property float             $dtransaction_amount            实际交易金额，单位：元
 * @property int               $dtransaction_amount_f          实际交易金额，单位：分
 * @property float             $dtransaction_repaid_amount     已经偿还金额：单位：元
 * @property int               $dtransaction_repaid_amount_f
 * @property string            $dtransaction_status
 * @property string            $dtransaction_late_status
 * @property string            $dtransaction_create_at
 * @property string            $dtransaction_finish_at
 * @property string            $dtransaction_update_at
 * @property string            $dtransaction_remark
 * @property string            $dtransaction_add_type
 * @property int               $dtransaction_dtransaction_id
 * @property int               $dtransaction_period
 * @property string            $dtransaction_expect_finish_time
 * @property int               $dtransaction_extension_batch
 * @property int               $dtransaction_decrease_amount   减免的费用，单位分
 * @property Dlog[]            $dlogs
 * @property Dtransaction      $dtransactionDtransaction
 * @property Dtransaction[]    $dtransactions
 * @property-read Ftransaction $lateInterest
 * @property-read Ftransaction $lateManage
 * @property-read Ftransaction $lateService
 * @property-read int          $lateDays
 * @property Asset             $asset
 * @property float             $withholdAmount
 */
class Dtransaction extends ActiveRecord
{
    public const DTRANSACTION_TYPE_GRANT          = 'grant';
    public const DTRANSACTION_TYPE_REPAYINTEREST  = 'repayinterest';
    public const DTRANSACTION_TYPE_REPAYPRINCIPAL = 'repayprincipal';

    public const DTRANSACTION_STATUS_FINISH    = 'finish';
    public const DTRANSACTION_STATUS_NO_FINISH = 'nofinish';

    public const LATE_STATUS_ADVANCE = 'advance';
    public const LATE_STATUS_NORMAL  = 'normal';
    public const LATE_STATUS_OVERDUE = 'overdue';

    public const TYPES = [
        self::DTRANSACTION_TYPE_GRANT          => '放款',
        self::DTRANSACTION_TYPE_REPAYINTEREST  => '偿还利息',
        self::DTRANSACTION_TYPE_REPAYPRINCIPAL => '偿还本金',
    ];

    public const STATUS = [
        self::DTRANSACTION_STATUS_FINISH    => '已完成',
        self::DTRANSACTION_STATUS_NO_FINISH => '未完成',
    ];

    // 业务筛选条件

    /**
     * 依照放款日 来做筛选
     *
     * @var string
     */
    public const TIME_GRANT_TYPE = 'grant';

    /**
     * 依照到期日 来做筛选
     *
     * @var string
     */
    public const TIME_DUE_TIME = 'due';

    /**
     * 三天之内逻辑
     */
    public const TIME_NORMAL_TIME = 'normal';

    public $repay_interest;            //应还利息
    public $repay_principal;           //应还本金
    public $repay_amount;              //应还金额
    public $late_days;                 //逾期天数
    public $same_period_remain_amount; //一期还款的剩余未还金额
    public $remain_principal;
    public $remain_interest;
    public $remain_amount;
    public $repaid_principal;
    public $repaid_interest;
    public $repaid_amount; //已还金额
    public $individual_name;
    public $asset_item_no;

    /**
     * @inheritdoc
     */

    public static function tableName(): string
    {
        return 'dtransaction';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'dtransaction_asset_id',
                    'dtransaction_dtransaction_id',
                    'dtransaction_period',
                    'dtransaction_extension_batch',
                    'dtransaction_amount_f',
                    'dtransaction_repaid_amount_f',
                    'dtransaction_decrease_amount',
                ],
                'integer',
            ],
            [
                [
                    'dtransaction_type',
                    'dtransaction_status',
                    'dtransaction_late_status',
                    'dtransaction_add_type',
                    'dtransaction_finish_channel',
                    'dtransaction_remark',
                ],
                'string',
            ],
            [['dtransaction_amount', 'dtransaction_repaid_amount'], 'number'],
            [
                [
                    'dtransaction_create_at',
                    'dtransaction_expect_finish_time',
                    'dtransaction_finish_at',
                    'dtransaction_update_at',
                ],
                'safe',
            ],
            [
                ['dtransaction_asset_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => Asset::class,
                'targetAttribute' => ['dtransaction_asset_id' => 'asset_id'],
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'dtransaction_id'                 => 'Dtransaction ID',
            'dtransaction_asset_id'           => '所属资产id',
            'dtransaction_type'               => '资金类型',
            'dtransactionTypeText'            => '资金类型',
            'dtransaction_period'             => '期数',
            'dtransaction_amount'             => '金额',
            'dtransaction_amount_f'           => '金额',
            'dtransaction_repaid_amount'      => '已还金额',
            'dtransaction_repaid_amount_f'    => '已还金额',
            'dtransaction_status'             => '状态',
            'dtransactionStatusText'          => '状态',
            'dtransaction_create_at'          => '创建时间',
            'dtransaction_finish_at'          => '完成时间',
            'dtransaction_expect_finish_time' => '预期完成时间',
            'dtransaction_update_at'          => '更新时间',
            'dtransaction_dtransaction_id'    => '父id',
            'realGrantAmount'                 => '实际放款额',
            'dtransaction_remark'             => '备注',
            'dtransaction_extension_batch'    => '展期批次',
            'dtransaction_decrease_amount'    => '减免金额',
        ];
    }

    public function getAsset(): ActiveQuery
    {
        return $this->hasOne(Asset::class, ['asset_id' => 'dtransaction_asset_id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getDtransactionDtransaction(): ActiveQuery
    {
        return $this->hasOne(__CLASS__, ['dtransaction_id' => 'dtransaction_dtransaction_id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getDtransactions(): ActiveQuery
    {
        return $this->hasMany(__CLASS__, ['dtransaction_dtransaction_id' => 'dtransaction_id']);
    }

    /**
     * 获取同期的服务费
     *
     * @return array|ActiveRecordInterface|null
     */
    public function getService()
    {
        return $this->getFtransactionOneType('service');
    }

    /**
     * 获取同期的逾期交易记录
     *
     * @return array|ActiveRecordInterface|null
     */
    public function getLateInterest()
    {
        return $this->getFtransactionOneType('lateinterest');
    }

    /**
     * 逾期总金额
     *
     * @return int|float
     */
    public function getLateInterestAmount()
    {
        return isset($this->lateInterest) ? $this->lateInterest->ftransaction_amount : 0;
    }

    /**
     * 已还逾期金额
     *
     * @return int|float
     */
    public function getLateInterestAmountRepaid()
    {
        return isset($this->lateInterest) ? $this->lateInterest->ftransaction_repaid_amount : 0;
    }

    /**
     * 减免的罚息
     *
     * @return int|float
     */
    public function getLateInterestAmountDecrease()
    {
        return isset($this->lateInterest) ? $this->lateInterest->ftransaction_decrease_amount : 0;
    }

    /**
     * 获取同期的逾期交易记录
     */
    public function getLateManage()
    {
        return $this->getFtransactionOneType('latemanage');
    }

    /**
     * 逾期管理费总金额
     *
     * @return int|float
     */
    public function getLateManageAmount()
    {
        return isset($this->lateManage) ? $this->lateManage->ftransaction_amount : 0;
    }

    /**
     * 已还逾期管理费
     *
     * @return int|float
     */
    public function getLateManageAmountRepaid()
    {
        return isset($this->lateManage) ? $this->lateManage->ftransaction_repaid_amount : 0;
    }

    /**
     * 减免的逾期管理费
     *
     * @return int|float
     */
    public function getLateManageAmountDecrease()
    {
        return isset($this->lateManage) ? $this->lateManage->ftransaction_decrease_amount : 0;
    }

    /**
     * 逾期服务费总金额
     *
     * @return ActiveRecordInterface|array|null
     */
    public function getLateService()
    {
        return Ftransaction::find()
            ->leftJoin('fee', 'fee_id = ftransaction_fee_id')
            ->where([
                'ftransaction_asset_id' => $this->dtransaction_asset_id,
                'fee_type'              => 'lateservice',
            ])->orderBy(['ftransaction_expect_finish_time' => SORT_DESC])
            ->one();
    }

    /**
     * 逾期服务费总额
     *
     * @return int
     */
    public function getLateServiceAmount()
    {
        return isset($this->lateService) ? $this->lateService->ftransaction_amount : 0;
    }

    /**
     * 减免的逾期管理费
     */
    public function getLateServiceAmountDecrease()
    {
        return isset($this->lateService) ? $this->lateService->ftransaction_decrease_amount : 0;
    }

    /**
     * 已还逾期服务费
     *
     * @return int
     */
    public function getLateServiceAmountRepaid()
    {
        return isset($this->lateService) ? $this->lateService->ftransaction_repaid_amount : 0;
    }

    /**
     * 减免的违约金
     */
    public function getLateTotalDecrease()
    {
        return $this->getLateInterestAmountDecrease() + $this->getLateManageAmountDecrease()
            + $this->getLateServiceAmountDecrease();
    }

    /**
     * @return ActiveRecordInterface|array|null
     */
    private function getFtransactionOneType($type)
    {
        return Ftransaction::find()
            ->leftJoin('fee', 'fee_id = ftransaction_fee_id')
            ->where([
                'ftransaction_period'   => $this->dtransaction_period,
                'ftransaction_asset_id' => $this->dtransaction_asset_id,
                'fee_type'              => $type,
            ])->orderBy(['ftransaction_expect_finish_time' => SORT_DESC])
            ->one();
    }

    /**
     * 交易类型文本
     *
     * @return string
     */
    public function getDtransactionTypeText()
    {
        switch ($this->dtransaction_type) {
            case 'grant':
                $result = '放款';
                break;
            case 'repayinterest':
                $result = '偿还利息';
                break;
            case 'repayprincipal':
                $result = '偿还本金';
                break;
            default:
                $result = '未知';
                break;
        }

        return $result;
    }

    /**
     * 资金操作类型下拉
     *
     * @return string[]
     *
     * @psalm-return array{grant: '放款', repayinterest: '偿还利息', repayprincipal: '偿还本金'}
     */
    public function typeDropdownList(): array
    {
        return [
            'grant'          => '放款',
            'repayinterest'  => '偿还利息',
            'repayprincipal' => '偿还本金',
        ];
    }

    /**
     * 交易状态文本
     *
     * @return string
     */
    public function getDtransactionStatusText($dtransaction_type = null, $dtransaction_status = null)
    {
        $dtransaction_type   = $dtransaction_type ?? $this->dtransaction_type;
        $dtransaction_status = $dtransaction_status ?? $this->dtransaction_status;

        return [
                'grantfinish'            => '放款已完成',
                'grantnofinish'          => '放款未完成',
                'repayinterestfinish'    => '还款已完成',
                'repayinterestnofinish'  => '还款未完成',
                'repayprincipalfinish'   => '还款已完成',
                'repayprincipalnofinish' => '还款未完成',
            ][$dtransaction_type . $dtransaction_status] ?? '-';
    }

    /**
     * 获取对应的账户交易记录
     *
     * @return ActiveRecordInterface[]
     *
     * @psalm-return array<ActiveRecordInterface>
     */
    public function getAtransaction(): array
    {
        return Atransaction::find()->where([
            'atransaction_type'             => 'repay',
            'atransaction_transaction_type' => 'dtransaction',
            'atransaction_transaction_id'   => $this->dtransaction_id,
        ])->all();
    }

    /**
     * 获取还款列表
     *
     * @param $assetId
     * @param $period
     * @param $repayment_type
     *
     * @return Dtransaction
     */
    public static function findByAssetIdPeriodAndType($assetId, $period, $repayment_type)
    {
        return self::findOne([
            'dtransaction_asset_id' => $assetId,
            'dtransaction_period'   => $period,
            'dtransaction_type'     => $repayment_type,
        ]);
    }

    /**
     * @return int
     */
    public function getLateDays(): int
    {
        $endDate = $this->dtransaction_status === Consts::TRANSACTION_STATUS_FINISH ? $this->dtransaction_finish_at
            : Carbon::now()->toDateTimeString();

        return Carbon::parse($this->dtransaction_expect_finish_time)->diffInDays($endDate, false);
    }

    /**
     * 根据资产item_no 获取回购所需的dtran数据
     *
     * @param array $item_nos
     *
     * @return array
     */
    public static function getBuybackDtranData($item_nos, $channel, $date)
    {
        $query = self::find()
            ->innerJoin('asset', 'dtransaction_asset_id=asset_id')
            ->leftJoin('rate_extra', 'rate_extra_merchant_number=asset_cmdb_product_number')
            ->leftJoin(
                'withhold_history',
                "asset_id=withhold_result_asset_id and withhold_result_status='success' and withhold_result_channel=:channel and withhold_result_finish_at<:date",
                [':channel' => $channel, ':date' => Carbon::parse($date)->floorDay()->toDateTimeString()]
            )
            ->leftJoin(
                'withhold_result_transaction',
                "withhold_result_transaction_withhold_result_id=withhold_result_id and withhold_result_transaction_period=dtransaction_period"
            )
            ->leftJoin('capital_asset', "capital_asset_item_no=asset_item_no")
            ->leftJoin(
                'capital_transaction',
                "capital_transaction_asset_id=capital_asset_id and capital_transaction_type='principal' and capital_transaction_period=dtransaction_period"
            )
            ->where([
                'asset_status'      => ['repay', 'payoff'],
                'asset_item_no'     => $item_nos,
                'dtransaction_type' => ['repayprincipal', 'repayinterest'],
            ])
            ->select([
                'dtransaction_id',
                'dtransaction_amount_f',
                'dtransaction_period',
                'dtransaction_expect_finish_time',
                'dtransaction_type',
                'asset_item_no',
                'asset_period_count',
                'asset_granted_principal_amount_f',
                'rate_extra_year_days',
                'rate_extra_investor_interest_rate',
                'withhold_result_transaction_period',
                'capital_transaction_operation_type',
            ])->orderBy(['asset_id', 'dtransaction_period']);

        return $query->asArray()
            ->all();
    }
}
