<?php

namespace common\filter;

use common\models\AssetLoanRecord;
use import\interfaces\RowFilterInterface;
use yii\base\UserException;

class ImportCsvJmxFilter implements RowFilterInterface
{
    /**
     * @param array $row
     *
     * @return bool
     * @throws UserException
     */
    public function filter(array $row): bool
    {
        $type = trim($row['type']);
        if (empty($type)) {
            throw new UserException('代偿类型不能为空');
        }
        $loanNo = $row['asset_item_no'];
        $asset = AssetLoanRecord::find()
            ->innerJoinWith(['asset'], false)
            ->where(['asset_loan_record_due_bill_no' => $loanNo])
            ->exists();

        return $asset && $type === 'buyback';
    }
}
