<?php

namespace common\behaviors;

use Carbon\Carbon;
use common\components\ExpressionComponent;
use common\models\Asset;
use Doctrine\SqlFormatter\NullHighlighter;
use Doctrine\SqlFormatter\SqlFormatter;
use Exception;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use xlerr\common\helpers\MoneyHelper;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\StringHelper;

class FormatterBehavior extends \xlerr\common\behaviors\FormatterBehavior
{
    /**
     * @param float|int $amount
     *
     * @return int
     */
    public function asAmount($amount): int
    {
        return $this->asY2f($amount);
    }

    public function asDayOfWeekHuman(int $dayOfWeek): string
    {
        return ['日', '一', '二', '三', '四', '五', '六'][$dayOfWeek];
    }

    /**
     * @param mixed $jsonRaw
     * @param int $length
     * @param string $substring
     * @param string|null $encoding
     *
     * @return mixed
     */
    public function asNJson($jsonRaw, int $length = 30, string $substring = '...', string $encoding = null)
    {
        if (empty($jsonRaw) || !is_string($jsonRaw)) {
            return $jsonRaw;
        }

        try {
            $json = Json::decode($jsonRaw, false);

            return Html::tag('span', StringHelper::truncate(Json::encode($json), $length, $substring, $encoding), [
                'title' => Json::encode($json, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT),
            ]);
        } catch (Exception $e) {
            // nobody
        }

        return StringHelper::truncate($jsonRaw, $length, $substring, $encoding);
    }

    public function asHaoToYuyan($value): string
    {
        $amount = round($value / 10000, 2);

        return MoneyHelper::format($amount);
    }

    /**
     * @param string $string
     * @param int $length
     * @param        ...$params
     *
     * @return string
     */
    public function asTruncate($string, $length = 30, ...$params): string
    {
        $content = StringHelper::truncate((string)$string, $length, ...$params);

        return Html::tag('span', $content, [
            'title' => $string,
        ]);
    }

    /**
     * @param mixed $date
     * @param string $default
     *
     * @return string
     */
    public function asVDate($date, string $default = '-'): string
    {
        $date = Carbon::parse($date);
        if ($date->year < 1980) {
            return $default;
        }

        return $date->toDateString();
    }

    /**
     * @param mixed $date
     * @param string $default
     *
     * @return string
     */
    public function asVTime($date, string $default = '-'): string
    {
        $date = Carbon::parse($date);
        if ($date->year < 1980) {
            return $default;
        }

        return $date->toTimeString();
    }

    /**
     * @param mixed $date
     * @param string $default
     *
     * @return string
     */
    public function asVDateTime($date, string $default = '-'): string
    {
        $date = Carbon::parse($date);
        if ($date->year < 1980) {
            return $default;
        }

        return $date->toDateTimeString();
    }

    /**
     * @param mixed $value
     * @param string $exp
     * @param array $data
     *
     * @return mixed
     * @throws InvalidConfigException
     */
    public function asExpression($value, string $exp, array $data = [])
    {
        if (!isset($data['self'])) {
            $data['self'] = $value;
        }
        /**  @deprecated 当 php>=8.1,symfony/expression-language>=6.2 删除此段代码 */
        //兼容资方回购文件导入的行数据缺少部分列的问题 jiexin_taikang_xinheyuan_buyback
        if (!isset($data['row'])) {
            $data['row'] = $data;
        }

        return ExpressionComponent::instance()->evaluate($exp, $data);
    }

    /**
     * @param $value
     * @param $forceValue
     *
     * @return mixed
     */
    public function asForceValue($value, $forceValue)
    {
        return $forceValue;
    }

    /**
     * @param $value
     * @param $rand
     *
     * @return string
     * @throws \yii\base\Exception
     */
    public function asRandom($value, $rand): string
    {
        return $rand . hash('crc32', microtime() . Yii::$app->security->generateRandomKey());
    }

    /**
     * @param int $val
     *
     * @return string
     * @throws Exception
     */
    public function asNumberToDate($val): string
    {
        return Carbon::parse(Date::excelToTimestamp((int)$val))->toDateString();
    }

    public function asOffsetDays($val, $days, $type): string
    {
        switch ($type) {
            case 'sub':
                $render = -$days;
                break;
            case 'add':
            default:
                $render = $days;
        }

        return (string)Carbon::parse($val)->modify("{$render} days");
    }

    /**
     * 显示百分比
     *
     * @param $value
     *
     * @return string
     */
    public function asPercentage($value): string
    {
        return sprintf('%.2f%%', $value);
    }

    /**
     * @return false|int|null|string
     */
    public function asGetAssetAttributeByAssetLoanRecord($dueBillNo, $assetAttribute)
    {
        return Asset::find()
            ->innerJoinWith([
                'assetLoanRecord' => function (ActiveQuery $query) use ($dueBillNo) {
                    $query->onCondition(['asset_loan_record_due_bill_no' => (string)$dueBillNo]);
                },
            ], false)
            ->select($assetAttribute)
            ->scalar();
    }

    /**
     * @return false|int|string
     */
    public function asGetAssetColumn($assetItemNo, $attribute)
    {
        return Asset::find()
            ->where([
                'asset_item_no' => (string)$assetItemNo,
            ])
            ->select($attribute)
            ->scalar() ?? 0;
    }

    public function asGetCurrentPeriod($val)
    {
        $asset = Asset::findOne([
            'asset_item_no' => $val,
        ]);
        $assetGrantAt = $asset->asset_grant_at;
        $assetPeriodCount = $asset->asset_period_count;

        $currentDate = carbon::parse()->toDateString();
        $currentPeriod = carbon::parse($currentDate)->diffInMonths($assetGrantAt) + 1;
        if ($currentPeriod >= $assetPeriodCount) {
            $currentPeriod = $assetPeriodCount;
        }

        return $currentPeriod;
    }

    /**
     * @param string $sql
     * @param string $class
     * @param array $constructorArgs
     * @param string $action
     *
     * @return string
     */
    public function asSqlFormatter(
        string $sql,
        string $class = NullHighlighter::class,
        array $constructorArgs = [],
        string $action = 'format'
    ): string {
        return (new SqlFormatter(new $class($constructorArgs)))->$action($sql);
    }
}
