<?php

namespace common\consts;

class Consts
{
    public const WITHHOLD_QSQ = 'qsq';

    /**
     * 各种费用类型
     */
    public const FEE_TYPE_SERVICE = 'service';            // 平台服务费
    public const FEE_TYPE_QSCSERVICE = 'qscservice';         // 代扣服务费
    public const FEE_TYPE_MANAGE = 'manage';             // 平台管理费
    public const FEE_TYPE_PENALTY = 'penalty';            // 违约金
    public const FEE_TYPE_LATEINTEREST = 'lateinterest';       // 罚息
    public const FEE_TYPE_LATEMANAGE = 'latemanage';         // 逾期管理费
    public const FEE_TYPE_LATESERVICE = 'lateservice';        // 逾期服务费
    public const FEE_TYPE_WRITEOFF_PENALTY = 'writeoff_penalty';   // 注销资产违约金
    public const FEE_TYPE_DELAY = 'delay';              // 延期服务费
    public const FEE_TYPE_DELAY_INTEREST = 'delay_interest';     // 延期息费
    public const FEE_TYPE_CREDIT = 'credit_fee';         // 延期息费
    public const FEE_TYPE_AFTER_LOAN_MANAGE = 'after_loan_manage';  //贷后管理费
    public const FEE_TYPE_TECH_SERVICE = 'technical_service';  //技术服务费
    public const FEE_TYPE_TECH_SERVICE1 = 'technical_service1'; //技术服务费

    /**
     * 还款类型
     */
    public const ASSET_REPAYMENT_TYPE_END = 'end'; // 提前一月付息，每月付息，到期还本
    public const ASSET_REPAYMENT_TYPE_EQUAL = 'equal'; // 等本等息
    public const ASSET_REPAYMENT_TYPE_ACPI = 'acpi'; // 等额本息
    public const ASSET_REPAYMENT_TYPE_IPCL = 'ipcl'; // 提前付息，到期还本
    public const ASSET_REPAYMENT_TYPE_RTLATAIO = 'rtlataio'; // 到期一次性还本付息
    public const ASSET_REPAYMENT_TYPE_BIAPP = 'biapp'; // 按月付息、到期还本
    public const ASSET_REPAYMENT_TYPE_AVGCAPITAL = 'averagecapital'; // 等额本金
    public const ASSET_REPAYMENT_TYPE_OTHER = 'other'; // 其它还款类型

    /**
     * 资产类型
     */
    public const ASSET_TYPE_GUARANTEE = 'guarantee';      // 担保贷
    public const ASSET_TYPE_ELITELOAN = 'eliteloan';      // 精英贷
    public const ASSET_TYPE_FENQILE = 'fenqile';        // 分期乐
    public const ASSET_TYPE_QUFENQI = 'qufenqi';        // 趣分期
    public const ASSET_TYPE_MEMEDAI = 'memedai';        // 么么贷
    public const ASSET_TYPE_LANTOUZI = 'lantouzi';       // 懒投资
    public const ASSET_TYPE_TAODANGPU = 'taodangpu';      // 淘当铺
    public const ASSET_TYPE_NUOXINBAOLI = 'nuoxinbaoli';    // 诺信保理
    public const ASSET_TYPE_KUNSHANAIDENG = 'kunshanaideng';  // 昆山埃登
    public const ASSET_TYPE_LONGTENGXINTUO = 'longtengxintuo'; // 龙腾信托
    public const ASSET_TYPE_RECEIVE = 'receive';        // 应收账款
    public const ASSET_TYPE_CREDIT = 'credit';         // 信用贷
    public const ASSET_TYPE_MORTGAGE = 'mortgage';       // 抵押贷
    public const ASSET_TYPE_IMUS = 'imus';           // 艾慕斯
    public const ASSET_TYPE_CAR = 'car';            // 车贷
    public const ASSET_TYPE_HOUSE = 'house';          // 房贷
    public const ASSET_TYPE_HOSPITAL = 'hospital';       // 医美分期
    public const ASSET_TYPE_JIAWANQICHE = 'jiawancar';      // 嘉万汽车
    public const ASSET_TYPE_PAY_DAY_LOAN = 'paydayloan';     // 消费分期

    /**
     * 资产子类型
     */
    //快分期的子类型 Asset=hospital
    public const ASSET_SUB_TYPE_SY = 'SY-1';  // 摄影
    public const ASSET_SUB_TYPE_YM = 'YM-1';  // 医美
    public const ASSET_SUB_TYPE_JSG = 'JSG-1'; // 机速购
    public const ASSET_SUB_TYPE_MR = 'MR-1';  // 美容
    public const ASSET_SUB_TYPE_JY = 'JY-1';  // 教育
    public const ASSET_SUB_TYPE_HJ = 'HJ-1';  // 婚嫁
    public const ASSET_SUB_TYPE_FY = 'FY-1';  // 妇幼
    public const ASSET_SUB_TYPE_WS = 'WS-1';  // 纹身

    //paydayloan子类型 AssetType=paydayloan
    public const ASSET_SUB_TYPE_SINGLE = 'single';   // 单期贷
    public const ASSET_SUB_TYPE_MULTIPLE = 'multiple'; // 分期贷
    public const ASSET_SUB_TYPE_DKHK = 'dkhk';     // 贷款黑卡
    public const ASSET_SUB_TYPE_KKJ = 'kkj';      // 卡卡借
    public const ASSET_SUB_TYPE_TRADE = 'trade';    //交易转让

    //信用贷子类型
    public const ASSET_SUB_TYPE_XD = 'XD';   // 薪贷
    public const ASSET_SUB_TYPE_SYD = 'SYD';  // 生意贷
    public const ASSET_SUB_TYPE_KYD = 'KYD';  // 快易贷
    public const ASSET_SUB_TYPE_JYD = 'JYD';  // 生意贷
    public const ASSET_SUB_TYPE_ZYD = 'ZYD';  // 助业贷
    public const ASSET_SUB_TYPE_HYD = 'HYD';  // 恒业贷
    public const ASSET_SUB_TYPE_HXGC = 'HXGC'; // 合兴共创
    public const ASSET_SUB_TYPE_JWCD = 'JWCD'; // 嘉万车贷

    public const DEFAULT_DATE = '1000-01-01 00:00:00';

    /**
     * DSQ多主体
     */
    public const DSQ_MULTI_AGENT = [
        self::ASSET_FROM_SYSTEM_DSQ,
        self::ASSET_FROM_SYSTEM_BANANA,
        self::ASSET_FROM_SYSTEM_STRAWBERRY,
    ];

    /**
     * 资产来源系统
     */
    public const ASSET_FROM_SYSTEM_HXYL = 'hxyl';       // 恒信永利
    public const ASSET_FROM_SYSTEM_DSQ = 'dsq';        //
    public const ASSET_FROM_SYSTEM_BANANA = 'banana';     // 香蕉
    public const ASSET_FROM_SYSTEM_STRAWBERRY = 'strawberry'; // 香蕉
    public const ASSET_FROM_SYSTEM_KFQ = 'kfq';        // 快分期
    public const ASSET_FROM_SYSTEM_CASH = 'cash';       //现金分期

    /**
     * 系统名
     */
    public const SYSTEM_NAME_BIZ = 'BIZ';  // BIZ

    // 废弃的资金方常量
    public const ASSET_LOAN_CHANNEL_NOLOAN = 'noloan';

    public const TRANSACTION_STATUS_FINISH = 'finish';
    public const TRANSACTION_STATUS_NO_FINISH = 'nofinish';
    public const TRANSACTION_STATUS_UN_FINISH = 'unfinish';

    /**
     * 返回码
     */
    public const RESPONSE_SUCCESS = 0;    // 成功
    public const RESPONSE_FAIL = 1;    // 失败
    public const DATE_TYPE_WEEKDAY = 0;
    public const DATE_TYPE_WEEKEND = 1;
    public const DATE_TYPE_HOLIDAY = 2;

    /**
     * 资产子类型映射
     *
     * @var array
     */
    public static array $assetSubTypeList = [
        self::ASSET_SUB_TYPE_SY => '摄影',
        self::ASSET_SUB_TYPE_YM => '医美',
        self::ASSET_SUB_TYPE_JSG => '机速购',
        self::ASSET_SUB_TYPE_MR => '美容',
        self::ASSET_SUB_TYPE_JY => '教育',
        self::ASSET_SUB_TYPE_HJ => '婚嫁',
        self::ASSET_SUB_TYPE_FY => '妇幼',
        self::ASSET_SUB_TYPE_WS => '纹身',
        self::ASSET_SUB_TYPE_SINGLE => '单期贷',
        self::ASSET_SUB_TYPE_MULTIPLE => '分期贷',
        self::ASSET_SUB_TYPE_DKHK => '贷款黑卡',
        self::ASSET_SUB_TYPE_KKJ => '卡卡借',
        self::ASSET_SUB_TYPE_JYD => '精英贷',
        self::ASSET_SUB_TYPE_XD => '薪贷',
        self::ASSET_SUB_TYPE_KYD => '快易贷',
        self::ASSET_SUB_TYPE_SYD => '生意贷',
        self::ASSET_SUB_TYPE_ZYD => '助业贷',
        self::ASSET_SUB_TYPE_HYD => '恒易贷',
        self::ASSET_SUB_TYPE_HXGC => '合兴共创',
        self::ASSET_SUB_TYPE_JWCD => '嘉万车贷',
        self::ASSET_SUB_TYPE_TRADE => '商城分期',
    ];

    /**
     * 资产子类型映射
     *
     * @var array
     */
    public static array $assetFromSystemList = [
        self::ASSET_FROM_SYSTEM_DSQ => '',
        self::ASSET_FROM_SYSTEM_BANANA => '香蕉',
        self::ASSET_FROM_SYSTEM_STRAWBERRY => '草莓',
        self::ASSET_FROM_SYSTEM_KFQ => '快分期',
        self::ASSET_FROM_SYSTEM_CASH => '现金分期',
        self::ASSET_FROM_SYSTEM_HXYL => '恒信永利',
    ];

    public static array $repaymentTypeDisplay = [
        self::ASSET_REPAYMENT_TYPE_END => '提前一月付息，每月付息，到期还本',
        self::ASSET_REPAYMENT_TYPE_EQUAL => '等本等息',
        self::ASSET_REPAYMENT_TYPE_ACPI => '等额本息',
        self::ASSET_REPAYMENT_TYPE_AVGCAPITAL => '等额本金',
        self::ASSET_REPAYMENT_TYPE_IPCL => '提前付息，到期还本',
        self::ASSET_REPAYMENT_TYPE_RTLATAIO => '到期一次性还本付息',
        self::ASSET_REPAYMENT_TYPE_BIAPP => '按月付息，到期还本',
        self::ASSET_REPAYMENT_TYPE_OTHER => '其它',
    ];

    public const ASSET_PERIOD_7_DAY = 7;
    public const ASSET_PERIOD_14_DAY = 14;

    public const REFRESH_COMPENSATION_DAYS = 30;

    //多期贷的总期次列表
    public static array $MULTIPLE_PERIOD_COUNT_LIST = [
        1 => '1期',
        3 => '3期',
        4 => '4期',
        6 => '6期',
        12 => '12期',
    ];

    public static array $PERIOD_DAYS_LIST = [
        '30' => '30天',
    ];

    public const YES = 'Y';
    public const NO = 'N';

    /**
     * ASSET OWN 相关标量
     */
    public const ASSET_OWN_KN = 'KN';             //KN
    public const ASSET_OWN_STB_NLJ = 'STB_NILAIJIE';   //STB 你来借
    public const ASSET_OWN_STB_HHD = 'STB_HHD';        //STB 好好贷
    public const ASSET_OWN_STB_SHUQIANBAO = 'STB_SHUQIANBAO'; //STB 数钱宝
    public const ASSET_OWN_STB_SBD = 'STB_SBD';        //STB 随便贷
    public const ASSET_OWN_STB_LEQUHUA = 'STB_LEQUHUA';    //STB 乐趣花
    public const ASSET_OWN_STB_FULIDAI = 'STB_FULIDAI';    //STB 福利贷
    public const ASSET_OWNER_STB_HAOYUNDAI = 'STB_HAOYUNDAI';  //STB 好运贷
    public const ASSET_OWNER_STB_JISUQIAN = 'STB_JISUQIAN';   //急速钱
    public const ASSET_OWNER_STB_YIKOUDAI = 'STB_YIKOUDAI';   //亿口袋
    public const ASSET_OWNER_STB_HUALEHUAN = 'STB_HUALEHUAN';  //花了还
    public const ASSET_OWNER_STB_FUGUIHEBAO = 'STB_FUGUIHEBAO'; //富贵荷包

    //ASSET OWN 表示
    public const ASSET_OWN_LIST = [
        self::ASSET_OWN_STB_NLJ => '你来借',
        self::ASSET_OWN_KN => '快牛',
        self::ASSET_OWN_STB_HHD => '好好贷',
        self::ASSET_OWN_STB_SHUQIANBAO => '数钱宝',
        self::ASSET_OWN_STB_SBD => '随便贷',
        self::ASSET_OWN_STB_LEQUHUA => '乐趣花',
        self::ASSET_OWN_STB_FULIDAI => '福利贷',
        self::ASSET_OWNER_STB_HAOYUNDAI => '好运贷',
        self::ASSET_OWNER_STB_JISUQIAN => '急速钱',
        self::ASSET_OWNER_STB_YIKOUDAI => '亿口袋',
        self::ASSET_OWNER_STB_HUALEHUAN => '花了还',
        self::ASSET_OWNER_STB_FUGUIHEBAO => '富贵荷包',
    ];

    public static array $orderTypeList = [
        'game_bill' => '游戏消费',
        'r360_bill' => '融360',
        'qiuzhibao_bill' => '求职宝',
        'qunajie_bill' => '去哪借',
        'stb_bill' => 'STB商户',
        'meitu' => '美图',
        'store_bill' => '商城资产',
        'nkz_bill' => '牛客栈',
    ];

    /**
     * 拨备户
     *
     * @var string
     */
    public const PROVISION_ACCOUNT = 'v_pingxiang_weidu_guarantee';
    public const PROVISION_ACCOUNT_HEFEI_WEIDU = 'v_hefei_weidu_bobei';

    /**
     * 下线费用比例配置
     *
     * @var string
     */
    public const CHANNEL_OFFLINE_CONFIG_CONFIG_KEY = 'biz_channel_offline_config';
}
