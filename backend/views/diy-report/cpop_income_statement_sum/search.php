<?php

use Xlerr\CpopIncome\Models\CpopIncomeItem;
use cpm\models\CapitalChannel;
use DiyReport\services\DiyReportService;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use Xlerr\SettlementFlow\Models\Rule;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\View;

/**
 * @var View $this
 * @var ActiveForm $form
 * @var DiyReportService $model
 */

$acctTitles = CpopIncomeItem::find()
    ->select('group_concat(loan_channel)')
    ->groupBy('acct_title')
    ->indexBy('acct_title')
    ->column();

$feeTypes = CpopIncomeItem::find()
    ->alias('i')
    ->innerJoinWith('rule wc', false)
    ->select('group_concat(wc.channel)')
    ->groupBy('wc.fee_type')
    ->indexBy('wc.fee_type')
    ->column();

$channels = CapitalChannel::list();

echo $form->field($model, 'channel', [
    'options' => [
        'style' => 'min-width: 182px',
    ]
])->widget(Select2::class, [
    'data' => $channels,
    'pluginOptions' => [
        'allowClear' => true,
    ],
    'options' => [
        'prompt' => '资金方',
    ]
]);

echo $form->field($model, 'acctTitle', [
    'options' => [
        'style' => 'min-width: 182px',
    ]
])->widget(Select2::class, [
    'data' => [],
    'pluginOptions' => [
        'allowClear' => true,
    ],
    'options' => [
        'prompt' => '会计科目',
    ]
]);

echo $form->field($model, 'calcGroup', [
    'autoPlaceholder' => false,
])->textInput([
    'placeholder' => '计算分组'
]);

echo $form->field($model, 'feeType', [
    'options' => [
        'style' => 'min-width: 182px',
    ]
])->widget(Select2::class, [
    'data' => [],
    'pluginOptions' => [
        'allowClear' => true,
    ],
    'options' => [
        'prompt' => '结算规则',
    ]
]);

echo $form->field($model, 'startDate')->widget(DatePicker::class, [
    'options' => [
        'placeholder' => '开始时间',
    ]
]);

echo $form->field($model, 'endDate')->widget(DatePicker::class, [
    'options' => [
        'placeholder' => '结束时间',
    ]
]);
?>

<script>
    <?php $this->beginBlock('changeEvent') ?>
    const feeTypes = <?= Json::htmlEncode($feeTypes)?>,
        acctTitles = <?= Json::htmlEncode($acctTitles)?>,
        allFeeTypes = <?= Json::htmlEncode(Rule::feeTypeList())?>,
        channelEl = $('#<?= Html::getInputId($model, 'channel')?>'),
        feeTypeEl = $('#<?= Html::getInputId($model, 'feeType')?>'),
        acctTitleEl = $('#<?= Html::getInputId($model, 'acctTitle')?>');
    channelEl.on('change', function () {
        const curChannel = channelEl.val()
        feeTypeEl.empty()
        acctTitleEl.empty()
        Object.entries(feeTypes).forEach(([feeType, channel]) => {
            if (curChannel === '' || channel.split(',').includes(curChannel)) {
                feeTypeEl.append(new Option(allFeeTypes[feeType] ?? feeType, feeType))
            }
        })
        feeTypeEl.val('<?= $model->feeType ?>').change()

        Object.entries(acctTitles).forEach(([acctTitle, channel]) => {
            if (curChannel === '' || channel.split(',').includes(curChannel)) {
                acctTitleEl.append(new Option(acctTitle, acctTitle))
            }
        })
        acctTitleEl.val('<?= $model->acctTitle ?>').change()
    }).change()
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['changeEvent']) ?>
</script>
