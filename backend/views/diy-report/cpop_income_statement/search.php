<?php

use Xlerr\CpopIncome\Models\CpopIncomeItem;
use cpm\models\CapitalChannel;
use DiyReport\services\DiyReportService;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\View;

/**
 * @var View $this
 * @var ActiveForm $form
 * @var DiyReportService $model
 */

$acctTitles = CpopIncomeItem::find()
    ->select('group_concat(loan_channel)')
    ->groupBy('acct_title')
    ->indexBy('acct_title')
    ->column();

$channels = CapitalChannel::list();

echo $form->field($model, 'channel', [
    'options' => [
        'style' => 'min-width: 182px',
    ]
])->widget(Select2::class, [
    'data' => $channels,
    'pluginOptions' => [
        'allowClear' => true,
    ],
    'options' => [
        'prompt' => '资金方',
    ]
]);

echo $form->field($model, 'startDate')->widget(DatePicker::class, [
    'options' => [
        'placeholder' => '开始时间',
    ]
]);

echo $form->field($model, 'endDate')->widget(DatePicker::class, [
    'options' => [
        'placeholder' => '结束时间',
    ]
]);

echo $form->field($model, 'acctTitle', [
    'options' => [
        'style' => 'min-width: 182px',
    ]
])->widget(Select2::class, [
    'data' => [],
    'pluginOptions' => [
        'allowClear' => true,
    ],
    'options' => [
        'prompt' => '会计科目',
    ]
]);

echo $form->field($model, 'calcGroup', [
    'autoPlaceholder' => false,
])->textInput([
    'placeholder' => '计算分组'
]);

echo $form->field($model, 'metricName', [
    'autoPlaceholder' => false,
])->textInput([
    'placeholder' => '业务指标编码'
]);

echo $form->field($model, 'metricCategory', [
    'autoPlaceholder' => false,
])->textInput([
    'placeholder' => '业务指标类别'
]);

?>

<script>
    <?php $this->beginBlock('changeEvent') ?>
    const
        acctTitles = <?= Json::htmlEncode($acctTitles)?>,
        channelEl = $('#<?= Html::getInputId($model, 'channel')?>'),
        acctTitleEl = $('#<?= Html::getInputId($model, 'acctTitle')?>');
    channelEl.on('change', function () {
        const curChannel = channelEl.val()
        acctTitleEl.empty()
        Object.entries(acctTitles).forEach(([acctTitle, channel]) => {
            if (curChannel === '' || channel.split(',').includes(curChannel)) {
                acctTitleEl.append(new Option(acctTitle, acctTitle))
            }
        })
        acctTitleEl.val('<?= $model->acctTitle ?>').change()
    }).change()
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['changeEvent']) ?>
</script>
