<?php

use mdm\admin\models\searchs\AuthItem;
use xlerr\common\widgets\ActiveForm;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View     $this
 * @var AuthItem $model
 * @var array    $labels
 */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <h3 class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action'        => ['index'],
            'method'        => 'get',
            'type'          => ActiveForm::TYPE_INLINE,
            'submitWaiting' => true,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'name') ?>

        <?= $form->field($model, 'ruleName') ?>

        <?= $form->field($model, 'description') ?>

        <?= Html::submitButton(Yii::t('rbac-admin', 'Search'), ['class' => 'btn btn-primary']) ?>
        <?= Html::a(Yii::t('rbac-admin', 'Reset'), [''], ['class' => 'btn btn-default']) ?>
        <?= Html::a(Yii::t('rbac-admin', 'Create ' . $labels['Item']), ['create'], ['class' => 'btn btn-success']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>
