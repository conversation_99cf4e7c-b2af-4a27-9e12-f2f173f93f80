<?php

use mdm\admin\AnimateAsset;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\YiiAsset;

/* @var $this yii\web\View */
/* @var $model mdm\admin\models\Assignment */
/* @var $fullnameField string */

$userName = $model->{$usernameField};
if (!empty($fullnameField)) {
    $userName .= ' (' . ArrayHelper::getValue($model, $fullnameField) . ')';
}
$userName = Html::encode($userName);

$this->title = Yii::t('rbac-admin', 'Assignment') . ' : ' . $userName;

$this->params['breadcrumbs'][] = ['label' => Yii::t('rbac-admin', 'Assignments'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $userName;

AnimateAsset::register($this);
YiiAsset::register($this);
$opts = Json::htmlEncode([
    'items' => $model->getItems(),
]);
$this->registerJs("var _opts = {$opts};");
$this->registerJs($this->render('_script.js'));
$animateIcon = ' <i class="fa fa-refresh fa-spin"></i>';
?>

<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title"><?= $this->title ?></h3>
    </div>

    <div class="box-body">
        <div class="row">
            <div class="col-sm-5">
                <input class="form-control search" data-target="available"
                       placeholder="<?= Yii::t('rbac-admin', 'Search for available'); ?>">
                <select multiple size="20" class="form-control list" data-target="available" style="margin-top: 10px">
                </select>
            </div>
            <div class="col-sm-2">
                <br/>
                <br/>
                <br/>
                <?= Html::a('<i class="fa fa-angle-double-right"></i>' . $animateIcon, ['assign', 'id' => (string) $model->id], [
                    'class'       => 'btn btn-block btn-success btn-assign',
                    'data-target' => 'available',
                    'title'       => Yii::t('rbac-admin', 'Assign'),
                ]); ?>
                <br/>
                <br/>
                <?= Html::a('<i class="fa fa-angle-double-left"></i>' . $animateIcon, ['revoke', 'id' => (string) $model->id], [
                    'class'       => 'btn btn-block btn-danger btn-assign',
                    'data-target' => 'assigned',
                    'title'       => Yii::t('rbac-admin', 'Remove'),
                ]); ?>
            </div>
            <div class="col-sm-5">
                <input class="form-control search" data-target="assigned"
                       placeholder="<?= Yii::t('rbac-admin', 'Search for assigned'); ?>">
                <select multiple size="20" class="form-control list" data-target="assigned" style="margin-top: 10px">
                </select>
            </div>
        </div>
    </div>
</div>
