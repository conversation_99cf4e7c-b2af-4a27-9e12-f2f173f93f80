<?php

use backend\assets\AppAsset;
use backend\assets\NotifyAsset;
use common\models\User;
use mdm\admin\components\Configs;
use mdm\admin\components\MenuHelper;
use xlerr\adminlte\widgets\Alert;
use xlerr\adminlte\widgets\Menu;
use xlerr\desensitise\DesensitiseWidget;
use yii\helpers\Html;
use yii\helpers\StringHelper;
use yii\web\JsExpression;
use yii\web\View;

use function xlerr\adminlte\userFullName;

return [
    'adminEmail' => '<EMAIL>',

    'common-payment' => [
        'db' => 'dbPaysvr',
        'defaultNamespace' => 'payment',
        'disabledRetryable' => true,
    ],

    'mdm.admin.configs' => [
        'class' => Configs::class,
        'db' => 'dbPortal',
        'userDb' => 'dbPortal',
        //        'class' => Module::class,
        //        'layout' => 'left-menu',
    ],

    'audit.index.columns' => [
        [
            'label' => '申请人',
            'attribute' => 'audit_creator_id',
            'format' => function (int $userId) {
                $user = User::findOne($userId);

                return $user ? userFullName($user) : '-';
            },
        ],
    ],

    'adminlte' => [
        'logo-lg' => '资管系统',
        'logo-mini' => '资管',
        'homePageUrl' => ['/dashboard/dashboard/index'],
        'appAssetClass' => AppAsset::class,
        'globalWidgets' => [
            DesensitiseWidget::class,
            Alert::class,
        ],
        'user' => [
            'summary' => function () {
                $userId = Yii::$app->getUser()->id;
                $authManager = Yii::$app->getAuthManager();
                if (!$userId || !$authManager) {
                    return '';
                }

                $roles = $authManager->getRolesByUser($userId);
                $roles = implode(',', array_keys($roles));

                return Html::tag('span', StringHelper::truncate($roles, 8), ['title' => $roles]);
            },
        ],
        'menu' => static function () {
            $userId = Yii::$app->getUser()->id;
            if (!$userId) {
                return [];
            }

            $items = array_merge(
                [
                    //                    ['label' => '菜单', 'options' => ['class' => 'header']],
                    [
                        'label' => '仪表盘',
                        'url' => ['/dashboard/dashboard/index'],
                        'icon' => 'tachometer-alt fa-fw',
                    ],
                ],
                MenuHelper::getAssignedMenu($userId, 0, function (array $menu): array {
                    $data = (array)json_decode($menu['data'] ?? '', true);

                    $route = (array)($data['params'] ?? []);
                    array_unshift($route, $menu['route']);

                    return [
                        'label' => $menu['name'],
                        'url' => $route,
                        'icon' => $data['icon'] ?? '',
                        'items' => $menu['children'],
                    ];
                })
            );

            return Menu::widget([
                'items' => $items,
            ]);
        },
        'customNavbar' => [
            [
                'icon' => 'fa fa-fw fa-search',
                'options' => [
                    'onclick' => new JsExpression('(() => window.bizSearch.show())()'),
                ],
            ],
            [
                'icon' => 'fa fa-fw fa-calendar-alt',
                'options' => [
                    'onclick' => new JsExpression(
                        '(() => window.makeDialog({type: 2, title: \'日历\', content: \'/event/event/fullcalendar\'}))()'
                    ),
                ],
            ],
            [
                'icon' => 'fa fa-fw fa-bell-o',
                'options' => [
                    'id' => 'openMsgMenu',
                ],
                'func' => static function (View $view): void {
                    NotifyAsset::register($view);
                },
            ],
            [
                'text' => static fn(): string => userFullName(Yii::$app->getUser()->identity),
                'icon' => 'fa fa-fw fa-sign-out-alt',
                'options' => [
                    'title' => '注销',
                    'onclick' => new JsExpression('(() => window.location.href = \'/site/logout\')()'),
                ],
            ],
        ],
    ],
];
