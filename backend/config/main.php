<?php

use common\models\User as UserModel;
use common\web\User;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use kvmanager\Module;
use mdm\admin\components\AccessControl;
use mdm\admin\components\DbManager;
use yii\web\CacheSession;
use yii\web\JsonParser;

$params = array_merge(
    require(__DIR__ . '/../../common/config/params.php'),
    require(__DIR__ . '/../../common/config/params-local.php'),
    require(__DIR__ . '/params.php'),
    require(__DIR__ . '/params-local.php')
);

return [
    'id' => 'app-backend',
    'language' => 'zh-CN',
    'basePath' => dirname(__DIR__),
    'controllerNamespace' => 'backend\controllers',
    'layout' => '@vendor/xlerr/yii2-adminlte/src/views/layouts/main.php',
    'bootstrap' => ['log', 'kvmanager', 'dashboard', 'capital', 'diy-report'],
    'modules' => [
        'kvmanager' => Module::class,
        'statement' => \Codingheping\StatementComponent\Module::class,
        'mq' => \xlerr\mq\Module::class,
        'webtask' => \Codingheping\WebTask\Module::class,
        'task' => \xlerr\task\Module::class,
        'event' => \EventManager\Module::class,
        'gateway' => \gateway\Module::class,
        'system' => \system\Module::class,
        'payment' => \payment\Module::class,
        'common-payment' => \CommonPayment\Module::class,
        'grant' => \grant\Module::class,
        'contract' => \contract\Module::class,
        'repay' => \repay\Module::class,
        'cmdb' => \cmdb\Module::class,
        'dcs' => \dcs\Module::class,
        'capital' => \capital\Module::class,
        'cpm' => \cpm\Module::class,
        'dashboard' => \dashboard\Module::class,
        'diy-report' => \DiyReport\Module::class,
        'account' => \account\Module::class,
        'jasypt' => \xlerr\jasypt\Module::class,
        'gridview' => \kartik\grid\Module::class,
        'audit' => \waterank\audit\Module::class,
        'admin' => [
            'class' => \mdm\admin\Module::class,
            'viewPath' => '@backend/views/admin',
        ],
        'kuainiu' => [
            'class' => \kuainiu\Module::class,
            'vpEmails' => function (): array {
                try {
                    return KeyValue::takeAsArray('vp_emails');
                } catch (KVException $e) {
                    return [];
                }
            },
        ],
    ],
    'as access' => [
        'class' => AccessControl::class,
        'allowActions' => [
            'dashboard/index',
            'dashboard/dashboard/index',
            'datasource/dashboard-data-source-sync/data-all',
            'site/*',
            'kuainiu/*',
        ],
    ],
    'components' => [
        'request' => [
            'trustedHosts' => ['*'],
            'parsers' => [
                'application/json' => JsonParser::class,
            ],
        ],
        'user' => [
            'class' => User::class,
            'identityClass' => UserModel::class,
            'enableAutoLogin' => YII_ENV_DEV, // 开发环境允许自动登录
            'identityCookie' => [
                'name' => '_identity-biz',
                'httpOnly' => true,
            ],
        ],
        'view' => [
            'theme' => [
                'pathMap' => [
                    '@vendor/xlerr/yii2-adminlte/src/views/layouts' => '@backend/views/layout',
                ],
            ],
        ],
        'authManager' => [
            'class' => DbManager::class,
            'db' => 'dbPortal',
            'cache' => 'cache',
        ],
        'assetManager' => [
            'appendTimestamp' => false,
            'linkAssets' => true,
        ],
        'session' => [
            'class' => CacheSession::class,
            'timeout' => 8 * 60 * 60, // 8小时
        ],
        'urlManager' => [
            'rules' => [
                'login' => 'site/login',
                'logout' => 'site/logout',
                'dashboard' => 'dashboard/dashboard/index',
            ],
        ],
    ],
    'params' => $params,
];
