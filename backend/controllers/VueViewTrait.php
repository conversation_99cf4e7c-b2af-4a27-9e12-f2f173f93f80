<?php

namespace backend\controllers;

use Yii;
use yii\base\InvalidConfigException;
use yii\web\Response;

trait VueViewTrait
{
    /**
     * @param string $name
     *
     * @return Response
     * @throws InvalidConfigException
     */
    public function asVue(string $name): Response
    {
        [$dstDir, $dir] = Yii::$app->assetManager->publish('@xlerr/adminlte-vue');

        $content = file_get_contents("{$dstDir}/{$name}.html");
        $content = str_replace('"./static/', "\"{$dir}/static/", $content);

        return new Response([
            'format' => Response::FORMAT_HTML,
            'content' => $content,
        ]);
    }
}
