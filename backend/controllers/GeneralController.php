<?php

namespace backend\controllers;

use Xlerr\SettlementFlow\Models\Rule;
use yii\web\Controller;
use yii\web\Response;

class GeneralController extends Controller
{
    public function actionDiyReportAcctGroups(string $channel = ''): Response
    {
        $acctGroups = (array)Rule::find()
            ->where(['<>', 'acct_group', ''])
            ->filterWhere(['channel' => $channel])
            ->select('acct_group')
            ->distinct()
            ->column();

        return $this->asJson($acctGroups);
    }

    public function actionDiyReportFeeTypes(string $channel = '', string $acctGroup = ''): Response
    {
        $acctGroups = (array)Rule::find()
            ->filterWhere([
                'channel' => $channel,
                'acct_group' => $acctGroup,
            ])
            ->select('fee_type')
            ->distinct()
            ->column();

        return $this->asJson($acctGroups);
    }
}
