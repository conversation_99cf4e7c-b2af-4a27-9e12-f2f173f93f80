{"name": "kuainiu/biz", "description": "biz 2", "keywords": ["biz"], "homepage": "https://git.kuainiujinke.com/labs/biz", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"wiki": "https://git.kuainiujinke.com/labs/biz/wikis/home", "source": "https://git.kuainiujinke.com/labs/biz"}, "minimum-stability": "stable", "require": {"php": ">=7.4 || ^8.0", "ext-bcmath": "*", "ext-curl": "*", "ext-dom": "*", "ext-iconv": "*", "ext-json": "*", "ext-pdo": "*", "ext-zlib": "*", "codingheping/code-diff": "^1.0", "codingheping/import": "^1.7", "codingheping/statement-component": "^1.0", "codingheping/web-task": "^1.0", "doctrine/sql-formatter": "^1.1", "guzzlehttp/guzzle": "^7.4", "kartik-v/yii2-widget-activeform": "v1.6.2", "kuainiu/yii2-kuainiu": "^2.0", "mdmsoft/yii2-admin": "^2.0", "monolog/monolog": "^2.2", "nesbot/carbon": "^2.72", "notamedia/yii2-sentry": "^1.7", "overtrue/pinyin": "^4.1", "phpoffice/phpspreadsheet": "^1.29", "predis/predis": "^2.1", "qcloud/cos-sdk-v5": "^2.5", "symfony/css-selector": "^5.4", "symfony/dom-crawler": "^5.4", "symfony/expression-language": "^5.2", "symfony/polyfill": "^1.26", "waterank/audit": "3.13.3.3", "xlerr/adminlte-vue": "^0.2", "xlerr/application-payment": "^1.1", "xlerr/cmdb": "^1.7", "xlerr/common-payment": "^1.0", "xlerr/cost-management": "^0.0.4", "xlerr/cpop-income": "^0.1.9", "xlerr/datasource": "^4.5", "xlerr/desensitise": "^2.0", "xlerr/diy-report": "^0.0", "xlerr/fullcalendar": "^1.0", "xlerr/group": "^1.0", "xlerr/httpca": "^1.2", "xlerr/jasypt": "^1.0", "xlerr/kvmanager": "^4.13", "xlerr/lock": "^1.0", "xlerr/metric": "^0.0", "xlerr/mq": "^0.0", "xlerr/settlement-flow": "~3.7.0", "xlerr/task": "^2.0", "xlerr/yii2-adminlte": "~3.9.0", "xlerr/yii2-common-widgets": ">=1.18.5", "xlerr/yii2-widget-code-editor": "^1.1", "yiisoft/yii2": ">=2.0.50 <2.2.0", "yiisoft/yii2-bootstrap": "^2.0", "yiisoft/yii2-symfonymailer": "^2.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "symfony/var-dumper": "^5.2", "xlerr/yii2-gii-templates": "^2.0", "yiisoft/yii2-debug": "^2.1", "yiisoft/yii2-faker": "^2.0", "yiisoft/yii2-gii": "^2.2"}, "autoload": {"psr-4": {"api\\": "api/", "backend\\": "backend/", "common\\": "common/", "console\\": "console/", "dashboard\\": "modules/dashboard/", "repay\\": "modules/repay/", "grant\\": "modules/grant/", "payment\\": "modules/payment/", "contract\\": "modules/contract/", "dcs\\": "modules/dcs/", "capital\\": "modules/capital/", "account\\": "modules/account/", "system\\": "modules/system/", "EventManager\\": "modules/EventManager/", "cpm\\": "modules/cpm/", "gateway\\": "modules/gateway/"}, "files": ["common/config/functions.php", "common/config/bootstrap.php"]}, "config": {"secure-http": true, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "yiisoft/yii2-composer": true, "php-http/discovery": true}}, "scripts": {"task": "./yii dashboard-data-generate && ./yii task/process-all", "start-backend": "php -S 0.0.0.0:9900 -t backend/web", "start-api": "php -S 0.0.0.0:9901 -t api/web", "load-remote-config": "JAVA=/usr/bin/java vendor/bin/loadRemoteConfig"}, "repositories": [{"type": "composer", "url": "https://kuainiu-satis.k8s-ingress-nginx.kuainiujinke.com/composer/"}, {"type": "composer", "url": "https://asset-packagist.org"}]}