version: "3"

services:
  app:
    image: registry-prod.kuainiujinke.com/biz/biz-admin.base:beta4
    volumes:
      - ./:/data/www/wwwroot/biz-admin:cached
      - ./docker/nginx/:/data/nginx/conf:cached
      - ./docker/php/php.ini:/etc/php.ini:cached
      - ./docker/php/php-fpm.conf:/etc/php-fpm.conf:cached
      - ./docker/supervisor/supervisord.conf:/etc/supervisord.conf
    ports:
      - 80:80
      - 8080:8080
      - 443:443
    networks:
      - brnet1
  redis:
    image: redis
    command: redis-server --appendonly yes
    networks:
      - brnet1
networks:
  brnet1:
