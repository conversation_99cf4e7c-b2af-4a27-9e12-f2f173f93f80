var:
  - &expr
    - common\components\ExpressionComponent
    - magicColumn
title: vintage目标列表
db: db
searchConditions:
  channel:
    attribute: channel
    condition: in
    subCondition: and
    defaultValue: ''
    html:
      type: select
      args:
        pluginOptions:
          allowClear: true
          ajax:
            delay: 250
            url: /cpm/channel/ajax-channel
            dataType: json
            cache: true
        options:
          placeholder: 资金方
          class: form-control
  business_month:
    attribute: business_month
    condition: <=
    defaultValue: '202506'
    html:
      type: date
      args:
        pluginOptions:
          todayBtn: 'linked'
          format: 'yyyymm'
          todayHighlight: true
          autoclose: true
          minViewMode: 1
        options:
          placeholder: 业务月
          class: form-control
sort:
  attributes:
    - grant_month
    - channel
  defaultOrder:
    grant_month: asc
sql: >-
  SELECT channel,
         grant_month,
         max(business_month) as `business_month`,
         IFNULL(MAX(CASE WHEN mob = 1 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB1',
         IFNULL(MAX(CASE WHEN mob = 1 THEN business_month END),'')        AS 'MOB1_BM',
         IFNULL(MAX(CASE WHEN mob = 2 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB2',
         IFNULL(MAX(CASE WHEN mob = 2 THEN business_month END),'')        AS 'MOB2_BM',
         IFNULL(MAX(CASE WHEN mob = 3 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB3',
         IFNULL(MAX(CASE WHEN mob = 3 THEN business_month END),'')        AS 'MOB3_BM',
         IFNULL(MAX(CASE WHEN mob = 4 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB4',
         IFNULL(MAX(CASE WHEN mob = 4 THEN business_month END),'')        AS 'MOB4_BM',
         IFNULL(MAX(CASE WHEN mob = 5 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB5',
         IFNULL(MAX(CASE WHEN mob = 5 THEN business_month END),'')        AS 'MOB5_BM',
         IFNULL(MAX(CASE WHEN mob = 6 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB6',
         IFNULL(MAX(CASE WHEN mob = 6 THEN business_month END),'')        AS 'MOB6_BM',
         IFNULL(MAX(CASE WHEN mob = 7 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB7',
         IFNULL(MAX(CASE WHEN mob = 7 THEN business_month END),'')        AS 'MOB7_BM',
         IFNULL(MAX(CASE WHEN mob = 8 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB8',
         IFNULL(MAX(CASE WHEN mob = 8 THEN business_month END),'')        AS 'MOB8_BM',
         IFNULL(MAX(CASE WHEN mob = 9 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob))  as 'MOB9',
         IFNULL(MAX(CASE WHEN mob = 9 THEN business_month END),'')        AS 'MOB9_BM',
         IFNULL(MAX(CASE WHEN mob = 10 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB10',
         IFNULL(MAX(CASE WHEN mob = 10 THEN business_month END),'')        AS 'MOB10_BM',
         IFNULL(MAX(CASE WHEN mob = 11 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB11',
         IFNULL(MAX(CASE WHEN mob = 11 THEN business_month END),'')        AS 'MOB11_BM',
         IFNULL(MAX(CASE WHEN mob = 12 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB12',
         IFNULL(MAX(CASE WHEN mob = 12 THEN business_month END),'')        AS 'MOB12_BM',
         IFNULL(MAX(CASE WHEN mob = 13 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB13',
        IFNULL(MAX(CASE WHEN mob = 13 THEN business_month END),'')        AS 'MOB13_BM',
         IFNULL(MAX(CASE WHEN mob = 14 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB14',
         IFNULL(MAX(CASE WHEN mob = 14 THEN business_month END),'')        AS 'MOB14_BM',
         IFNULL(MAX(CASE WHEN mob = 15 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB15',
       IFNULL(MAX(CASE WHEN mob = 15 THEN business_month END),'')        AS 'MOB15_BM',
         IFNULL(MAX(CASE WHEN mob = 16 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB16',
        IFNULL(MAX(CASE WHEN mob = 16 THEN business_month END),'')        AS 'MOB16_BM',
         IFNULL(MAX(CASE WHEN mob = 17 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB17',
        IFNULL(MAX(CASE WHEN mob = 17 THEN business_month END),'')        AS 'MOB17_BM',
         IFNULL(MAX(CASE WHEN mob = 18 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB18',
        IFNULL(MAX(CASE WHEN mob = 18 THEN business_month END),'')        AS 'MOB18_BM',
         IFNULL(MAX(CASE WHEN mob = 19 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB19',
         IFNULL(MAX(CASE WHEN mob = 19 THEN business_month END),'')        AS 'MOB19_BM',
         IFNULL(MAX(CASE WHEN mob = 20 THEN CONCAT(round(vintage * 100,4),'-', mob) END), concat( '0.00','-',mob)) as 'MOB20',
       IFNULL(MAX(CASE WHEN mob = 20 THEN business_month END),'')        AS 'MOB20_BM'
  FROM vintage_rule
  WHERE :channel
  AND :business_month
  GROUP BY channel, grant_month;

columns:
  - label: 资金方
    attribute: channel

  - label: 放款月
    attribute: grant_month

  - label: MOB1
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB1"])[0] > 0 && row["MOB1_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB1"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB2
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB2"])[0] > 0  && row["MOB2_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB2"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB3
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB3"])[0] > 0 && row["MOB3_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB3"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB4
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB4"])[0] > 0 && row["MOB4_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB4"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB5
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB5"])[0] > 0  && row["MOB5_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB5"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB6
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB6"])[0] > 0 && row["MOB6_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB6"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB7
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB7"])[0] > 0 && row["MOB7_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB7"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB8
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB8"])[0] > 0 && row["MOB8_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB8"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw


  - label: MOB9
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB9"])[0] > 0 && row["MOB9_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB9"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB10
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB10"])[0] > 0  && row["MOB10_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB10"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

  - label: MOB11
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB11"])[0] > 0 && row["MOB11_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB11"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw


  - label: MOB12
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB12"])[0] > 0 && row["MOB12_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB12"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw


  - label: MOB13
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB13"])[0] > 0 && row["MOB13_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB13"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw


  - label: MOB14
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB14"])[0] > 0  && row["MOB14_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB14"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw


  - label: MOB15
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB15"])[0] > 0 && row["MOB15_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB15"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw


  - label: MOB16
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB16"])[0] > 0  && row["MOB16_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB16"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw



  - label: MOB17
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB17"])[0] > 0  && row["MOB17_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB17"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw



  - label: MOB18
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB18"])[0] > 0 && row["MOB18_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB18"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw


  - label: MOB19
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB19"])[0] > 0  && row["MOB19_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB19"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw



  - label: MOB20
    attribute: ' "<span style=\"" ~ (explode("-",row["MOB20"])[0] > 0  && row["MOB20_BM"] == row["business_month"]  ? "background-color: #ffecec; color: #d8000c" : "") ~  "\">" ~ explode("-",row["MOB20"])[0] ~ "%" ~ "</span>" '
    value: *expr
    format: raw

